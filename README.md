# Rabbit Seat App - 智能儿童安全座椅应用

## 项目概述

Rabbit Seat App 是一款基于 Flutter 开发的智能儿童安全座椅管理应用，集成了涂鸦智能(Tuya)物联网平台，为用户提供设备管理、实时监控、安全预警等功能。

## 架构设计

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Views     │  │   Widgets   │  │   Routes    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Business Layer                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Providers  │  │  Services   │  │   Models    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Data Layer                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    HTTP     │  │ Local Store │  │  Tuya SDK   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈
- **前端框架**: Flutter 3.16.0
- **状态管理**: Provider
- **网络请求**: Dio
- **本地存储**: SharedPreferences
- **地图服务**: 高德地图 (AMap)
- **物联网平台**: 涂鸦智能 (Tuya Smart)
- **图片处理**: Image Picker, Image Cropper
- **UI组件**: Material Design + Cupertino

### 目录结构
```
lib/
├── business.dart              # 业务逻辑核心类
├── main.dart                 # 应用入口
├── common/                   # 公共组件
├── controls/                 # 自定义控件
├── events/                   # 事件总线
├── models/                   # 数据模型
│   ├── device/              # 设备相关模型
│   ├── user/                # 用户相关模型
│   ├── circle/              # 社区相关模型
│   └── tuya/                # 涂鸦SDK模型
├── provider/                 # 状态管理
├── routes/                   # 路由配置
├── services/                 # 业务服务层
├── utils/                    # 工具类
├── views/                    # 页面视图
└── widgets/                  # 可复用组件
```

## 数据结构

### 核心数据模型

#### 设备模型 (DeviceModel)
```dart
class DeviceModel {
  String? id;                    // 设备ID
  String? name;                  // 设备名称
  String? model;                 // 设备型号
  bool online;                   // 在线状态
  String? ownerId;               // 所有者ID
  String? productId;             // 产品ID
  String? statusJson;            // 状态JSON
  String? lat;                   // 纬度
  String? lon;                   // 经度
  num homeId;                    // 家庭ID
  UserModel? user;               // 用户信息
  
  // 设备状态
  bool isOnline;                 // 是否在线
  bool isHighTemp;               // 高温预警
  bool isRemote;                 // 远程模式
  bool isWind;                   // 通风状态
  bool isHost;                   // 加热状态
  bool isAuto;                   // 自动模式
  bool isLeft;                   // 左侧保护
  bool isRight;                  // 右侧保护
  bool isLamp;                   // 氛围灯
  String? temp;                  // 温度
  int energy;                    // 电量
  bool leaveWarm;                // 离开预警
}
```

#### 用户模型 (UserModel)
```dart
class UserModel {
  String? id;                    // 用户ID
  String? name;                  // 用户姓名
  String? tuyaId;                // 涂鸦用户ID
  String? avatar;                // 头像
  num sex;                       // 性别
  String? nickName;              // 昵称
  String? city;                  // 城市
  String? telephone;             // 电话
  List<BabyModel>? babyInfos;    // 宝宝信息
  List<ContactsModel>? emergencyContacts; // 紧急联系人
  String? openId;                // 微信OpenID
}
```

#### 涂鸦家庭模型 (TuyaHome)
```dart
class TuyaHome {
  num? homeId;                   // 家庭ID
  String? name;                  // 家庭名称
  double? lat;                   // 纬度
  double? lon;                   // 经度
  String? geoName;               // 地理位置名称
}
```

## 主要接口功能

### 用户管理接口
- `GET /api/appuser/user/{id}` - 获取用户信息
- `PUT /api/appuser/{id}` - 更新用户信息
- `POST /api/appuser/babyinfo/{userId}` - 添加宝宝信息
- `PUT /api/appuser/babyinfo/{userId}` - 修改宝宝信息
- `DELETE /api/appuser/babyinfo/{userId}` - 删除宝宝信息
- `POST /api/appuser/emergencyContact/{userId}` - 添加紧急联系人

### 设备管理接口
- `GET /api/device/appList` - 获取设备列表
- `GET /api/device/{id}` - 获取设备详情
- `GET /api/device/home` - 获取家庭信息
- `POST /api/device/controll/{deviceId}` - 设备控制
- `POST /api/device/unbundling/{deviceId}` - 解绑设备
- `PUT /api/device/config` - 修改设备配置
- `GET /api/device/sim/{iccid}` - 获取SIM卡信息

### 设备共享接口
- `POST /api/deviceShare/` - 添加设备共享
- `PUT /api/deviceShare/{id}` - 修改共享设置
- `GET /deviceShare/list/{homeId}` - 获取共享列表
- `GET /api/deviceShare/{id}` - 获取共享详情
- `POST /api/deviceShare/accept/{id}` - 接受设备共享
- `DELETE /api/deviceShare/{id}` - 撤销设备共享

### 其他接口
- `GET /api/banner/applist` - 获取横幅广告
- `GET /api/ticket/*` - 工单系统相关
- `GET /api/help/*` - 帮助中心相关
- `GET /api/notice/*` - 消息通知相关

## Tuya SDK 接入及使用

### SDK 初始化
```dart
// 初始化SDK
await Tuya.startInitSdk();

// 注册推送设备
await Tuya.registerPushDevice();
```

### 用户认证
```dart
// 手机号登录
String? result = await Tuya.login(phone, password);

// UID登录
String? result = await Tuya.LoginById(uid);
```

### 家庭管理
```dart
// 获取家庭列表
String? homeList = await Tuya.getHomeList;

// 创建家庭
String? result = await Tuya.createHome(name);

// 获取家庭详情
String? homeDetail = await Tuya.getHomeDetail(homeId);
```

### 设备配网
```dart
// 开始扫描设备
String? result = await Tuya.StartScanDevice;

// 停止扫描设备
bool? result = await Tuya.stopScanDevice;

// 二维码配网
String? result = await Tuya.activeDeviceScanQrcode(homeId, qrcode);

// 蓝牙配网
String? result = await Tuya.activeDevice(homeId, uuid, findScanDeviceBean);

// 蓝牙单点配网
String? result = await Tuya.startActivator(homeId, uuid, deviceType);

// 取消配网
bool result = await Tuya.stopActivator(homeId, uuid, deviceType);
```

### 设备管理
```dart
// 重置设备
bool? result = await Tuya.resetFactory(uuid);

// 删除设备
bool? result = await Tuya.removeDevice(uuid);
```

### 固件升级
```dart
// 获取升级信息
String? upgradeInfo = await Tuya.getUpgradeInfo(deviceId);

// 开始升级
String? result = await Tuya.startUpgrade(deviceId, type);
```

## Tuya SDK 完成功能列表

### ✅ 已实现功能

#### 用户管理
- [x] 手机号密码登录
- [x] UID登录
- [x] 用户状态管理

#### 家庭管理
- [x] 获取家庭列表
- [x] 创建家庭
- [x] 获取家庭详情
- [x] 家庭地理位置设置

#### 设备发现与配网
- [x] 设备扫描 (蓝牙/WiFi)
- [x] 二维码配网
- [x] 蓝牙配网
- [x] 蓝牙单点配网
- [x] 配网状态监听
- [x] 取消配网操作

#### 设备管理
- [x] 设备列表获取
- [x] 设备状态查询
- [x] 设备控制指令发送
- [x] 设备重置
- [x] 设备删除/解绑

#### 固件管理
- [x] 固件版本检查
- [x] 固件升级
- [x] 升级进度监听

#### 推送服务
- [x] 设备推送注册
- [x] 消息推送接收

### 🔄 平台差异处理

#### Android 平台
- 完整的设备扫描功能
- 蓝牙配网支持
- 推送服务集成

#### iOS 平台
- 设备扫描功能
- 蓝牙配网支持
- 推送服务集成
- 返回值统一处理 (字符串 "1" 表示成功)

### 📱 设备控制功能

#### 安全座椅控制指令
```dart
// 发送设备控制指令
await DeviceService.sendCommand(
  deviceId: deviceId,
  code: 'protectionLeftSw',  // 左侧保护开关
  value: true,
);

// 支持的控制指令
- protectionLeftSw: 左侧保护开关
- protectionRightSw: 右侧保护开关  
- F_light: 氛围灯控制
- fanSw: 通风开关
- hotSw: 加热开关
- autoMode: 自动模式
```

#### 远程控制
```dart
// 发送远程关闭指令
await DeviceService.sendRemoteInstruction(deviceId);
```

### 🔧 配置要求

#### Android 配置
- 最小SDK版本: API 21
- 蓝牙权限配置
- 网络权限配置
- 位置权限配置

#### iOS 配置  
- 最小版本: iOS 12.0
- 蓝牙权限配置
- 网络权限配置
- 位置权限配置

### 📋 使用注意事项

1. **初始化顺序**: 必须先调用 `startInitSdk()` 再进行其他操作
2. **登录状态**: 所有设备操作需要用户登录状态
3. **权限申请**: 配网功能需要蓝牙、位置等权限
4. **错误处理**: 所有异步操作都需要适当的错误处理
5. **平台差异**: iOS和Android返回值格式可能不同，需要统一处理

## 开发环境配置

### 环境要求
- Flutter SDK: 3.16.0+
- Dart SDK: 3.3.0+
- Android Studio / VS Code
- Xcode (iOS开发)

### 依赖安装
```bash
flutter pub get
```

### 运行项目
```bash
# 调试模式
flutter run

# 发布模式
flutter run --release

# 构建APK
flutter build apk --no-shrink
```

### 配置说明
1. 配置高德地图API Key
2. 配置涂鸦智能App Key和Secret
3. 配置阿里云OSS访问密钥
4. 配置微信SDK参数

## 项目特色功能

- 🔐 **智能安全监控**: 实时监控座椅状态，温度异常预警
- 📱 **远程控制**: 支持远程开启通风、加热等功能
- 👨‍👩‍👧‍👦 **家庭共享**: 设备共享给家庭成员使用
- 🗺️ **位置服务**: 集成高德地图，实时定位功能
- 🔔 **消息推送**: 及时推送设备状态和安全提醒
- 👶 **宝宝档案**: 管理宝宝信息和成长记录
- 🆘 **紧急联系**: 一键联系紧急联系人功能

## 版本信息

- **当前版本**: 1.2.44
- **Flutter版本**: 3.16.0
- **最后更新**: 2024年

## 许可证

本项目为私有项目，未开源发布。

---

*该文档持续更新中，如有疑问请联系开发团队。*