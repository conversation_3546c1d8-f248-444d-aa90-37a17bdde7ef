name: Publish to Pub.dev

on:
  push:
  pull_request:
    branches:
      - main


jobs:
  publishing:
    runs-on: ubuntu-latest
    steps:
      - name: 'Checkout'
        uses: actions/checkout@main # required!
        
      - name: '>> Dart package <<'
        id: package_publisher
        uses: k-paxian/dart-package-publisher@master
        with:
          accessToken: ${{ secrets.OAUTH_ACCESS_TOKEN }}
          refreshToken: ${{ secrets.OAUTH_REFRESH_TOKEN }}
          skipTests: true
          flutter: true
          force: true

      - name: Get Commit Logs
        id: git_log
        run: |
          TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
          if [ -z "$TAG" ]; then
            GIT_LOG=$(git log --pretty=format:'- %s')
          else
            GIT_LOG=$(git log --pretty=format:'- %s' $TAG..HEAD)
          fi
          echo "git_log=$GIT_LOG" >> $GITHUB_ENV

      - name: Create Release
        if: steps.package_publisher.outputs.success == 'true'
        uses: kuloud/Github-Release-Action@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.package_publisher.outputs.localVersion }}
          title: Release ${{ steps.package_publisher.outputs.localVersion }}
          body: |
            Automated release for version ${{ steps.package_publisher.outputs.localVersion }}\n
            Changes in this release:\n
            ${{ env.git_log }}
          draft: false
          prerelease: false