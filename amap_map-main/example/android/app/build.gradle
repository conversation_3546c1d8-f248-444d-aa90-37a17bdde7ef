plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}


def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}


def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}


android {
    namespace = "com.amap.flutter.amap_map_example"
    compileSdkVersion flutter.compileSdkVersion

    lint {
        disable 'InvalidPackage'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.amap.flutter.amap_map_example"
        minSdkVersion flutter.minSdkVersion
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }
    signingConfigs {
        debug {
            //keystore中key的别名
            keyAlias 'androiddebugkey'
            //keystore中key的密码
            keyPassword 'android'
            //keystore的文件路径，可以是绝对路径也可以是相对路径
            storeFile file('../debug.keystore')
            //keystore的密码l
            storePassword 'android'
        }
    }
    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug


            //关闭混淆, 否则在运行release包后可能出现运行崩溃， TODO后续进行混淆配置
            minifyEnabled false //删除无用代码
            shrinkResources false //删除无用资源
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    ndkVersion '27.0.12077973'
}
dependencies {
}

flutter {
    source '../..'
}
