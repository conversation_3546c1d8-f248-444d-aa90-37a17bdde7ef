name: amap_map_example
description: Demonstrates how to use the amap_map plugin.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

environment:
  sdk: ">=3.1.5 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  permission_handler: ^11.3.1

  amap_map:
    path: ../
  x_common: ^1.0.4
  x_amap_base: ^1.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
