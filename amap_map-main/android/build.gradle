group 'com.amap.flutter.map'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.4'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    namespace = "com.amap.flutter.map"
    compileSdkVersion 35

    defaultConfig {
        minSdkVersion 21
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
    implementation 'com.amap.api:3dmap:9.2.0'
    implementation 'androidx.annotation:annotation:1.9.1'
}

