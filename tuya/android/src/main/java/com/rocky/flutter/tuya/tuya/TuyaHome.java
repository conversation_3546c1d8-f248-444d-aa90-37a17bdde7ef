package com.rocky.flutter.tuya.tuya;

import com.alibaba.fastjson.JSON;
import com.thingclips.smart.home.sdk.ThingHomeSdk;
import com.thingclips.smart.home.sdk.bean.HomeBean;
import com.thingclips.smart.home.sdk.callback.IThingHomeResultCallback;

import org.json.JSONArray;

import java.util.ArrayList;
import java.util.List;

import io.flutter.plugin.common.MethodChannel;

public class TuyaHome {
    public static void getHomeList(MethodChannel.Result result){
        ThingHomeSdk.getHomeManagerInstance().queryHomeList(new com.thingclips.smart.home.sdk.callback.IThingGetHomeListCallback() {
            @Override
            public void onSuccess(List<HomeBean> homeBeans) {
                // do something
                String home = JSON.toJSONString(homeBeans);
                result.success(home);
            }
            @Override
            public void onError(String errorCode, String error) {
                // do something
                result.error(errorCode, error, null);
            }
        });
    }

    public static void getHomeDetail(Integer homeId,MethodChannel.Result result){
        ThingHomeSdk.newHomeInstance(homeId).getHomeDetail(new IThingHomeResultCallback() {
            @Override
            public void onSuccess(HomeBean bean) {
                String home = JSON.toJSONString(bean);
                result.success(home);
            }
            @Override
            public void onError(String errorCode, String errorMsg) {
                result.error(errorCode, errorMsg, null);
            }
        });

    }

    public static void createHome(String name,MethodChannel.Result result){
        ThingHomeSdk.getHomeManagerInstance().createHome(name, 120, 30, "默认位置", new ArrayList<>(), new IThingHomeResultCallback() {
            @Override
            public void onSuccess(HomeBean bean) {
                // do something
                result.success(JSON.toJSONString(bean));
            }
            @Override
            public void onError(String errorCode, String errorMsg) {
                // do something
                result.error(errorCode, errorMsg, null);
            }
        });

    }
}
