package com.rocky.flutter.tuya.tuya;

import android.app.Application;

import androidx.annotation.NonNull;

import com.alibaba.fastjson.JSON;
import com.thingclips.smart.android.device.bean.UpgradeInfoBean;
import com.thingclips.smart.home.sdk.ThingHomeSdk;
import com.thingclips.smart.sdk.api.IGetOtaInfoCallback;
import com.thingclips.smart.sdk.api.IOtaListener;
import com.thingclips.smart.sdk.api.IThingOta;
import com.thingclips.smart.sdk.bean.OTAErrorMessageBean;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import io.flutter.plugin.common.MethodChannel;


public class TuyaFirmware {

    // 初始化固件信息
    private static void initFirmware(String deviceId, MethodChannel.Result result) {
        ThingHomeSdk.newOTAInstance(deviceId);
        result.success(true);
    }

    // 查询固件升级信息
    private static void getFirmwareInfo(String deviceId, MethodChannel.Result result) {
        ThingHomeSdk.newOTAInstance(deviceId).getOtaInfo(new IGetOtaInfoCallback(){

            @Override
            public void onSuccess(List<UpgradeInfoBean> upgradeInfoBeans) {
                String str = JSON.toJSONString(upgradeInfoBeans);
                result.success(str);
            }

            @Override
            public void onFailure(String code, String error) {
                result.error(code, error, null);
            }

        });
    }

    // 添加固件升级监听
    private static void setUpdateListener(String deviceId, MethodChannel.Result result) {
        ThingHomeSdk.newOTAInstance(deviceId).setOtaListener(new IOtaListener() {
            @Override
            public void onSuccess(int otaType) {
                result.success(100);
            }

            @Override
            public void onFailure(int otaType, String code, String error) {
                result.error(code, error, null);
            }

            @Override
            public void onFailureWithText(int otaType, String code, OTAErrorMessageBean messageBean) {
                result.error(code, messageBean.text, null);
            }

            @Override
            public void onProgress(int otaType, int progress) {
                result.success(progress);
            }

            @Override
            public void onTimeout(int otaType) {
                result.error("400", "链接超时", null);
            }

            @Override
            public void onStatusChanged(int otaStatus, int otaType) {
                // 3.23.0 版本新增，针对于低功耗类设备会通过该方法返回设备等待唤醒状态。此时 otaStatus 参数返回 5
            }
        });
    }

    // 升级固件
    private static void startUpgrading(String deviceId, MethodChannel.Result result) {
        ThingHomeSdk.newOTAInstance(deviceId).startOta();
        result.success(true);
    }

    // 释放资源
    private static void destory(String deviceId, MethodChannel.Result result) {
        ThingHomeSdk.newOTAInstance(deviceId).onDestroy();
        result.success(true);
    }

    // 升级固件
    private static  void upgrade(String deviceId, MethodChannel.Result result) {

        IThingOta iTuyaOta = ThingHomeSdk.newOTAInstance(deviceId);
        // 设置监听
        iTuyaOta.setOtaListener(new IOtaListener() {
            @Override
            public void onSuccess(int otaType) {
                result.success(100);
                // 释放资源
                iTuyaOta.onDestroy();
            }

            @Override
            public void onFailure(int otaType, String code, String error) {
                result.error(code, error, null);
            }

            @Override
            public void onFailureWithText(int otaType, String code, OTAErrorMessageBean messageBean) {
                result.error(code, messageBean.text, null);
            }

            @Override
            public void onProgress(int otaType, int progress) {
                result.success(progress);
            }

            @Override
            public void onTimeout(int otaType) {
                result.error("400", "链接超时", null);
            }

            @Override
            public void onStatusChanged(int otaStatus, int otaType) {
                // 3.23.0 版本新增，针对于低功耗类设备会通过该方法返回设备等待唤醒状态。此时 otaStatus 参数返回 5
            }
        });
        // 启动升级
        iTuyaOta.startOta();
    }

}