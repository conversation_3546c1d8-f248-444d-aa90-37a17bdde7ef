package com.rocky.flutter.tuya.tuya;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.util.Log;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.alibaba.fastjson.JSON;
import com.thingclips.smart.android.ble.api.LeScanSetting;
import com.thingclips.smart.android.ble.api.ScanDeviceBean;
import com.thingclips.smart.android.ble.api.ScanType;
import com.thingclips.smart.home.sdk.ThingHomeSdk;
import com.thingclips.smart.home.sdk.builder.ThingQRCodeActivatorBuilder;
import com.thingclips.smart.scene.api.IResultCallback;
import com.thingclips.smart.sdk.api.IThingDevice;
import com.thingclips.smart.sdk.api.*;
import com.thingclips.smart.sdk.bean.BleActivatorBean;
import com.thingclips.smart.sdk.bean.DeviceBean;
import com.thingclips.smart.sdk.bean.MultiModeActivatorBean;
import com.thingclips.smart.sdk.bean.QrScanBean;

import java.util.TimeZone;

import io.flutter.plugin.common.MethodChannel;

public class TuyaDevice {
    // You need to check permissions before using Bluetooth devices

    public static void StartScanDevice(MethodChannel.Result result) throws Exception {
        // Scan Single Ble Device
        LeScanSetting scanSetting = new LeScanSetting.Builder()
                .setTimeout(60 * 1000) // Timeout：ms
                .addScanType(ScanType.SINGLE) // If you need to scan for BLE devices, you only need to add ScanType.SINGLE
                .addScanType(ScanType.SINGLE_QR)
                .build();

        // start scan
        ThingHomeSdk.getBleOperator().startLeScan(scanSetting, bean -> {
            System.out.println("------------------------------bean.getProductId()：" + bean.getProductId());
            if (bean.getProductId().equals("4vyzrxvibcxaalgt")) {
                result.success(JSON.toJSONString(bean));
            }
        });
    }

    public static void stopScanDevice(MethodChannel.Result result) {
        ThingHomeSdk.getBleOperator().stopLeScan();
        result.success(true);
    }

    public static void activeDevice(long homeId, String uuid, MethodChannel.Result result) throws Exception {
        Application mApplication = (Application) Class.forName("android.app.ActivityThread").getMethod("currentApplication").invoke(null, (Object[]) null);
        ThingQRCodeActivatorBuilder builder = new ThingQRCodeActivatorBuilder()
                .setUuid(uuid)
                .setHomeId(homeId)
                .setContext(mApplication.getApplicationContext())
                .setTimeOut(30000)
                .setListener(new IThingSmartActivatorListener() {

                                 @Override
                                 public void onError(String errorCode, String errorMsg) {
                                     System.out.println(">>> code:" + errorCode + "  " + "msg:" + errorMsg);
                                     result.error(errorCode, errorMsg, null);
                                 }

                                 @Override
                                 public void onActiveSuccess(DeviceBean devResp) {
                                     result.success(JSON.toJSONString(devResp));
                                 }

                                 @Override
                                 public void onStep(String step, Object data) {
                                     System.out.println(step);
                                 }
                             }
                );

        IThingActivator mTuyaActivator = ThingHomeSdk.getActivatorInstance().newQRCodeDevActivator(builder);

        //开始配网
        mTuyaActivator.start();
    }

    public static void resetFactory(String deviceId, MethodChannel.Result result) {
        IThingDevice mDevice = ThingHomeSdk.newDeviceInstance(deviceId);
        mDevice.resetFactory(new com.thingclips.smart.sdk.api.IResultCallback() {
            @Override
            public void onError(String errorCode, String errorMsg) {
                result.error(errorCode, errorMsg, null);
            }

            @Override
            public void onSuccess() {
                result.success(true);
            }
        });
    }

    public static void removeDevice(String deviceId, MethodChannel.Result result) {
        IThingDevice mDevice = ThingHomeSdk.newDeviceInstance(deviceId);
        mDevice.removeDevice(new com.thingclips.smart.sdk.api.IResultCallback() {
            @Override
            public void onError(String errorCode, String errorMsg) {
                result.error(errorCode, errorMsg, null);
            }

            @Override
            public void onSuccess() {
                result.success(true);
            }
        });
    }

    /// 蓝牙单点配网
    public static void startActivator(long homeId, String uuid, int deviceType, MethodChannel.Result result) {

        /// 蓝牙配网参数bean
        BleActivatorBean bleActivatorBean = new BleActivatorBean();
        bleActivatorBean.homeId = homeId; // homeId
        bleActivatorBean.deviceType = deviceType; // 设备类型
        bleActivatorBean.uuid = uuid; // UUID

        /// 开始配网
        ThingHomeSdk.getActivator().newBleActivator().startActivator(bleActivatorBean, new IBleActivatorListener() {
            @Override
            public void onSuccess(DeviceBean deviceBean) {
                /// 配网成功
                result.success(JSON.toJSONString(deviceBean));
            }

            @Override
            public void onFailure(int code, String msg, Object handle) {
                /// 配网失败
                result.error("" + code, msg, null);
            }
        });

    }

    public static void startMultiModeActivator(long homeId, ScanDeviceBean mScanDeviceBean, MethodChannel.Result result) {
        ThingHomeSdk.getActivatorInstance().getActivatorToken(homeId,
                new IThingActivatorGetToken() {

                    @Override
                    public void onSuccess(String token) {
                        MultiModeActivatorBean multiModeActivatorBean = new MultiModeActivatorBean();

// mScanDeviceBean 来自于扫描回调的 ScanDeviceBean
                        multiModeActivatorBean.deviceType = mScanDeviceBean.getDeviceType(); // 设备类型
                        multiModeActivatorBean.uuid = mScanDeviceBean.getUuid(); // 设备 uuid
                        multiModeActivatorBean.address = mScanDeviceBean.getAddress(); // 设备地址
                        multiModeActivatorBean.mac = mScanDeviceBean.getMac(); // 设备 mac
                        multiModeActivatorBean.ssid = ""; // Wi-Fi SSID
                        multiModeActivatorBean.pwd = ""; // Wi-Fi 密码
                        multiModeActivatorBean.token = token; // 获取的 Token
                        multiModeActivatorBean.homeId = homeId; // 当前家庭 homeId
                        multiModeActivatorBean.timeout = 120000; // 超时时间
// 开始配网
                        ThingHomeSdk.getActivator().newMultiModeActivator().startActivator(multiModeActivatorBean, new IMultiModeActivatorListener() {
                            @Override
                            public void onSuccess(DeviceBean deviceBean) {
                                // 配网成功
                                result.success(JSON.toJSONString(deviceBean));
                            }

                            @Override
                            public void onFailure(int code, String msg, Object handle) {
                                // 配网失败
                                result.error("" + code, msg, null);
                            }
                        });
                    }

                    @Override
                    public void onFailure(String s, String s1) {
                        result.error("" + s, s1, null);
                    }
                });


    }

    /// 蓝牙单点取消配网
    public static void stopActivator(String uuid, MethodChannel.Result result) {
        ThingHomeSdk.getActivator().newBleActivator().stopActivator(uuid);
        result.success(true);
    }

    public static String getTimeZone() {
        TimeZone tz = TimeZone.getDefault();
        String displayName = "+08:00";
        if (tz != null) {
            String str = tz.getDisplayName();
            if (str != null && !str.isEmpty()) {
                int indexOf = str.indexOf("+");
                if (indexOf == -1) {
                    indexOf = str.indexOf("-");
                }
                if (indexOf != -1) {
                    displayName = str.substring(indexOf);
                }
                if (!displayName.contains(":")) {
                    displayName = displayName.substring(0, 3) + ":" + displayName.substring(3);
                }
            }
        }
        return displayName;
    }

    private static String TAG = "TuyaDevice";

    public static void scanQrcode(String qrcode, long homeId, MethodChannel.Result _result) {
        //二维码扫码得到的 URL
        ThingHomeSdk.getActivatorInstance().deviceQrCodeParse(qrcode, new IThingDataCallback<QrScanBean>() {
            @Override
            public void onSuccess(QrScanBean result) {
                if (result != null) {

                    String uuid = ((com.alibaba.fastjson.JSONObject) result.getActionData()).get("uuid").toString();
                    Log.i(TAG, "uuid:" + uuid);
                    Log.i(TAG, "homeId:" + homeId);

                    try {
                        activeDevice(homeId, uuid, _result);
                    } catch (Exception e) {
                        Log.e(TAG, "scanQrcode activeDevice",e);
                    }


                    //取出 result.actionData 中的 UUID 用于后续配网入参
                    //二维码扫码得到的 URL
//                    ThingHomeSdk.getActivatorInstance().bindNbDeviceWithQRCode(homeId, uuid, getTimeZone(), new IThingDevActivatorListener() {
//
//                        @Override
//                        public void onError(String errorCode, String errorMessage) {
//                            Log.i(TAG, "onError:" + errorCode + errorMessage);
//                        }
//
//                        @Override
//                        public void onActiveSuccess(DeviceBean devResp) {
//                            Log.i(TAG, "onActiveSuccess:" + devResp.devId);
//                            _result.success(JSON.toJSONString(devResp));
//                        }
//                    });
                }
            }

            @Override
            public void onError(String errorCode, String errorMessage) {
                //errorCode:QR_PROTOCOL_NOT_RECOGNIZED 协议不可识别
                _result.error(errorCode, errorMessage, null);

            }
        });


    }
}
