package com.rocky.flutter.tuya.tuya;

import androidx.annotation.NonNull;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.EventChannel;

public class FlutterEventChannel
  implements FlutterPlugin, EventChannel.StreamHandler {

  public static final String CHANNEL_NAME = "EventChannel";
  private EventChannel eventChannel;
  private EventChannel.EventSink eventSink;

  private static volatile FlutterEventChannel instance;

  public static FlutterActivity activity;

  private FlutterEventChannel() {}

  public static FlutterEventChannel getInstance() {
    if (instance == null) {
      synchronized (FlutterEventChannel.class) {
        if (instance == null) {
          instance = new FlutterEventChannel();
        }
      }
    }
    return instance;
  }

  /**
   * 处理设置事件流的请求。
   *
   * @param arguments 流配置参数，可能为空。
   * @param events    用于向flutter接收器发射事件。
   */
  @Override
  public void onListen(Object arguments, EventChannel.EventSink events) {
    this.eventSink = events;
  }

  /**
   * 处理请求以删除最近创建的事件流。
   *
   * @param arguments 流配置参数，可能为空。
   */
  @Override
  public void onCancel(Object arguments) {}

  public void sendEventData(Object data) {
    if (eventSink != null) {
      activity.runOnUiThread(() -> {
        eventSink.success(data);
      });
    }
  }

  @Override
  public void onAttachedToEngine(
    @NonNull FlutterPlugin.FlutterPluginBinding binding
  ) {
    eventChannel = new EventChannel(binding.getBinaryMessenger(), CHANNEL_NAME);
    eventChannel.setStreamHandler(this);
  }

  @Override
  public void onDetachedFromEngine(
    @NonNull FlutterPlugin.FlutterPluginBinding binding
  ) {
    if (eventSink != null) {
      eventSink.endOfStream();
    }
    eventChannel.setStreamHandler(null);
  }
}
