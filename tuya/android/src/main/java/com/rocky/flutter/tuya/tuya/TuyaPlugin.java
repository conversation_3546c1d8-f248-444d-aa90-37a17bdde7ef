package com.rocky.flutter.tuya.tuya;

import androidx.annotation.NonNull;


import com.alibaba.fastjson.JSON;
import com.thingclips.smart.android.ble.api.ScanDeviceBean;

import java.lang.reflect.Type;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

/**
 * TuyaPlugin
 */
public class TuyaPlugin implements FlutterPlugin, MethodCallHandler {
    /// The MethodChannel that will the communication between Flutter and native Android
    ///
    /// This local reference serves to register the plugin with the Flutter Engine and unregister it
    /// when the Flutter Engine is detached from the Activity
    private MethodChannel channel;

    private boolean isInit = false;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "tuya");
        channel.setMethodCallHandler(this);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
//    if(!isInit){
////      try {
////        // TuyaAccount.init();
////      } catch (ClassNotFoundException e) {
////        e.printStackTrace();
////      } catch (NoSuchMethodException e) {
////        e.printStackTrace();
////      } catch (InvocationTargetException e) {
////        e.printStackTrace();
////      } catch (IllegalAccessException e) {
////        e.printStackTrace();
////      }
//      isInit = true;
//    }
        try {
            switch (call.method) {
                case "getPlatformVersion":
                    result.success("Android " + android.os.Build.VERSION.RELEASE);
                    break;
                case "testInfo":
                    result.success("test info");
                    break;
                case "Login":
                    String phone = call.argument("phone");
                    String password = call.argument("password");
                    TuyaAccount.Login(phone, password, result);
                    break;
                case "LoginById": {
                    String uid = call.argument("uid");
                    TuyaAccount.LoginById(uid, "Zhou939436", false, result);
                    break;
                }
                case "getHomeList":
                    TuyaHome.getHomeList(result);
                    break;
                case "createHome":
                    String name = call.argument("name");
                    TuyaHome.createHome(name, result);
                    break;
                case "getHomeDetail":
                    Integer id = call.argument("id");
                    TuyaHome.getHomeDetail(id, result);
                    break;
                case "StartScanDevice":
                    TuyaDevice.StartScanDevice(result);
                    break;
                case "stopScanDevice":
                    TuyaDevice.stopScanDevice(result);
                    break;
                case "activeDevice": {
                    Integer homeId = call.argument("homeId");
                    String uuid = call.argument("uuid");
                    String findScanDeviceBeanString = call.argument("findScanDeviceBean");
                    ScanDeviceBean findScanDeviceBean = JSON.parseObject(findScanDeviceBeanString, (Type) ScanDeviceBean.class);
                    TuyaDevice.startMultiModeActivator(homeId, findScanDeviceBean, result);
                    break;
                }
                case "resetFactory": {
                    String uuid = call.argument("uuid");
                    TuyaDevice.resetFactory(uuid, result);
                    break;
                }
                case "removeDevice": {
                    String uuid = call.argument("uuid");
                    TuyaDevice.removeDevice(uuid, result);
                    break;
                }
                case "startActivator": {
                    Integer homeId = call.argument("homeId");
                    String uuid = call.argument("uuid");
                    Integer type = call.argument("type");
                    TuyaDevice.startActivator(homeId, uuid, type, result);
                    break;
                }
                case "stopActivator": {
                    String uuid = call.argument("uuid");
                    TuyaDevice.stopActivator(uuid, result);
                    break;
                }
//        case "getUpgradeInfo": {
//          String deviceId = call.argument("id");
//          TuyaFirmware.getFirmwareInfo(deviceId, result);
//          break;;
//        }
//        case "startUpgrade": {
//          String deviceId = call.argument("id");
//          TuyaFirmware.upgrade(deviceId, result);
//          break;
//        }
                case "registerPushDevice": {
                    TuyaAccount.registerPushDevice();
                    break;
                }
                case "scanQrcode": {
                    String qrcode = call.argument("qrcode");
                    Integer homeId = call.argument("homeId");
                    TuyaDevice.scanQrcode(qrcode, homeId, result);
                }
                break;
                default:
                    result.notImplemented();
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
    }
}
