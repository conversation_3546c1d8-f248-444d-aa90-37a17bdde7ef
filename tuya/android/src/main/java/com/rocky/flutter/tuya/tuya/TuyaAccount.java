package com.rocky.flutter.tuya.tuya;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;

import com.alibaba.fastjson.JSON;
import com.thingclips.smart.android.user.bean.User;
import com.thingclips.smart.home.sdk.ThingHomeSdk;

import java.lang.reflect.InvocationTargetException;

import io.flutter.plugin.common.MethodChannel;

public class TuyaAccount {

    public static String mRegId;

    private static String PUSH_PROVIDER_MI = "mi";

//    public static void init() throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
//        Application mApplication = (Application) Class.forName("android.app.ActivityThread").getMethod("currentApplication").invoke(null, (Object[]) null);
//
////        // 注册 regId 到涂鸦，涂鸦使用注册的 regId 进行推送
////        TuyaHomeSdk.getPushInstance()
////                .registerDevice(mRegId, PUSH_PROVIDER_MI, new IResultCallback() {
////                    @Override
////                    public void onError(String code, String error) {
////
////                    }
////
////                    @Override
////                    public void onSuccess() {
////
////                    }
////                });
//    }

    public static void registerPushDevice() {
        ThingHomeSdk.getPushInstance()
                .registerDevice(mRegId, PUSH_PROVIDER_MI, new com.thingclips.smart.sdk.api.IResultCallback() {
                    @Override
                    public void onError(String code, String error) {
                        System.out.println("registerPushDevice error");
                    }

                    @Override
                    public void onSuccess() {
                        System.out.println("registerPushDevice onSuccess:"+mRegId);
                    }
                });
    }

    public static void Login(String phone, String password, MethodChannel.Result result) {
        //手机密码登录
        ThingHomeSdk.getUserInstance().loginWithPhonePassword("86", phone, password, new com.thingclips.smart.android.user.api.ILoginCallback() {
            @Override
            public void onSuccess(User user) {
                String str = JSON.toJSONString(user);
                result.success(str);
            }

            @Override
            public void onError(String code, String error) {
                result.error(code, error, null);
            }
        });
    }

    public static void LoginById(String uid, String password, boolean isCreateHome, MethodChannel.Result result) {
        // UID 登录
        ThingHomeSdk.getUserInstance().loginOrRegisterWithUid("86", uid, password, new com.thingclips.smart.android.user.api.ILoginCallback() {
            @Override
            public void onSuccess(User user) {
                String str = JSON.toJSONString(user);
                result.success(str);
            }

            @Override
            public void onError(String code, String error) {
                result.error(code, error, null);
            }
        });

    }
}
