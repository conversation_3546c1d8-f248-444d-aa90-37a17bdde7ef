//
//  TuyaAccount.m
//  Tuya
//
//  Created by <PERSON> on 2022/5/9.
//

#import "TuyaAccount.h"
#import <ThingSmartHomeKit/ThingSmartKit.h>
#import "MJExtension.h"

@implementation TuyaAccount

/**
 *  使用手机号码和密码登录账号
 *
 *  @param phone     手机号码
 *  @param password  密码
 *  @param code      国家码，例如 86
 *  @param result    回调函数 成功  1 或 失败  0
 */
+ (void)loginByPhone:(NSString *)phone password:(NSString *)password countryCode:(NSString *)code result:(FlutterResult)result {
    [[ThingSmartUser sharedInstance] loginByPhone:@"86" phoneNumber:phone password:password success:^{
        NSLog(@"login success");
        result(@"1");
    } failure:^(NSError *error) {
        NSLog(@"login failure: %@", error);
        result(@"0");
    }];
}

/**
 *  用户 UID 登录(弃用)
 *
 *  @param uid       匿名 ID，没有格式要求
 *  @param password  密码LoginById
 *  @param code      国家码，例如 86
 *  @param result    回调函数 成功  1 或 失败  0
 */
+ (void)loginByUid:(NSString *)uid password:(NSString *)password countryCode:(NSString *)code result:(FlutterResult)result {
    /// 手机号码登录
    [[ThingSmartUser sharedInstance] loginByUid:uid password:password countryCode:code success:^{
        NSLog(@"loginByUid success");
        result(@"1");
    } failure:^(NSError *error) {
        NSLog(@"loginByUid failure: %@", error);
        result(@"0");
    }];
}


/**
 *  用户 UID 登录
 *
 *  @param uid         匿名 ID，没有格式要求
 *  @param password    密码
 *  @param createHome  是否创建默认家庭
 *  @param code        国家码，例如 86
 *  @param result      回调函数 成功  1 或 失败  0
 */
+ (void)loginByUid:(NSString *)uid password:(NSString *)password createHome:(BOOL)createHome countryCode:(NSString *)code result:(FlutterResult)result {
    [[ThingSmartUser sharedInstance] loginOrRegisterWithCountryCode:code uid:uid password:password createHome:createHome success:^(id res) {
        NSLog(@"loginOrRegisterWithCountryCode success: %@", res);
        result(@"1");
    } failure:^(NSError *error) {
        NSLog(@"loginOrRegisterWithCountryCode failure: %@", error);
        result(@"0");
    }];
}

@end
