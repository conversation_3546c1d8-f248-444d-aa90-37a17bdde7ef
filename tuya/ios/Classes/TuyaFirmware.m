//
//  TuyaFirmware.m
//  Runner
//
//  Created by YZH on 2022/6/1.
//

#import "TuyaFirmware.h"
#import <ThingSmartHomeKit/ThingSmartKit.h>
#import "MJExtension.h"


/// 单利对象
static TuyaFirmware *_instance = nil;

@interface TuyaFirmware () <ThingSmartDeviceDelegate>
/// 升级会掉
@property (nonatomic, copy) FlutterResult upgradeResult;
@end

@implementation TuyaFirmware

/*
/// 单利对象
+ (instancetype)internal {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _instance  = [[TuyaFirmware alloc] init];
    });
    return _instance;
}

/**
 *  查询升级信息
 *
 *  @param deviceId  设备ID
 *  @param result    回调函数 成功 1 或 失败 0
 *
+ (void)getFirmwareUpgradeInfo:(NSString *)deviceId result:(FlutterResult)result {
    [[ThingSmartDevice deviceWithDeviceId:deviceId] getFirmwareUpgradeInfo:^(NSArray<ThingSmartFirmwareUpgradeModel *> *upgradeModelList) {
        NSLog(@"getFirmwareUpgradeInfo success");
        if (upgradeModelList != nil && upgradeModelList.count > 0) {
            // homes 家庭列表
            @try {

                NSMutableArray *datas = [NSMutableArray arrayWithCapacity:upgradeModelList.count];
                for (ThingSmartFirmwareUpgradeModel *model in upgradeModelList) {
                    [datas addObject:model.mj_JSONObject];
                }
                NSString *json = [datas mj_JSONString];
                result(json);

            } @catch (NSException *exp) {
                NSString *err = [NSString stringWithFormat:@"error:%@  %@  %@", exp.name, exp.reason, exp.userInfo[NSLocalizedDescriptionKey]];
                NSLog(@">>> getHomeList Error:%@", err);
                result(nil);
            }
        } else {
            result(nil);
        }
    } failure:^(NSError *error) {
        NSLog(@"getFirmwareUpgradeInfo failure: %@", error);
        result(nil);
    }];
}



+ (void)upgradeFirmwareWithDeviceId:(NSString *)deviceId type:(NSInteger)type result:(FlutterResult)result {

    TuyaFirmware *_obj = [TuyaFirmware internal];
    _obj.upgradeResult = result;

    ThingSmartDevice *device = [ThingSmartDevice deviceWithDeviceId:deviceId];

    device.delegate = _obj;

    // type: 从查询设备升级信息接口 getFirmwareUpgradeInfo 返回的固件类型
    // ThingSmartFirmwareUpgradeModel - type
    [device upgradeFirmware:type success:^{
        NSLog(@"upgradeFirmware success");
        result(@"100");
    } failure:^(NSError *error) {
        NSLog(@"upgradeFirmware failure: %@", error);
        result(@"-1");
    }];
}

#pragma mark --- <ThingSmartDeviceDelegate>

- (void)device:(ThingSmartDevice *)device firmwareUpgradeStatusModel:(ThingSmartFirmwareUpgradeStatusModel *)upgradeStatusModel {
    //固件升级状态回调
}

- (void)device:(ThingSmartDevice *)device firmwareUpgradeProgress:(NSInteger)type progress:(double)progress {
    //固件升级的进度
    if (self.upgradeResult != nil) {
        self.upgradeResult(@(progress).stringValue);
    }
}
*/
@end
