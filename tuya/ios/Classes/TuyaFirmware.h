//
//  TuyaFirmware.h
//  Tuya
//
//  Created by <PERSON> on 2022/5/9.
//

#import <Foundation/Foundation.h>
#import <Flutter/Flutter.h>

NS_ASSUME_NONNULL_BEGIN


@interface TuyaFirmware : NSObject

/**
 *  查询升级信息
 *
 *  @param deviceId  设备ID
 *  @param result    回调函数 成功 1 或 失败 0
 */
+ (void)getFirmwareUpgradeInfo:(NSString *)deviceId result:(FlutterResult)result;

/**
 * 固件升级
 *
 * @param type    需要升级的类型，从设备升级信息接口 getFirmwareUpgradeInfo 查询
 * @param result    回调函数 成功 100 或 失败 -1   同时返回升级进度数据
 */
+ (void)upgradeFirmwareWithDeviceId:(NSString *)deviceId type:(NSInteger)type result:(FlutterResult)result;


@end

NS_ASSUME_NONNULL_END
