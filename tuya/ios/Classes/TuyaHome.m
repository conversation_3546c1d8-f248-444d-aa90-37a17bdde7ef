//
//  TuyaHome.m
//  Tuya
//
//  Created by <PERSON> on 2022/5/18.
//

#import "TuyaHome.h"


@implementation TuyaHome

/**
 *  创建家庭
 *
 * @param homeName    家庭的名称
 * @param geoName    家庭的地址
 * @param rooms    家庭下房间的名称列表
 * @param latitude    家庭地址纬度
 * @param longitude    家庭地址经度
 * @param result 回调
 */
+ (void)addHomeWithName:(NSString *)homeName
                geoName:(NSString *)geoName
                  rooms:(NSArray <NSString *>*)rooms
               latitude:(double)latitude
              longitude:(double)longitude
                 result:(FlutterResult)result {
    ThingSmartHomeManager *manage = [[ThingSmartHomeManager alloc] init];
    [manage addHomeWithName:homeName geoName:geoName rooms:rooms latitude:latitude longitude:longitude success:^(long long homeId) {
        // homeId 创建的家庭的 homeId
        NSLog(@"add home success");
//         result(@(homeId).stringValue);
        result([@{@"homeId" : @(homeId), @"name" : homeName} mj_JSONString]);
    } failure:^(NSError *error) {
        NSLog(@"add home failure: %@", error);
        result(nil);
    }];
}

/**
 * 查询家庭的列表
 *
 * @param result 回调
 */
+ (void)getHomeList:(FlutterResult)result  {
    ThingSmartHomeManager *manage = [[ThingSmartHomeManager alloc] init];
    [manage getHomeListWithSuccess:^(NSArray<ThingSmartHomeModel *> *homes) {
        NSLog(@"******homes.count:%ld", homes.count);
        // homes 家庭列表
        @try {
            if (homes != nil && homes.count > 0) {
                NSMutableArray *datas = [NSMutableArray arrayWithCapacity:homes.count];
                for (ThingSmartHomeModel *model in homes) {
                   [datas addObject:model.mj_JSONObject];
                }
                NSString *json = [datas mj_JSONString];
                result(json);
            } else {
                result(nil);
            }
        } @catch (NSException *exp) {
            NSString *err = [NSString stringWithFormat:@"error:%@  %@  %@", exp.name, exp.reason, exp.userInfo[NSLocalizedDescriptionKey]];
            NSLog(@">>> getHomeList Error:%@", err);
            result(nil);
        }
    } failure:^(NSError *error) {
        NSLog(@"get home list failure: %@", error);
        result(nil);
    }];
}


/**
 *  修改家庭信息
 *
 * @param homeId    家庭的名称
 * @param homeName    家庭的名称
 * @param geoName    家庭的地址
 * @param latitude    家庭地址纬度
 * @param longitude    家庭地址经度
 * @param result 回调
 */
+ (void)updateHomeInfoWithHomeId:(long long)homeId
                            name:(NSString *)homeName
                         geoName:(NSString *)geoName
                        latitude:(double)latitude
                       longitude:(double)longitude
                          result:(FlutterResult)result {
    ThingSmartHome *home = [ThingSmartHome homeWithHomeId:homeId];
    [home updateHomeInfoWithName:homeName geoName:geoName latitude:latitude longitude:longitude success:^{
        NSLog(@"update home info success");
        result(@"1");
    } failure:^(NSError *error) {
        NSLog(@"update home info failure: %@", error);
        result(@"0");
    }];
}

/**
 *  注销或删除家庭
 *
 * @param homeId homeId
 * @param result 回调
 */
+ (void)deleteHomeWithHomeId:(long long)homeId result:(FlutterResult)result {
    ThingSmartHome *home = [ThingSmartHome homeWithHomeId:homeId];
    [home dismissHomeWithSuccess:^() {
        NSLog(@"dismiss home success");
        result(@"1");
    } failure:^(NSError *error) {
        NSLog(@"dismiss home failure: %@", error);
        result(@"0");
    }];
}

/**
 *  查询家庭的详细信息
 *
 * @param homeId homeId
 * @param result 回调
 */
+ (void)getHomeDetailWithHomeId:(long long)homeId result:(FlutterResult)result  {
    ThingSmartHome *home = [ThingSmartHome homeWithHomeId:homeId];
    [home getHomeDetailWithSuccess:^(ThingSmartHomeModel *homeModel) {
        // homeModel 家庭信息
        NSLog(@"get home detail success");
        @try {
           if (homeModel != nil) {
              result(homeModel.mj_JSONString);
           } else {
              result(nil);
           }
       } @catch (NSException *exp) {
           NSString *err = [NSString stringWithFormat:@"error:%@  %@  %@", exp.name, exp.reason, exp.userInfo[NSLocalizedDescriptionKey]];
           NSLog(@">>> getHomeList Error:%@", err);
           result(nil);
        }
    } failure:^(NSError *error) {
        NSLog(@"get home detail failure: %@", error);
        result(nil);
    }];
}

@end
