//
//  TuyaHome.h
//  Tuya
//
//  Created by <PERSON> on 2022/5/18.
//

#import <Flutter/Flutter.h>
#import <Foundation/Foundation.h>
#import <ThingSmartHomeKit/ThingSmartKit.h>
#import "MJExtension.h"


NS_ASSUME_NONNULL_BEGIN

@interface TuyaHome : NSObject


/**
 * 查询家庭的列表
 *
 * @param result 回调 返回 家庭列表 List String 或者 nil
 */
+ (void)getHomeList:(FlutterResult)result;

/**
 *  创建家庭
 *
 * @param homeName    家庭的名称
 * @param geoName    家庭的地址
 * @param rooms    家庭下房间的名称列表
 * @param latitude    家庭地址纬度
 * @param longitude    家庭地址经度
 * @param result 回调 返回 homeId 或 nil
 */
+ (void)addHomeWithName:(NSString *)homeName
                geoName:(NSString *)geoName
                  rooms:(NSArray <NSString *>*)rooms
               latitude:(double)latitude
              longitude:(double)longitude
                 result:(FlutterResult)result;


/**
 *  修改家庭信息
 *
 * @param homeId    家庭的名称
 * @param homeName    家庭的名称
 * @param geoName    家庭的地址
 * @param latitude    家庭地址纬度
 * @param longitude    家庭地址经度
 * @param result 回调 成功 true 或 失败 false
 */
+ (void)updateHomeInfoWithHomeId:(long long)homeId
                            name:(NSString *)homeName
                         geoName:(NSString *)geoName
                        latitude:(double)latitude
                       longitude:(double)longitude
                          result:(FlutterResult)result;

/**
 *  注销或删除家庭
 *
 * @param homeId homeId
 * @param result 回调 成功 true 或 失败 false
 */
+ (void)deleteHomeWithHomeId:(long long)homeId result:(FlutterResult)result;


/**
 *  查询家庭的详细信息
 *
 * @param homeId homeId
 * @param result 回调 返回 详情 String 或 nil
 */
+ (void)getHomeDetailWithHomeId:(long long)homeId result:(FlutterResult)result;

@end

NS_ASSUME_NONNULL_END
