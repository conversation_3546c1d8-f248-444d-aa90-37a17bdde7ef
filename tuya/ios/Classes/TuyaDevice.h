//
//  TuyaDevice.h
//  Tuya
//
//  Created by <PERSON> on 2022/5/18.
//

#import <Foundation/Foundation.h>
#import <Flutter/Flutter.h>
#import <ThingSmartHomeKit/ThingSmartKit.h>


NS_ASSUME_NONNULL_BEGIN

@interface TuyaDevice : NSObject

+ (instancetype)internal;

/**
 * 移除设备
 */
- (void)removeWithDeviceId:(NSString *)deviceId result:(FlutterResult)result;

/**
 * 恢复出厂设置
 */
- (void)resetFactoryWithDeviceId:(NSString *)deviceId result:(FlutterResult)result;

/**
 * 获取配网令牌
 *
 * @param result    回调方法 token
 */
- (void)getTokenWithHomeId:(long long)homeId result:(FlutterResult)result;

/**
 * 二维码配网token
 */
- (void)getTokenWithUUID:(NSString *)uuid homeId:(long long)homeId  result:(FlutterResult)result;

/**
 *  开始配网
 *
 *  @param mode 配网模式
 *          -> 0  TYActivatorModeEZ     <智能配置模式
 *          -> 1  TYActivatorModeAP     <访问模式
 *          -> 2  TYActivatorModeQRCode     < QRCode模式
 *          -> 3  TYActivatorModeWired          < wired mode
 *  @param ssid  WIFI 名称
 *  @param password wifi密码
 *  @param token 配网令牌
 *  @param result 回调，成功 true，或失败 false
 */
- (void)startConfigMode:(NSUInteger)mode WiFi:(NSString *)ssid password:(NSString *)password token:(NSString *)token result:(FlutterResult)result;

/**
 * 启动二维码配置网络
 *
 * @param uuid              设备 ID
 * @param homeId        设备将要绑定到的家庭的 ID
 * @param ssid             Wi-Fi 名称
 * @param password    Wi-Fi 密码
 */
- (void)startQRCodeConfigNetWithUUID:(NSString *)uuid homeId:(long long)homeId ssid:(NSString *)ssid password:(NSString *)password result:(FlutterResult)result;

/**
 * 停止配网
 */
- (void)stopConfigNet:(FlutterResult)result;

/**
 * 获取蓝牙启用状态
 *
 * @param result  回调函数，蓝牙状态，开启 1 或 关闭 0
 */
- (void)getBluetoothEnabledStatus:(FlutterResult)result;


/**
 *  开始扫描
 *
 *  @param result 回调
 */
- (void)startListening:(FlutterResult)result;

/**
 *  停止扫描
 *
 *  @param result 回调
 */
- (void)stopListening:(FlutterResult)result;

/**
 * 蓝牙单点配网
 *
 * 接口说明
 *    扫描到未激活的设备后，可以进行设备激活并且注册到云端，并记录在家庭下。
 *
 * @param params    设备信息 Model，来源于扫描代理方法返回的结果
 *              uuid                NSString    设备 UUID，可以唯一区别设备
 *              productId       NSString    产品 ID
 *              mac                NSString    设备 Mac，不可作为唯一码，iOS 为空
 *              isActive          Bool    是否被绑定，能回调的均为未配网的设备
 *              isSupport5G         Bool    表示蓝牙 LE 设备是否支持通过路由器在5GHz频段上的连接
 *              isProuductKey      Bool    是否支持 productKey
 *              bleProtocolV        int    设备支持的涂鸦蓝牙协议版本
 *              bleType                 Enum    设备类型，用于区分不同协议的设备（取值范围 1 ~ 11）
 * @param homeId    当前家庭 ID
 */
- (void)activeBLE:(NSDictionary *)params homeId:(long long)homeId result:(FlutterResult)result;
//+ (void)startActivatorWithHomeId:(long long)homeId uuid:(NSString *)uuid deviceType:(int)type result:(FlutterResult)result;


/**
 * 蓝牙单点配网
 *
 * @param uuid  NSString    设备 UUID，可以唯一区别设备
 * @param homeId    当前家庭 ID
 */
- (void)activeDeviceWithUUID:(NSString *)uuid homeId:(long long)homeId result:(FlutterResult)result;

/**
 *
 *
 * 双模设备配网
 *
 * 接口说明
 *    扫描到未激活的设备后，可以使用该方法将配网信息通过蓝牙通道下发给设备，并等待配网结果回调。
 *
 * @param uuid    设备 uuid
 * @param homeId    当前家庭 ID
 * @param productId    产品 ID
 * @param ssid    Wi-Fi 路由器热点名称
 * @param password    Wi-Fi 路由器热点密码
 * @param timeout    轮询时间
 */
- (void)startConfigBLEWifiDeviceWithUUID:(NSString *)uuid
                                  homeId:(long long)homeId
                               productId:(NSString *)productId
                                    ssid:(NSString *)ssid
                                password:(NSString *)password
                                 timeout:(NSTimeInterval)timeout  result:(FlutterResult)result;

/**
 *  关闭轮询
 */
- (void)stopDiscover;

/**
 * 连接离线中的设备
 * 接口说明
 *    若设备处于离线状态，可以调用连接方法进行设备连接。
 *
 * @param uuid    设备 UUID
 * @param productKey    产品 ID
 * @param result 回调 成功 true 或失败 false
 */
- (void)connectBLEWithUUID:(NSString *)uuid productKey:(NSString *)productKey result:(FlutterResult)result;

/**
 * 断开已连接的设备
 * 接口说明
 *    若设备处于连接状态 可以调用该方法断开与设备连接。
 *
 * @param uuid    设备 UUID
 * @param result 回调 成功 true 或失败 false
 */
- (void)disconnectBLEWithUUID:(NSString *)uuid result:(FlutterResult)result;

- (void)scanQrcode:(long long)homeId qrcode:(NSString *)qrcode result:(FlutterResult)result;

- (void)activator:(ThingSmartActivator *)activator didReceiveDevice:(ThingSmartDeviceModel *)deviceModel error:(NSError *)error;

@end

NS_ASSUME_NONNULL_END
