//
//  TuyaDevice.m
//  Tuya
//
//  Created by <PERSON> on 2022/5/18.
//

#import "TuyaDevice.h"
#import "MJExtension.h"
#import <ThingSmartHomeKit/ThingSmartKit.h>

/// 单利对象
static TuyaDevice *_instance = nil;

/// 全局静态数据缓存
static NSMutableDictionary *_allDevices = nil;

@interface TuyaDevice () <ThingSmartBLEManagerDelegate,
                          ThingSmartActivatorDelegate,
                          ThingSmartBLEWifiActivatorDelegate>
/// 蓝牙启用状态回调
@property(nonatomic, copy) FlutterResult bluetoothStatusResult;
/// 蓝牙开始扫描
@property(nonatomic, copy) FlutterResult startListeningResult;
/// 配网回调
@property(nonatomic, copy) FlutterResult configNetResult;
/// 双模配网回调
@property(nonatomic, copy) FlutterResult configBLEWifiDeviceResult;

@end

@implementation TuyaDevice

/// 单利对象
+ (instancetype)internal {
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    _instance = [[TuyaDevice alloc] init];
  });
  return _instance;
}

/**
 * 移除设备
 */
- (void)removeWithDeviceId:(NSString *)deviceId result:(FlutterResult)result {
  [[ThingSmartDevice deviceWithDeviceId:deviceId]
      remove:^{
        NSLog(@"remove success");
        result(@"1");
      }
      failure:^(NSError *error) {
        NSLog(@"remove failure: %@", error);
        result(@"0");
      }];
  ;
}

/**
 * 恢复出厂设置
 */
- (void)resetFactoryWithDeviceId:(NSString *)deviceId
                          result:(FlutterResult)result {
  [[ThingSmartDevice deviceWithDeviceId:deviceId]
      resetFactory:^{
        NSLog(@"reset success");
        result(@"1");
      }
      failure:^(NSError *error) {
        NSLog(@"reset failure: %@", error);
        result(@"0");
      }];
  ;
}

/**
 * 获取配网令牌
 *
 * @param result    回调方法 token
 */
- (void)getTokenWithHomeId:(long long)homeId result:(FlutterResult)result {
  // 获取 配网 Token
  [[ThingSmartActivator sharedInstance] getTokenWithHomeId:homeId
      success:^(NSString *token) {
        NSLog(@"getToken success: %@", token);
        result(token);
      }
      failure:^(NSError *error) {
        NSLog(@"getToken failure: %@", error.localizedDescription);
        result(nil);
      }];
}

/**
 * 二维码配网token
 */
- (void)getTokenWithUUID:(NSString *)uuid
                  homeId:(long long)homeId
                  result:(FlutterResult)result {
  [[ThingSmartActivator sharedInstance] getTokenWithUUID:uuid
      homeId:homeId
      success:^(NSString *token) {
        // 获取token
        NSLog(@"getUUIDToken success: %@", token);
        result(token);
      }
      failure:^(NSError *error) {
        NSLog(@"getUUIDToken failure: %@", error.localizedDescription);
        result(nil);
      }];
}

/**
 *  开始配网
 */
- (void)startConfigMode:(NSUInteger)mode
                   WiFi:(NSString *)ssid
               password:(NSString *)password
                  token:(NSString *)token
                 result:(FlutterResult)result {
  /// 设置回调
  self.configNetResult = result;
  // 设置 ThingSmartActivator 的 delegate，并实现 delegate 方法
  [ThingSmartActivator sharedInstance].delegate = self;
  // 开始配网，快连模式对应 mode 为 TYActivatorModeEZ
  [[ThingSmartActivator sharedInstance] startConfigWiFi:mode
                                                   ssid:ssid
                                               password:password
                                                  token:token
                                                timeout:2000];
}

/**
 * 启动二维码配置网络
 *
 * @param uuid              设备 ID
 * @param homeId        设备将要绑定到的家庭的 ID
 * @param ssid             Wi-Fi 名称
 * @param password    Wi-Fi 密码
 */
- (void)startQRCodeConfigNetWithUUID:(NSString *)uuid
                              homeId:(long long)homeId
                                ssid:(NSString *)ssid
                            password:(NSString *)password
                              result:(FlutterResult)result {

  /// 设置回调
  self.configNetResult = result;

  /// 设置 ThingSmartActivator 的 delegate，并实现 delegate 方法
  [ThingSmartActivator sharedInstance].delegate = self;

  /// 获取token
  [[ThingSmartActivator sharedInstance] getTokenWithUUID:uuid
      homeId:homeId
      success:^(NSString *token) {
        // 获取token
        NSLog(@"getUUIDToken success: %@", token);
        // // 开始配网，快连模式对应 mode 为 TYActivatorModeEZ//
        // TYActivatorModeQRCode
        [[ThingSmartActivator sharedInstance]
            startConfigWiFi:ThingActivatorModeEZ
                       ssid:ssid
                   password:password
                      token:token
                    timeout:2000];
      }
      failure:^(NSError *error) {
        NSLog(@"getUUIDToken failure: %@", error.localizedDescription);
        result(@"0");
      }];
}

/**
 * 停止配网
 */
- (void)stopConfigNet:(FlutterResult)result {
  [ThingSmartActivator sharedInstance].delegate = nil;
  [[ThingSmartActivator sharedInstance] stopConfigWiFi];
  result(@"1");
}

/**
 * 获取蓝牙启用状态
 *
 * @param result  回调函数，蓝牙状态，开启 1 或 关闭 0
 */
- (void)getBluetoothEnabledStatus:(FlutterResult)result {
  // 添加回调
  self.bluetoothStatusResult = result;
  // 设置代理
  [ThingSmartBLEManager sharedInstance].delegate = self;
}

// 开始扫描
- (void)startListening:(FlutterResult)result {
  // 添加回调
  self.startListeningResult = result;
  // 设置代理
  [ThingSmartBLEManager sharedInstance].delegate = self;
  ;
  // 开始扫描
  [[ThingSmartBLEManager sharedInstance] startListening:YES];
}

// 停止扫描
- (void)stopListening:(FlutterResult)result {
  // 清除回调
  self.startListeningResult = nil;
  // 停止扫描
  [[ThingSmartBLEManager sharedInstance] stopListening:YES];
  // 回调
  result(@"1");
}

/**
 * 蓝牙单点配网
 *
 * @param params    设备信息 Model，来源于扫描代理方法返回的结果
 *              uuid                NSString    设备 UUID，可以唯一区别设备
 *              productId       NSString    产品 ID
 *              mac                NSString    设备 Mac，不可作为唯一码，iOS
 * 为空 isActive          Bool    是否被绑定，能回调的均为未配网的设备
 *              isSupport5G         Bool    表示蓝牙 LE
 * 设备是否支持通过路由器在5GHz频段上的连接 isProuductKey      Bool    是否支持
 * productKey bleProtocolV        int    设备支持的涂鸦蓝牙协议版本 bleType Enum
 * 设备类型，用于区分不同协议的设备（取值范围 1 ~ 11）
 * @param homeId    当前家庭 ID
 */
- (void)activeBLE:(NSDictionary *)params
           homeId:(long long)homeId
           result:(FlutterResult)result {
  ThingBLEAdvModel *model = [ThingBLEAdvModel mj_objectWithKeyValues:params];
  [[ThingSmartBLEManager sharedInstance] activeBLE:model
      homeId:homeId
      success:^(ThingSmartDeviceModel *deviceModel) {
        // 激活成功
        NSLog(@"激活成功");

        // 配网成功
        NSDictionary *deviceDict = @{
          @"devId" : deviceModel.devId ?: @"",
          @"name" : deviceModel.name ?: @"",
          // 添加其他属性
        };
        NSError *jsonError;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:deviceDict
                                                           options:0
                                                             error:&jsonError];
        NSString *jsonString =
            jsonError ? @""
                      : [[NSString alloc] initWithData:jsonData
                                              encoding:NSUTF8StringEncoding];
        result(jsonString);
      }
      failure:^{
        // 激活中的错误
        NSLog(@"激活失败！");
        result(nil);
      }];
}

/**
 * 蓝牙单点配网
 *
 * @param uuid  NSString    设备 UUID，可以唯一区别设备
 * @param homeId    当前家庭 ID
 */
//- (void)activeDeviceWithUUID:(NSString *)uuid homeId:(long long)homeId
// result:(FlutterResult)result {
//    if (_allDevices == nil) {
//        result(nil);
//        return;
//    }
//    if (![_allDevices.allKeys containsObject:uuid]) {
//        result(nil);
//        return;
//    }
//
//    self.configBLEWifiDeviceResult = result;
//
//    TYBLEAdvModel *deviceInfo = _allDevices[uuid];
//    NSString *productId = deviceInfo.productId;
//
//    ThingSmartBLEWifiActivator *manager = [ThingSmartBLEWifiActivator
//    sharedInstance]; manager.bleWifiDelegate = self;
//
//    [manager startConfigBLEWifiDeviceWithUUID:uuid homeId:homeId
//    productId:productId ssid:nil password:nil  timeout:100 success:^{
//            // 下发成功
//            NSLog(@">>> 双模设备配网成功");
////            result(deviceInfo.mj_JSONString);
//        } failure:^{
//            // 下发失败
//            NSLog(@">>> 双模设备配网失败");
////            result(nil);
//        }];
//}

/**
 * 蓝牙单点配网
 *
 * @param uuid  NSString    设备 UUID，可以唯一区别设备
 * @param homeId    当前家庭 ID
 */
- (void)activeDeviceWithUUID:(NSString *)uuid
                      homeId:(long long)homeId
                      result:(FlutterResult)result {
  self.configBLEWifiDeviceResult = result;

  ThingSmartBLEWifiActivator *manager =
      [ThingSmartBLEWifiActivator sharedInstance];
  manager.bleWifiDelegate = self;

  [manager startConfigBLEWifiDeviceWithUUID:uuid
      homeId:homeId
      productId:@"4vyzrxvibcxaalgt"
      ssid:nil
      password:nil
      timeout:100
      success:^{
        // 下发成功
        NSLog(@">>> 双模设备配网成功");
      }
      failure:^{
        // 下发失败
        NSLog(@">>> 双模设备配网失败");
      }];
}

/// 双模配方回调方法
- (void)bleWifiActivator:(ThingSmartBLEWifiActivator *)activator
    didReceiveBLEWifiConfigDevice:(ThingSmartDeviceModel *)deviceModel
                            error:(NSError *)error {
  if (error) {
    NSLog(@">>> 双模设备配网失败");

    [self showAlert:[NSString stringWithFormat:@"双模设备配网失败 err:%@",
                                               error.localizedDescription]];

    if (self.configBLEWifiDeviceResult)
      self.configBLEWifiDeviceResult(@"");

  } else {

    // [self showAlert:[NSString stringWithFormat:@"双模配网成功 obj:%@",
    // [deviceModel mj_JSONString]]];

      if (self.configBLEWifiDeviceResult){
          NSDictionary *deviceDict = @{
            @"devId" : deviceModel.devId ?: @"",
            @"name" : deviceModel.name ?: @"",
            // 添加其他属性
          };
          NSError *jsonError;
          NSData *jsonData = [NSJSONSerialization dataWithJSONObject:deviceDict
                                                             options:0
                                                               error:&jsonError];
          NSString *jsonString =
              jsonError ? @""
                        : [[NSString alloc] initWithData:jsonData
                                                encoding:NSUTF8StringEncoding];
          self.configBLEWifiDeviceResult(jsonString);
      }
  }
}

- (void)showAlert:(NSString *)msg {

  UIViewController *viewController =
      [UIApplication sharedApplication].keyWindow.rootViewController;

  UIAlertController *alert =
      [UIAlertController alertControllerWithTitle:@"配网弹窗"
                                          message:msg
                                   preferredStyle:UIAlertControllerStyleAlert];
  [alert addAction:[UIAlertAction
                       actionWithTitle:@"确定"
                                 style:UIAlertActionStyleDefault
                               handler:^(UIAlertAction *_Nonnull action){

                               }]];
  [viewController showViewController:alert sender:nil];
}

//+ (void)activeDeviceWithUUID:(NSString *)uuid homeId:(long long)homeId
// result:(FlutterResult)result {
//    if (_allDevices == nil) {
//        result(nil);
//        return;
//    }
//    if (![_allDevices.allKeys containsObject:uuid]) {
//        result(nil);
//        return;
//    }
//    TYBLEAdvModel *deviceInfo = _allDevices[uuid];
//    [ThingSmartBLEManager.sharedInstance activeBLE:deviceInfo homeId:homeId
//    success:^(ThingSmartDeviceModel * _Nonnull deviceModel) {
//        // 激活成功
//        NSLog(@"激活成功");
//        result(deviceModel.mj_JSONString);
//    } failure:^{
//        // 激活中的错误
//        NSLog(@"激活失败！");
//        result(nil);
//    }];
//}

// + (void)startActivatorWithHomeId:(long long)homeId uuid:(NSString *)uuid
// deviceType:(int)type result:(FlutterResult)result {
//     TYBLEAdvModel *model = [TYBLEAdvModel new];
//     model.uuid = uuid;
//     mode.deviceType = deviceType;
//     [[ThingSmartBLEManager sharedInstance] activeBLE:model homeId:homeId
//     success:^(ThingSmartDeviceModel *deviceModel) {
//         // 激活成功
//         NSLog(@"激活成功");
//         result(deviceModel.mj_JSONString);
//     } failure:^{
//         // 激活中的错误
//         NSLog(@"激活失败！");
//         result(nil);
//     }];
// }

/**
 * 双模设备配网
 *
 * 接口说明
 *    扫描到未激活的设备后，可以使用该方法将配网信息通过蓝牙通道下发给设备，并等待配网结果回调。
 *
 * 参数说明
 * @param uuid    设备 uuid
 * @param homeId    当前家庭 ID
 * @param productId    产品 ID
 * @param ssid    Wi-Fi 路由器热点名称
 * @param password    Wi-Fi 路由器热点密码
 * @param timeout    轮询时间
 */
- (void)startConfigBLEWifiDeviceWithUUID:(NSString *)uuid
                                  homeId:(long long)homeId
                               productId:(NSString *)productId
                                    ssid:(NSString *)ssid
                                password:(NSString *)password
                                 timeout:(NSTimeInterval)timeout
                                  result:(FlutterResult)result {
  [[ThingSmartBLEWifiActivator sharedInstance]
      startConfigBLEWifiDeviceWithUUID:uuid
      homeId:homeId
      productId:productId
      ssid:ssid
      password:password
      timeout:timeout
      success:^{
        // 下发成功
        NSLog(@">>> 双模设备配网成功");
        result(@"1");
      }
      failure:^{
        // 下发失败
        NSLog(@">>> 双模设备配网失败");
        result(@"0");
      }];
}

/**
 *  关闭轮询
 */
- (void)stopDiscover {
  // 在配网结束后调用
  [[ThingSmartBLEWifiActivator sharedInstance] stopDiscover];
}

/**
 * 连接离线中的设备
 * 接口说明
 *    若设备处于离线状态，可以调用连接方法进行设备连接。
 *
 * @param uuid    设备 UUID
 * @param productKey    产品 ID
 * @param result 回调 成功 true 或失败 false
 */
- (void)connectBLEWithUUID:(NSString *)uuid
                productKey:(NSString *)productKey
                    result:(FlutterResult)result {
  [[ThingSmartBLEManager sharedInstance] connectBLEWithUUID:uuid
      productKey:productKey
      success:^{
        NSLog(@"链接成功");
        result(@"1");
      }
      failure:^{
        NSLog(@"链接失败");
        result(@"0");
      }];
}

/**
 * 断开已连接的设备
 * 接口说明
 *    若设备处于连接状态 可以调用该方法断开与设备连接。
 *
 * @param uuid    设备 UUID
 * @param result 回调 成功 true 或失败 false
 */
- (void)disconnectBLEWithUUID:(NSString *)uuid result:(FlutterResult)result {
  [[ThingSmartBLEManager sharedInstance] disconnectBLEWithUUID:uuid
      success:^{
        NSLog(@"断开链接成功");
        result(@"1");
      }
      failure:^(NSError *error) {
        NSLog(@"断开链接失败");
        result(@"0");
      }];
}

- (void)scanQrcode:(long long)homeId
            qrcode:(NSString *)qrcode
            result:(FlutterResult)result {
  [ThingSmartActivator parseQRCode:qrcode
      success:^(id dicresult) {
        NSDictionary *qrCodeDict = (NSDictionary *)dicresult;
        NSLog(@"Action Name: %@", [qrCodeDict objectForKey:@"actionName"]);
        NSLog(@"Action Data: %@", [qrCodeDict objectForKey:@"actionData"]);
        id actionData = [qrCodeDict objectForKey:@"actionData"];
        if ([actionData isKindOfClass:[NSDictionary class]]) {
          NSString *uuid = [(NSDictionary *)actionData objectForKey:@"uuid"];
          if (uuid) {
            NSLog(@"UUID: %@", uuid);
            [self startQRCodeConfigNetWithUUID:uuid
                                        homeId:homeId
                                          ssid:@""
                                      password:@""
                                        result:result];
          } else {
            NSLog(@"actionData 字典中未找到 uuid 键");
          }
        } else if ([actionData isKindOfClass:[NSString class]]) {
          NSError *error = nil;
          NSData *jsonData =
              [(NSString *)actionData dataUsingEncoding:NSUTF8StringEncoding];
          NSDictionary *actionDataDict =
              [NSJSONSerialization JSONObjectWithData:jsonData
                                              options:0
                                                error:&error];
          if (actionDataDict &&
              [actionDataDict isKindOfClass:[NSDictionary class]]) {
            NSString *uuid = [actionDataDict objectForKey:@"uuid"];
            if (uuid) {
              NSLog(@"UUID: %@", uuid);
              [self startQRCodeConfigNetWithUUID:uuid
                                          homeId:homeId
                                            ssid:@""
                                        password:@""
                                          result:result];
            } else {
              NSLog(@"解析后的 actionData 中未找到 uuid 键");
            }
          } else {
            NSLog(@"无法解析 actionData JSON: %@", error.localizedDescription);
          }
        } else {
          NSLog(@"actionData 不是字符串或字典，类型: %@",
                NSStringFromClass([actionData class]));
        }
      }
      failure:^(NSError *error) {
        NSLog(@"Error: %@", error.localizedDescription);
      }];
}

#pragma mark--- ThingSmartBLEManagerDelegate

/**
 * 监测手机蓝牙状态
 *
 * 接口说明
 *    在用户手机的蓝牙发生状态变化时，如开启或关闭，可以通过设置代理收到具体的消息。
 *
 * @param isPoweredOn 蓝牙状态，开启或关闭
 *
 */
- (void)bluetoothDidUpdateState:(BOOL)isPoweredOn {
  NSLog(@"蓝牙状态变化: %@", isPoweredOn ? @"true" : @"false");
  // 蓝牙状态变化回调
  if (self.bluetoothStatusResult != nil) {
    self.bluetoothStatusResult(isPoweredOn ? @"1" : @"0");
  }
}

/**
 * 扫描到未激活的设备
 *
 * @param deviceInfo 未激活设备信息 Model
 */
- (void)didDiscoveryDeviceWithDeviceInfo:(ThingBLEAdvModel *)deviceInfo {
  // 判断是否存在设备
  if (_allDevices == nil) {
    _allDevices = [[NSMutableDictionary alloc] initWithCapacity:1];
  }
  _allDevices[deviceInfo.uuid] = deviceInfo;
  // 成功扫描到未激活的设备
  // 若设备已激活，则不会走此回调，且会自动进行激活连接
  if (self.startListeningResult != nil) {
      
      NSDictionary *deviceDict = @{
        @"uuid" : deviceInfo.uuid ?: @"",
        @"productId" : deviceInfo.productId ?: @"",
        // 添加其他属性
      };
      NSError *jsonError;
      NSData *jsonData = [NSJSONSerialization dataWithJSONObject:deviceDict
                                                         options:0
                                                           error:&jsonError];
      NSString *jsonString =
          jsonError ? @""
                    : [[NSString alloc] initWithData:jsonData
                                            encoding:NSUTF8StringEncoding];
      
    self.startListeningResult(jsonString);
  }
}

#pragma mark - ThingSmartActivatorDelegate
/// 分销状态更新的回调，wifi单产品，zigbee网关，zigbee子设备。
/// @param activator instance
/// @param deviceModel deviceModel
/// @param error error
- (void)activator:(ThingSmartActivator *)activator
    didReceiveDevice:(ThingSmartDeviceModel *)deviceModel
               error:(NSError *)error {
  if (!error && deviceModel) {
    // 配网成功
    NSDictionary *deviceDict = @{
      @"devId" : deviceModel.devId ?: @"",
      @"name" : deviceModel.name ?: @"",
      // 添加其他属性
    };
    NSError *jsonError;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:deviceDict
                                                       options:0
                                                         error:&jsonError];
    NSString *jsonString =
        jsonError ? @""
                  : [[NSString alloc] initWithData:jsonData
                                          encoding:NSUTF8StringEncoding];
    self.configNetResult(jsonString);
  }
  if (error) {
    // 配网失败
      NSString *errorCode = [NSString stringWithFormat:@"%@_%ld", error.domain, (long)error.code];
          NSString *errorMessage = [NSString stringWithFormat:@"%@ (%@)", error.localizedDescription, error.domain];
          NSDictionary *errorDetails = error.userInfo; // 或自定义 details
          self.configNetResult([FlutterError errorWithCode:errorCode
                                                  message:errorMessage
                                                  details:errorDetails]);

  }
}

@end
