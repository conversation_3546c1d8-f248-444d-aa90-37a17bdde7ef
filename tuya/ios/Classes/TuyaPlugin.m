#import "TuyaPlugin.h"
// #if __has_include(<tuya/tuya-Swift.h>)
// #import <tuya/tuya-Swift.h>
// #else
// // Support project import fallback if the generated compatibility header
// // is not copied when this plugin is created as a library.
// //
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
// #import "tuya-Swift.h"
// #endif

#import "TuyaAccount.h"
#import "TuyaDevice.h"
#import "TuyaHome.h"
#import <ThingSmartHomeKit/ThingSmartKit.h>
// #import "TuyaFirmware.h"

@implementation TuyaPlugin

+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar> *)registrar {
  FlutterMethodChannel *channel =
      [FlutterMethodChannel methodChannelWithName:@"tuya"
                                  binaryMessenger:[registrar messenger]];
  TuyaPlugin *instance = [[TuyaPlugin alloc] init];
  [registrar addMethodCallDelegate:instance channel:channel];
}

- (void)handleMethodCall:(FlutterMethodCall *)call
                  result:(FlutterResult)result {
  // 后去设备版本号
  if ([@"getPlatformVersion" isEqualToString:call.method]) {
    result([@"iOS "
        stringByAppendingString:[[UIDevice currentDevice] systemVersion]]);
  }
  // 手机号码登录
  else if ([@"Login" isEqualToString:call.method]) {
    NSString *phone = call.arguments[@"phone"];       //@"***********";
    NSString *password = call.arguments[@"password"]; //@"Zhou939436";
    NSString *code = @"86";
    [TuyaAccount loginByPhone:phone
                     password:password
                  countryCode:code
                       result:result];
  }
  // uid 登录
  else if ([@"LoginById" isEqualToString:call.method]) {
    NSString *uid = call.arguments[@"uid"];
    NSString *password = @"Zhou939436";
    //         NSString *uid = @"1111";
    //         NSString *password = @"123456";
    //         NSString *uid = @"abc";
    //         NSString *password = @"123";
    NSString *code = @"86";
    BOOL createHome = NO;
    // [TuyaAccount loginByUid:uid password:password countryCode:code
    // result:result];
    [TuyaAccount loginByUid:uid
                   password:password
                 createHome:createHome
                countryCode:code
                     result:result];
  }
  // 获取家庭列表
  else if ([@"getHomeList" isEqualToString:call.method]) {
    [TuyaHome getHomeList:result];
  }
  // 创建家庭
  else if ([@"createHome" isEqualToString:call.method]) {
    NSString *homeName = call.arguments[@"name"];
    NSString *geoName = @"默认位置";
    NSArray<NSString *> *rooms = @[];
    double latitude = 30;
    double longitude = 120;
    [TuyaHome addHomeWithName:homeName
                      geoName:geoName
                        rooms:rooms
                     latitude:latitude
                    longitude:longitude
                       result:result];
  }
  // 获取家庭详情
  else if ([@"getHomeDetail" isEqualToString:call.method]) {
    long long homeId = [call.arguments[@"id"] integerValue];
    [TuyaHome getHomeDetailWithHomeId:homeId result:result];
  }
  // 开始扫描
  else if ([@"StartScanDevice" isEqualToString:call.method]) {
    [[TuyaDevice internal] startListening:result];
  }
  // 停止扫描
  else if ([@"stopScanDevice" isEqualToString:call.method]) {
    [[TuyaDevice internal] stopListening:result];
  }

  //    // 二维码配网
  //    else if ([@"activeDevice" isEqualToString:call.method]) {
  //        NSString *uuid = call.arguments[@"uuid"];
  //        long long homeId = [call.arguments[@"homeId"] integerValue];
  ////#warning msg --- 和android 配网有点却别，这里需要添加 wifi 信息
  //        NSString *ssid = call.arguments[@"name"];
  //        NSString *password = call.arguments[@"pwd"];
  //        [[TuyaDevice internal] startQRCodeConfigNetWithUUID:uuid
  //        homeId:homeId ssid:ssid password:password result:result];
  //    }
  // 蓝牙配网
  else if ([@"activeDevice" isEqualToString:call.method]) {
    NSString *uuid = call.arguments[@"uuid"];
    long long homeId = [call.arguments[@"homeId"] integerValue];
    [[TuyaDevice internal] activeDeviceWithUUID:uuid
                                         homeId:homeId
                                         result:result];
  }
  // 重置设备
  else if ([@"resetFactory" isEqualToString:call.method]) {
    NSString *deviceId = call.arguments[@"uuid"];
    [[TuyaDevice internal] resetFactoryWithDeviceId:deviceId result:result];
  }
  // 删除设备
  else if ([@"removeDevice" isEqualToString:call.method]) {
    NSString *deviceId = call.arguments[@"uuid"];
    [[TuyaDevice internal] removeWithDeviceId:deviceId result:result];
  } else if ([@"scanQrcode" isEqualToString:call.method]) {
    long long homeId = [call.arguments[@"homeId"] integerValue];
    NSString *qrcode = call.arguments[@"qrcode"];

    [[TuyaDevice internal] scanQrcode:homeId qrcode:qrcode result:result];
  }
//  else if ([@"stopQrcodeActive" isEqualToString:call.method]) {
//
//    [[TuyaDevice internal] stopConfigNet];
//  }
  //     // 获取固件信息
  //     else if ([@"getUpgradeInfo" isEqualToString:call.method]) {
  //         NSString *deviceId = call.arguments[@"id"];
  //         [TuyaFirmware getFirmwareUpgradeInfo:deviceId result:result];
  //     }
  //     // 升级固件
  //     else if ([@"startUpgrade" isEqualToString:call.method]) {
  //         NSString *deviceId = call.arguments[@"id"];
  //         NSInteger type = [call.arguments[@"type"] integerValue];
  //         [TuyaFirmware upgradeFirmwareWithDeviceId:deviceId type:type
  //         result:result];
  //     }
  else {
    result(FlutterMethodNotImplemented);
  }
}

@end
