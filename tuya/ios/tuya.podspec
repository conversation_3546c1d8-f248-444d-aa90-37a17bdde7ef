#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint tuya.podspec` to validate before publishing.
#
# Pod::Spec.new do |s|
#   s.name             = 'tuya'
#   s.version          = '0.0.1'
#   s.summary          = 'A new Flutter project.'
#   s.description      = <<-DESC
# A new Flutter project.
#                        DESC
#   s.homepage         = 'http://example.com'
#   s.license          = { :file => '../LICENSE' }
#   s.author           = { 'Your Company' => '<EMAIL>' }
#   s.source           = { :path => '.' }
#   s.source_files = 'Classes/**/*'
#   s.dependency 'Flutter'
#   s.dependency 'ThingSmartHomeKit/ThingSmartKit'
#   s.platform = :ios, '10.0'
#
#   #s.xcconfig = { 'SWIFT_OBJC_BRIDGING_HEADER' => 'Classes/bridging_header.h' }
#   # Flutter.framework does not contain a i386 slice.
#   s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
#   s.swift_version = '5.0'
#   #s.static_framework = true
# end


Pod::Spec.new do |s|
  s.name             = 'tuya'
  s.version          = '0.0.1'
  s.summary          = 'A new Flutter project.'
  s.description      = <<-DESC
A new Flutter project.
                       DESC
  s.homepage         = 'http://example.com'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Your Company' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.public_header_files = 'Classes/**/*.h'
  s.dependency 'Flutter'
  s.dependency 'ThingSmartHomeKit'
  s.dependency 'MJExtension'
  s.platform = :ios, '11.0'
  
  s.static_framework = true
  
  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
end

