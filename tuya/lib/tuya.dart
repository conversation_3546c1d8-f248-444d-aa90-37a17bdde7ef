import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';

class Tuya {
  static const MethodChannel _channel = MethodChannel('tuya');

  static Future<String?> get platformVersion async {
    final String? version = await _channel.invokeMethod(
        'getPlatformVersion', <String, dynamic>{"uuid": "asdoufou"});
    return version;
  }

  static Future<String?> login(String phone, String password) async {
    try {
      final res = await _channel.invokeMethod('Login', <String, dynamic>{
        'phone': phone,
        'password': password,
      });
      if (Platform.isAndroid) {
        return res;
      } else if (Platform.isIOS) {
        if (res is String && res == "1") {
          return "success";
        } else {
          return null;
        }
      }
    } catch (e) {
      print(e.toString());
    }
  }

  // static Future<String?> get Login async {
  //   try {
  //     final res = await _channel.invokeMethod('Login');
  //     if (Platform.isAndroid) {
  //       return res;
  //     } else if (Platform.isIOS) {
  //       if (res is String && res == "1") {
  //         return "success";
  //       } else {
  //         return null;
  //       }
  //     }
  //   } catch (e) {
  //     print(e.toString());
  //   }
  // }

  static Future<String?> get getHomeList async {
    try {
      return await _channel.invokeMethod('getHomeList');
    } catch (e) {
      print(e.toString());
    }
  }

  static Future<String?> createHome(String name) async {
    try {
      return await _channel.invokeMethod('createHome', <String, dynamic>{
        'name': name,
      });
    } catch (e) {
      print(e.toString());
    }
  }

  static Future<String?> getHomeDetail(num id) async {
    try {
      return await _channel.invokeMethod('getHomeDetail', <String, dynamic>{
        'id': id,
      });
    } catch (e) {
      print(e.toString());
    }
  }

  static Future<String?> get StartScanDevice async {
    try {
      return await _channel.invokeMethod('StartScanDevice');
    } catch (e) {
      print(e.toString());
    }
  }

  static Future<bool?> get stopScanDevice async {
    try {
      final res = await _channel.invokeMethod('stopScanDevice');
      if (Platform.isAndroid) {
        return res;
      } else if (Platform.isIOS) {
        if (res is String && res == "1") {
          return true;
        } else {
          return false;
        }
      }
    } catch (e) {
      print(e.toString());
    }
  }

  static Future<String?> activeDeviceScanQrcode(
      num homeId, String qrcode) async {
    return await _channel.invokeMethod(
        'scanQrcode', <String, dynamic>{'homeId': homeId, 'qrcode': qrcode});
  }

  static Future<String?> activeDevice(
      num homeId, String uuid, String findScanDeviceBean) async {
    try {
      return await _channel.invokeMethod('activeDevice', <String, dynamic>{
        'homeId': homeId,
        'uuid': uuid,
        'findScanDeviceBean': findScanDeviceBean
      });
    } catch (e) {
      print(e.toString());
      return null;
    }
  }

  static Future<bool?> resetFactory(String uuid) async {
    try {
      final res = await _channel.invokeMethod('resetFactory', <String, dynamic>{
        'uuid': uuid,
      });
      if (Platform.isAndroid) {
        return res;
      } else if (Platform.isIOS) {
        if (res is String && res == "1") {
          return true;
        } else {
          return false;
        }
      }
    } catch (e) {
      print(e.toString());
    }
  }

  static Future<bool?> removeDevice(String uuid) async {
    try {
      final res = await _channel.invokeMethod('removeDevice', <String, dynamic>{
        'uuid': uuid,
      });
      if (Platform.isAndroid) {
        return res;
      } else if (Platform.isIOS) {
        if (res is String && res == "1") {
          return true;
        } else {
          return false;
        }
      }
    } catch (e) {
      print(e.toString());
    }
  }

  static Future<String?> LoginById(String uid) async {
    try {
      final res = await _channel.invokeMethod('LoginById', <String, dynamic>{
        'uid': uid,
      });
      if (Platform.isAndroid) {
        return res;
      } else if (Platform.isIOS) {
        if (res is String && res == "1") {
          return "success";
        } else {
          return null;
        }
      }
    } catch (e) {
      print(e.toString());
    }
  }

  /// 获取固件升级信息
  static Future<String?> getUpgradeInfo(String id) async {
    try {
      final res =
          await _channel.invokeMethod('getUpgradeInfo', <String, dynamic>{
        'id': id,
      });
      return res;
    } catch (e) {
      print(e.toString());
      return null;
    }
  }

  /// 固件升级
  static Future<String?> startUpgrade(String id, int type) async {
    try {
      final res = await _channel.invokeMethod(
          'startUpgrade', <String, dynamic>{'id': id, 'type': type});
      return res;
    } catch (e) {
      print(e.toString());
      return null;
    }
  }

  /// 蓝牙单点配网
  static Future<String?> startActivator(
      num homeId, String uuid, int deviceType) async {
    try {
      final res =
          await _channel.invokeMethod('startActivator', <String, dynamic>{
        'homeId': homeId,
        'uuid': uuid,
        'type': deviceType,
      });
      return res;
    } catch (e) {
      print(e.toString());
      return null;
    }
  }

  /// 蓝牙点点配网取消 仅 android
  static Future<bool> stopActivator(
      int homeId, String uuid, int deviceType) async {
    try {
      final res = await _channel
          .invokeMethod('stopActivator', <String, dynamic>{'uuid': uuid});
      return res;
    } catch (e) {
      print(e.toString());
      return false;
    }
  }

  // registerPushDevice
  static Future<bool> registerPushDevice() async {
    try {
      final res = await _channel.invokeMethod('registerPushDevice');
      return res;
    } catch (e) {
      print(e.toString());
      return false;
    }
  }

  static Future<bool> startInitSdk() async {
    try {
      final res = await _channel.invokeMethod('startInitSdk');
      return res;
    } catch (e) {
      print(e.toString());
      return false;
    }
  }
}
