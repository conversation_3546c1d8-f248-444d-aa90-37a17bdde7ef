import 'package:flutter/material.dart';
import 'dart:async';

import 'package:flutter/services.dart';
import 'package:tuya/tuya.dart';

// import '../../lib/tuya.dart';
import 'package:tuya/tuya.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String _platformVersion = 'Unknown';

  @override
  void initState() {
    super.initState();
    initPlatformState();
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initPlatformState() async {
    String platformVersion;
    // Platform messages may fail, so we use a try/catch PlatformException.
    // We also handle the message potentially returning null.
    // try {
    //   platformVersion =
    //       await Tuya.platformVersion ?? 'Unknown platform version';
    // } on PlatformException {
    //   platformVersion = 'Failed to get platform version.';
    // }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    // if (!mounted) return;
    //
    // setState(() {
    //   _platformVersion = platformVersion;
    // });

    print("++++++++++++++++++++++++++++++++++++++++");

    // print(">>>1、 ${Tuya.platformVersion}" );
    // final res = await Tuya.Login;
    // print(">> login:$res");

    // final res1 = await Tuya.LoginById('ay1650123360526nMAkR');
    // print(">>> loginById: ${res1}" );

    // final res2 = await Tuya.getHomeList;
    // print(">>> getHomeList: ${res2}" );
    // final res3 = await Tuya.getHomeDetail(56165667);
    // print(">>> getHomeDetail:$res3");
    // print(">>>5、 ${Tuya.createHome}" );
    // print(">>>6、 ${Tuya.getHomeDetail}" );
    // print(">>>7、 ${Tuya.StartScanDevice}" );
    // print(">>>8、 ${Tuya.stopScanDevice}" );
    // print(">>>9、 ${Tuya.activeDevice}" );
    // print(">>>10、 ${Tuya.resetFactory}" );
    // print(">>>11、 ${Tuya.removeDevice}" );
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Plugin example app'),
        ),
        body: Center(
          child: Text('Running on: $_platformVersion\n'),
        ),
      ),
    );
  }
}
