PODS:
  - CocoaAsyncSocket (7.6.5)
  - Flutter (1.0.0)
  - Masonry (1.1.0)
  - MJExtension (3.4.1)
  - tuya (0.0.1):
    - Flutter
    - MJExtension
    - ThingSmartHomeKit
  - TuyaDefaultPanelUIKit (0.1.3):
    - Masonry
  - ThingSmartActivatorCoreKit (3.33.2):
    - ThingSmartDeviceCoreKit (>= 3.32.0)
    - ThingSmartMbedTLS (>= 3.33.1)
  - ThingSmartActivatorKit (3.32.5):
    - ThingSmartActivatorCoreKit
    - ThingSmartDeviceKit
  - ThingSmartBaseKit (3.33.2):
    - ThingSmartNetworkKit (>= 3.32.0)
    - YYModel
  - ThingSmartBLECoreKit (3.33.5):
    - ThingSmartUtil (>= 3.32.0)
    - TYBluetooth (< 10.0)
    - YYModel
  - ThingSmartBLEKit (3.33.6):
    - ThingSmartBLECoreKit (>= 3.32.0)
    - ThingSmartDeviceCoreKit (>= 3.32.0)
    - TYBluetooth (>= 3.32.0)
  - ThingSmartBLEMeshKit (3.33.6):
    - ThingSmartActivatorCoreKit (>= 3.32.0)
    - ThingSmartBLEKit (>= 3.32.0)
    - ThingSmartDeviceCoreKit (>= 3.32.0)
  - ThingSmartDefaultPanelKit (2.0.4):
    - TuyaDefaultPanelUIKit
    - ThingSmartDeviceCoreKit
  - ThingSmartDeviceCoreKit (3.33.7):
    - ThingSmartBaseKit (>= 3.32.0)
    - ThingSmartMQTTChannelKit (>= 3.32.0)
    - ThingSmartSocketChannelKit (>= 3.32.0)
    - ThingSmartUtil (>= 3.32.0)
  - ThingSmartDeviceKit (3.33.7):
    - ThingSmartBaseKit
    - ThingSmartDeviceCoreKit (>= 3.32.0-rc1)
    - ThingSmartShareKit
  - ThingSmartFeedbackKit (3.33.5):
    - ThingSmartBaseKit
  - ThingSmartHomeKit (3.33.5):
    - TuyaDefaultPanelUIKit (~> 0.1.0)
    - ThingSmartActivatorCoreKit (~> 3.33.0)
    - ThingSmartActivatorKit (~> 3.32.0)
    - ThingSmartBaseKit (~> 3.33.0)
    - ThingSmartBLECoreKit (~> 3.33.0)
    - ThingSmartBLEKit (~> 3.33.0)
    - ThingSmartBLEMeshKit (~> 3.33.0)
    - ThingSmartDefaultPanelKit (~> 2.0.0)
    - ThingSmartDeviceCoreKit (~> 3.33.0)
    - ThingSmartDeviceKit (~> 3.33.0)
    - ThingSmartFeedbackKit (~> 3.33.0)
    - ThingSmartMbedTLS (~> 3.33.0)
    - ThingSmartMessageKit (~> 3.33.0)
    - ThingSmartMQTTChannelKit (~> 3.32.0)
    - ThingSmartNetworkKit (~> 3.33.0)
    - ThingSmartQUIC (~> 1.2.0)
    - ThingSmartSceneCoreKit (~> 3.33.0)
    - ThingSmartSceneKit (~> 3.33.0)
    - ThingSmartShareKit (~> 1.3.0)
    - ThingSmartSocketChannelKit (~> 3.33.0)
    - ThingSmartTimerKit (~> 3.33.0)
    - ThingSmartUtil (~> 3.32.0)
    - TYBluetooth (~> 3.33.0)
    - TYMbedtls (~> 2.25.0)
  - ThingSmartMbedTLS (3.33.3):
    - TYMbedtls (>= 2.25.1)
  - ThingSmartMessageKit (3.33.5):
    - ThingSmartBaseKit
  - ThingSmartMQTTChannelKit (3.32.0):
    - ThingSmartNetworkKit (>= 3.32.0)
    - ThingSmartQUIC (>= 1.1.4)
  - ThingSmartNetworkKit (3.33.6):
    - ThingSmartUtil (>= 3.32.5)
    - YYModel
  - ThingSmartQUIC (1.2.0)
  - ThingSmartSceneCoreKit (3.33.9):
    - ThingSmartBaseKit
    - ThingSmartDeviceCoreKit
  - ThingSmartSceneKit (3.33.6):
    - ThingSmartBaseKit
    - ThingSmartDeviceKit
    - ThingSmartSceneCoreKit
  - ThingSmartShareKit (1.3.2):
    - ThingSmartBaseKit
  - ThingSmartSocketChannelKit (3.33.0):
    - CocoaAsyncSocket (>= 7.4.3)
    - ThingSmartUtil (>= 3.32.0)
  - ThingSmartTimerKit (3.33.0):
    - ThingSmartBaseKit
    - ThingSmartDeviceCoreKit
  - ThingSmartUtil (3.32.5)
  - TYBluetooth (3.33.0):
    - ThingSmartUtil (>= 3.32.0)
  - TYMbedtls (2.25.2)
  - YYModel (1.0.4)

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - tuya (from `.symlinks/plugins/tuya/ios`)
  - ThingSmartHomeKit (~> 3.33.5)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - CocoaAsyncSocket
    - Masonry
    - MJExtension
    - TuyaDefaultPanelUIKit
    - ThingSmartActivatorCoreKit
    - ThingSmartActivatorKit
    - ThingSmartBaseKit
    - ThingSmartBLECoreKit
    - ThingSmartBLEKit
    - ThingSmartBLEMeshKit
    - ThingSmartDefaultPanelKit
    - ThingSmartDeviceCoreKit
    - ThingSmartDeviceKit
    - ThingSmartFeedbackKit
    - ThingSmartHomeKit
    - ThingSmartMbedTLS
    - ThingSmartMessageKit
    - ThingSmartMQTTChannelKit
    - ThingSmartNetworkKit
    - ThingSmartQUIC
    - ThingSmartSceneCoreKit
    - ThingSmartSceneKit
    - ThingSmartShareKit
    - ThingSmartSocketChannelKit
    - ThingSmartTimerKit
    - ThingSmartUtil
    - TYBluetooth
    - TYMbedtls
    - YYModel

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  tuya:
    :path: ".symlinks/plugins/tuya/ios"

SPEC CHECKSUMS:
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  Flutter: 50d75fe2f02b26cc09d224853bb45737f8b3214a
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  MJExtension: 21c5f6f8c4d5d8844b7ae8fbae08fed0b501f961
  tuya: fead28c95cfde710aa1a2fea250bf27f1af5ffdc
  TuyaDefaultPanelUIKit: 8effb6fa45dc5f827ecc87a94a0d491ef0604f8c
  ThingSmartActivatorCoreKit: bc48a6449762d2a7692dedc4639fb830f0956737
  ThingSmartActivatorKit: cd697fc10965589743a88c6117e804306b03e140
  ThingSmartBaseKit: 41bccb312a050e1813b938a6c6a8d829f2301f3b
  ThingSmartBLECoreKit: ed0ca295177ae96f1dab01e249b98267c422238b
  ThingSmartBLEKit: 9eac1b5fe8abdd681009a284f5df303503092209
  ThingSmartBLEMeshKit: a6487909e090baddf04e4bfa06956567df92962b
  ThingSmartDefaultPanelKit: 77d9d7a286920b140cd3fb8525d17d20b941450d
  ThingSmartDeviceCoreKit: a9723a48250795ec7f9d7412d919a696c29c0dab
  ThingSmartDeviceKit: 7e607b0cd05c4188bc6f27a649bc7ea59d87ffd9
  ThingSmartFeedbackKit: eb12d0767b9f09c6f765fbffa6e06e7f89ce9a90
  ThingSmartHomeKit: 9ffd770424455b2e6d143e95244f86ff8ceae02f
  ThingSmartMbedTLS: 7fb545e0e1cfef546be2c7aafd0ab9f0642976b6
  ThingSmartMessageKit: 21383406bc813526731505cef2a8968039b0c28c
  ThingSmartMQTTChannelKit: 83dd5057d8f32436b079588c049a2038a590c025
  ThingSmartNetworkKit: a3e06b6a66ab12752ac0a5ac15894391a45aa809
  ThingSmartQUIC: fc6d0cc6123734c8268f8c1354697203c8b16a82
  ThingSmartSceneCoreKit: 6008573b03b1bc46580f141dd23b898bd84f66c8
  ThingSmartSceneKit: 1f9cecad018120047200e4c6d81b5732c2690785
  ThingSmartShareKit: 7809ef43c360fa1ec7636659c6dd108db1f25422
  ThingSmartSocketChannelKit: 692000b1139b0c2dfde63aa6f2562668d584a886
  ThingSmartTimerKit: f3bd2c93136edca6d59855c959127992627657fc
  ThingSmartUtil: 2e39e5fefd117767c7b2a4d8e2333afca1072170
  TYBluetooth: e64f41e2e6674c3b7329ddab9049709086d0d36a
  TYMbedtls: de670f7c1a5bb00d658067c76ec19bda4966611c
  YYModel: 2a7fdd96aaa4b86a824e26d0c517de8928c04b30

PODFILE CHECKSUM: 7b2c235693a77befd03b0ed7c0f2a702fc64b0c4

COCOAPODS: 1.11.3
