import UIKit
import Flutter

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
    ThingSmartSDK.sharedInstance().start(withAppKey: "unh4avcat9j4ccuw94wg", secretKey: "7dh889cffkhwpgnyuq97apr5wmnn49sm")
      
      print("******* 测试功能");
      
      temp.start()
      
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}

