/*
 * @Author: 丁健
 * @Date: 2022-04-04 16:05:50
 * @LastEditTime: 2022-04-04 16:05:50
 * @LastEditors: 丁健
 * @Description: 
 * @FilePath: /amap_flutter_search/example/lib/test.dart
 * 可以输入预定的版权声明、个性签名、空行等
 */

import 'package:flutter/material.dart';

class YZTestPage extends StatefulWidget {
  const YZTestPage({Key? key}) : super(key: key);

  @override
  State<YZTestPage> createState() => _YZTestPageState();
}

class _YZTestPageState extends State<YZTestPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Container(),
    );
  }
}
