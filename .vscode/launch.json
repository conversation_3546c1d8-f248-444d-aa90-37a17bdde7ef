{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "rabbit_seat_app",
            "cwd": "rabbit_seat_app",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "rabbit_seat_app (profile mode)",
            "cwd": "rabbit_seat_app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "rabbit_seat_app (release mode)",
            "cwd": "rabbit_seat_app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "tuya",
            "cwd": "tuya",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "tuya (profile mode)",
            "cwd": "tuya",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "tuya (release mode)",
            "cwd": "tuya",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "example",
            "cwd": "tuya\\example",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "example (profile mode)",
            "cwd": "tuya\\example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "example (release mode)",
            "cwd": "tuya\\example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}