import 'dart:convert';

import 'package:rabbit_seat_app/models/device/device_model.dart';

var jsonStr = '''            {
                "id": "c1522165-8078-431d-a7c9-dec1c0f3b195",
                "createBy": "dd6bd640-e8f5-4060-82be-d84988aaee10",
                "createOn": 1753239671804,
                "updateBy": "dd6bd640-e8f5-4060-82be-d84988aaee10",
                "updateOn": 1753240055613,
                "status": "Normal",
                "name": "两只兔子求知2Pro儿童安全座椅",
                "model": "",
                "online": true,
                "ownerId": "*********",
                "originalId": "6c9cac5545732b6da3v6db",
                "lastEventTime": 1753240055613,
                "mcuLpTimer": null,
                "batCharge": true,
                "productId": "6sho1h3rxg4a2bbn",
                "productName": "L511-两只兔子求知2Pro儿童安全座椅",
                "statusJson": "[{\\"code\\":\\"assist_rotate\\",\\"value\\":true},{\\"code\\":\\"auto_rotate\\",\\"value\\":false},{\\"code\\":\\"hotSw\\",\\"value\\":false},{\\"code\\":\\"autoHotTempTH\\",\\"value\\":16.0},{\\"code\\":\\"autoFanTempTH\\",\\"value\\":16.0},{\\"code\\":\\"autoHotTempTL\\",\\"value\\":16.0},{\\"code\\":\\"fanSw\\",\\"value\\":false},{\\"code\\":\\"IMEI\\",\\"value\\":\\"ODY5MjE3MDcyOTQ2MDIz\\"},{\\"code\\":\\"mcuLpTimer\\",\\"value\\":5},{\\"code\\":\\"autoFanTempTL\\",\\"value\\":16.0},{\\"code\\":\\"recTemp\\",\\"value\\":29.0},{\\"code\\":\\"batPercent\\",\\"value\\":30.0},{\\"code\\":\\"batCharge\\",\\"value\\":true},{\\"code\\":\\"leaveWarm\\",\\"value\\":false},{\\"code\\":\\"rssi\\",\\"value\\":0.0},{\\"code\\":\\"batWarm\\",\\"value\\":false},{\\"code\\":\\"fanWarm\\",\\"value\\":false},{\\"code\\":\\"hotWarm\\",\\"value\\":false},{\\"code\\":\\"protectionLeftSw\\",\\"value\\":true},{\\"code\\":\\"protectionRightSw\\",\\"value\\":true},{\\"code\\":\\"leftProtectionWarm\\",\\"value\\":false},{\\"code\\":\\"autoMode\\",\\"value\\":true},{\\"code\\":\\"rightProtectionWarm\\",\\"value\\":false},{\\"code\\":\\"voiceModuleVersion\\",\\"value\\":\\"\\"},{\\"code\\":\\"ICCID\\",\\"value\\":\\"ODk4NjA4MzYxOTI0RDM4MTY4MDU=\\"},{\\"code\\":\\"trafficSw\\",\\"value\\":true},{\\"code\\":\\"sleepTimeSet\\",\\"value\\":5.0},{\\"code\\":\\"leaveWarmTimeSet\\",\\"value\\":5.0},{\\"code\\":\\"noLoadModeRunTimeSet\\",\\"value\\":5.0},{\\"code\\":\\"F_light\\",\\"value\\":true},{\\"code\\":\\"GNSS\\",\\"value\\":\\"\\"},{\\"code\\":\\"isRemoteMode\\",\\"value\\":false},{\\"code\\":\\"cloudUnbund\\",\\"value\\":false},{\\"code\\":\\"hardWareVersion\\",\\"value\\":\\"Ni4wLjE=\\"},{\\"code\\":\\"seaton\\",\\"value\\":false},{\\"code\\":\\"mcusleep\\",\\"value\\":false}]",
                "uuid": "51e6ee473b6ce3af",
                "activeTime": 1753239660000,
                "statusList": [  
                
                  {
                "code": "lptime_onoff",
                "value": false
            },
                {
                "code": "err_value",
                "value": 0
            },
            {
                "code": "auto_fan_temp",
                "value": 28
            },
            {
                "code": "auto_heat_temp",
                "value": 13
            },
            {
                "code": "mute_mode_switch",
                "value": false
            },
            {
                "code": "auto_rotate",
                "value": true
            },
            {
                "code": "assist_rotate",
                "value": true
            },
            {
                "code": "hotSw",
                "value": false
            },
            {
                "code": "autoHotTempTH",
                "value": 16.0
            },
            {
                "code": "autoFanTempTH",
                "value": 16.0
            },
            {
                "code": "autoHotTempTL",
                "value": 16.0
            },
            {
                "code": "fanSw",
                "value": false
            },
            {
                "code": "IMEI",
                "value": "ODY4MzQ3MDUwNjcxMDg2"
            },
            {
                "code": "mcuLpTimer",
                "value": 5
            },
            {
                "code": "autoFanTempTL",
                "value": 16.0
            },
            {
                "code": "recTemp",
                "value": 28.0
            },
            {
                "code": "batPercent",
                "value": 10.0
            },
            {
                "code": "batCharge",
                "value": true
            },
            {
                "code": "leaveWarm",
                "value": false
            },
            {
                "code": "rssi",
                "value": 2.0
            },
            {
                "code": "batWarm",
                "value": false
            },
            {
                "code": "fanWarm",
                "value": false
            },
            {
                "code": "hotWarm",
                "value": false
            },
            {
                "code": "protectionLeftSw",
                "value": true
            },
            {
                "code": "protectionRightSw",
                "value": false
            },
            {
                "code": "leftProtectionWarm",
                "value": false
            },
            {
                "code": "autoMode",
                "value": true
            },
            {
                "code": "rightProtectionWarm",
                "value": false
            },
            {
                "code": "voiceModuleVersion",
                "value": ""
            },
            {
                "code": "ICCID",
                "value": "ODk4NjA4NDExOTI0QzA1MTU1OTM="
            },
            {
                "code": "trafficSw",
                "value": true
            },
            {
                "code": "sleepTimeSet",
                "value": 5.0
            },
            {
                "code": "leaveWarmTimeSet",
                "value": 5.0
            },
            {
                "code": "noLoadModeRunTimeSet",
                "value": 5.0
            },
            {
                "code": "F_light",
                "value": true
            },
            {
                "code": "GNSS",
                "value": ""
            },
            {
                "code": "isRemoteMode",
                "value": false
            },
            {
                "code": "cloudUnbund",
                "value": false
            },
            {
                "code": "hardWareVersion",
                "value": "NS4wLjE="
            },
            {
                "code": "seaton",
                "value": true
            },
            {
                "code": "mcusleep",
                "value": false
            }
        ],
                "lat": "",
                "lon": "",
                "ip": "**************",
                "icon": "https://images.tuyacn.com/smart/icon/bay1624877255379LFDK/b80c2ea348291084ae202b2dc90df019.png",
                "homeId": *********,
                "user": {
                    "id": "dd6bd640-e8f5-4060-82be-d84988aaee10",
                    "createBy": "dd6bd640-e8f5-4060-82be-d84988aaee10",
                    "createOn": 1748947278840,
                    "updateBy": "dd6bd640-e8f5-4060-82be-d84988aaee10",
                    "updateOn": 1748947278840,
                    "status": "Normal",
                    "name": null,
                    "tuyaId": "ay1748947278676wPry8",
                    "avatar": null,
                    "sex": 0,
                    "nickName": "两只兔子v80A07",
                    "city": null,
                    "telephone": "13330230489",
                    "emergencyContacts": null,
                    "babyInfos": null,
                    "openId": "oK_165sC_0Y-Ns3qnIQ5B78DOb4o",
                    "hasNewAppComment": null,
                    "lastLoginOn": 1748947278553,
                    "isBlockCall": null
                },
                "userName": null,
                "userTelephone": null,
                "deviceStatus": "Actived",
                "configJson": null,
                "iccid": "ODk4NjA4MzYxOTI0RDM4MTY4MDU=",
                "cityInfo": null,
                "leaveWarnStatus": "Normal",
                "leaveWarnUpdateTime": null,
                "leaveWarnCount": 0,
                "isShare": false,
                "manualWarn": false,
                "expiredTime": "2028-04-30 23:59:59",
                "remoteCommandsJson": null,
                "remoteExpiredTime": null,
                "mcuVersion": "6.0.1",
                "networkVersion": "0.3.2",
                "batWarmStatus": null,
                "mcusleep": null,
                "warningCallCount": 0,
                "callId": null,
                "lastCallTime": null,
                "emergencyCallId": null,
                "leaveWarnValue": null
            }''';
var testDevice =
    DeviceModel.fromJson(json.decode(jsonStr) as Map<String, dynamic>);

var testDevice2 =
    DeviceModel.fromJson(json.decode(jsonStr) as Map<String, dynamic>)
      ..online = false
      ..name = "测试座椅2";

var testDevices = [testDevice, testDevice2];
