import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/common/generate_titles.dart';
import 'package:rabbit_seat_app/provider/device_provider.dart';
import 'package:rabbit_seat_app/provider/local_provider.dart';
import 'package:rabbit_seat_app/provider/profile_provider.dart';
import 'package:rabbit_seat_app/provider/theme_provider.dart';
import 'package:rabbit_seat_app/provider/user_provider.dart';
import 'package:rabbit_seat_app/routes/routes.dart';
import 'package:rabbit_seat_app/utils/global.dart';
import 'package:rabbit_seat_app/utils/oss/OSSClient.dart';
import 'package:rabbit_seat_app/utils/oss/ossmodels.dart';
import 'package:rabbit_seat_app/views/launch/launch_view.dart';
import 'package:rabbit_seat_app/views/login/index.dart';
import 'package:rabbit_seat_app/views/main_view.dart';
import 'common/global_title_delegate.dart';

void main() {
  // runApp(const MyApp());

  /*
    region: 'oss-cn-hangzhou',
    accessKeyId: 'LTAI5t7Gc8BQNhmr5kAR4nG8',
    accessKeySecret: '******************************',
    bucket: 'rabbit-app-oss',
   */
  // 初始化OSSClient
  OSSClient.init(
    endpoint: 'oss-cn-hangzhou.aliyuncs.com',
    bucket: 'rabbit-app-oss',
    credentials: () async {
      return Credentials(
        accessKeyId: 'LTAI5t7Gc8BQNhmr5kAR4nG8',
        accessKeySecret: '******************************',
      );
    },
  );

  Global.init().then((e) async {
    runApp(MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: ThemeProvider()),
        ChangeNotifierProvider.value(value: LocaleProvider()),
        ChangeNotifierProvider.value(value: UserProvider()),
        ChangeNotifierProvider.value(value: ProfileProvider()),
        ChangeNotifierProvider.value(value: DeviceProvider()),
      ],
      child: const MyApp(),
    ));
  });

  // MEventChannel().config((arg) {
  //   print(">>> MEventChannel arg:$arg");
  // });
}

final GlobalKey<NavigatorState> navigatorKey = new GlobalKey<NavigatorState>();

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 初始化路由
    String _initRoute = '/';
    print(">>> token:${Global.profile.token}");
    bool? _agreement = Global().sharedPreferences.getBool('agreementKey');
    if (Platform.isAndroid && (_agreement == null || _agreement == false)) {
      _initRoute = '/launch';
    } else {
      if (Global.profile.token == null || Global.profile.token!.isEmpty) {
        _initRoute = '/login-code';
      }
    }

    // initChanel();
    return Consumer2<ThemeProvider, LocaleProvider>(
      builder: (BuildContext context, themeProvider, localeProvider, child) {
        return ScreenUtilInit(
            designSize: const Size(375, 812),
            minTextAdapt: true,
            splitScreenMode: true,
            builder: (context, child) {
              return MaterialApp(
                theme: new ThemeData(
                  primarySwatch: Colors.blue,
                  primaryColor: Business.mainColor
                ),
                onGenerateTitle: (context) {
                  return GenerateTitles.of(context)!.title;
                },
                debugShowCheckedModeBanner: false,
                showSemanticsDebugger: false,
                locale: localeProvider.getLocale(),
                //我们只支持美国英语和中文简体
                supportedLocales: const [
                  Locale('en', 'US'), // 美国英语
                  Locale('zh', 'CN'), // 中文简体
                  //其它Locales
                ],
                localizationsDelegates: const [
                  // 本地化的代理类
                  GlobalMaterialLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalTitleDelegate()
                ],
                localeResolutionCallback: (_locale, supportedLocales) {
                  if (localeProvider.getLocale() != null) {
                    //如果已经选定语言，则不跟随系统
                    return localeProvider.getLocale();
                  } else {
                    //跟随系统
                    Locale locale;
                    if (supportedLocales.contains(_locale)) {
                      locale = _locale!;
                    } else {
                      //如果系统语言不是中文简体或美国英语，则默认使用美国英语

                      locale = const Locale('zh', 'CN');
                    }
                    return locale;
                  }
                },
                // home: UITest(),
                // 初始化路由
                initialRoute: _initRoute,
                onGenerateInitialRoutes: (name) {
                  //是数组，因为初始化页面可以是/a/b
                  switch (_initRoute) {
                    case '/':
                      return [
                        MaterialPageRoute(builder: (context) {
                          return MainView();
                        })
                      ];
                    case '/launch':
                      return [
                        MaterialPageRoute(builder: (context) {
                          return LaunchPage();
                        })
                      ];
                    case '/login-code':
                      return [
                        MaterialPageRoute(builder: (context) {
                          return LoginPhoneView();
                        })
                      ];
                  }
                  return [
                    MaterialPageRoute(builder: (context) {
                      return MainView();
                    })
                  ];
                },
                // 注册路由表
                routes: RoutesUtils.routes,
                // 路由选择
                onGenerateRoute: RoutesUtils.onGenerateRoute,
                // 注册弹窗
                builder: EasyLoading.init(),
                navigatorKey: navigatorKey,
              );
            });
      },
    );
  }

  //初始化通道
  Future<void> initChanel() async {
    try {
      var XiaoMiEventChannel =
          const EventChannel("com.rabbit.seat.flutterEvent");

      //注册监听
      XiaoMiEventChannel.receiveBroadcastStream().listen(
          (dynamic msgData) async {
        print("receiveRemoteMsg main");
        // EasyLoading.showToast(msgData);
      }, onError: (Object error) {
        print("XiaoMiDataChannel---->" + error.toString());
      });
    } catch (e) {
      print(e);
    }
  }
}
