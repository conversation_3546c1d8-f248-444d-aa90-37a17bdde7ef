import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:rabbit_seat_app/models/circle/fileModel.dart';
import 'package:rabbit_seat_app/views/circle/ImgPreview.dart';
import 'package:rabbit_seat_app/views/common/video_play_view.dart';

class UI {
  static Widget bigImageWidget(BuildContext context, FileModel model) {
    return AspectRatio(
      aspectRatio: 1.778,
      child: GestureDetector(
        onTap: () {
          print("大张图片点击");
          if (model.type == 0) {
            Navigator.push(context,
                MaterialPageRoute(builder: (_) => ImgPreview(items: [model])));
          } else {
            Navigator.push(context, MaterialPageRoute(builder: (_) => VideoPlayView(url: model.fileUrl)));
          }
        },
        child: Container(
          decoration: BoxDecoration(
            color: Color(0x4F000000),
            border: Border.all(color: Color(0xFFF5F5F5), width: 1),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            child: model.thumbnailUrl != null
                ? Stack(
              children: [
                Positioned.fill(child: CachedNetworkImage(
                  fit: BoxFit.cover,
                  imageUrl: model.thumbnailUrl!,
                ),),
                // 播放按钮
                model.type == 1
                    ? Align(
                  child: Icon(
                    Icons.play_circle_outline,
                    color: Colors.white,
                    size: 40,
                  ),
                )
                    : SizedBox(),
              ],
            )
                : Container(color: Colors.grey),
          ),
        ),
      ),
    );
  }
}