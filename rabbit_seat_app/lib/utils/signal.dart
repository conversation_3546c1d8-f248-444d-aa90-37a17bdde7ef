
import 'package:flutter/material.dart';

// 信号回调函数
typedef SignalCallback = void Function(dynamic arg);

class KitSignal {

  // 自定义私有构造器
  KitSignal._internal();

  // 单利
  static  KitSignal _instance = new KitSignal._internal();

  // 工程构造函数，返回单利
  factory KitSignal() => _instance;

  // 信号监听者们
  Map<String, Set<String>> _keys = new Map<String, Set<String>>();
  Map<String, SignalCallback?> _subscribers = new Map<String, SignalCallback?>();

  // Map<String, List<Map<String, SignalCallback>>> _subscribers = new Map<String, List<Map<String, SignalCallback>>>();

  // 添加订阅者
  void link(String type, String tag, SignalCallback back) {
    // 判断订阅类型和回调函数是否为空
    if (type.isEmpty || link == null) return;
    // 判断订阅类型集合是否为空，如果为空则初始化
    if (_keys.containsKey(type) == true) {
      (_keys[type] as Set).add(tag);
      _subscribers.addAll({tag : back});
    } else {
      _keys.addAll({type : {tag}});
      _subscribers.addAll({tag : back});
    }
  }

  // 移除订阅者
  void off(String type, String tag, {SignalCallback? f}) {
    if (_keys.containsKey(type) == false) return;
    // 获取到订阅者类型集合
    Set<String> _kys = _keys[type]!;
    _kys.forEach((element) {
      if (element == tag) {
        _subscribers[tag] = null;
      }
    });
  }

  // 发送信号
  void send(String type, [arg]) {
    if (_keys.containsKey(type) == false) return;
    // 获取到订阅者类型集合
    Set<String> _kys = _keys[type]!;
    _kys.forEach((element) {
      SignalCallback? _back = _subscribers[element];
      if (_back != null) _back(arg);
    });
  }
}