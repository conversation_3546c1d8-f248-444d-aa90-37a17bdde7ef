import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:city_pickers/city_pickers.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:open_file/open_file.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/models/version/apkInfoModel.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:url_launcher/url_launcher.dart';

import '../widgets/dialog/dialog.dart';

class AppVersion {
  // 单利
  static final AppVersion _instance = AppVersion._instanal();

  factory AppVersion() => _instance;

  // 进度Dialog控制
  late BuildContext _progressContext;

  // 进度刷新控制
  late StateSetter _progressSetter;

  String? _aplUrl;

  int _length = 0;
  int _total = 0;

  // 下次再说
  bool _isNextAgain = false;

  // 是否在加载中
  bool _isLoging = false;

  AppVersion._instanal() {}

  // 检测安卓版本
  void checkAndroidVersion(BuildContext context) async {
    print(">>>> checkAndroidVersion");
    if (Platform.isAndroid) {
      // 下载再说
      if (_isNextAgain == true) return;
      // 在操作中
      if (_isLoging == true) return;
      _isLoging = true;

      /// 检测版本，下载Apk，并安装
      try {
        /// 加载本地版本信息
        PackageInfo packageInfo = await PackageInfo.fromPlatform();
        String localVersion = packageInfo.version;
        print("localVersion:$localVersion");

        // EasyLoading.show();

        /// 加载网络版本信息
        final data = await Http.get('/api/appUpgrade/androidNewest');
        final result = ResultModel<ApkInfoModel>.fromJson(data,
            (json) => ApkInfoModel.fromJson(json as Map<String, dynamic>));
        if (result.code == 200) {
          if (result.data != null) {
            ApkInfoModel info = result.data as ApkInfoModel;

            _aplUrl = info.file!.fileUrl;

            /// 比对版本
            if (info.version != null) {
              String _v1 = info.version!;
              String _v2 = localVersion;
              bool _isUpdate = false;
              List<String> sp1 = _v1.split('.'); // 网络
              List<String> sp2 = _v2.split('.'); // 本地
              for (int i = 0; i < 3; i++) {
                int _m1 = 0;
                int _m2 = 0;
                if (i < sp1.length) _m1 = int.tryParse(sp1[i] as String) ?? 0;
                if (i < sp2.length) _m2 = int.tryParse(sp2[i] as String) ?? 0;
                if (_m1 > _m2) {
                  _isUpdate = true;
                  break;
                } else if (_m2 > _m1) {
                  break;
                }
              }
              if (_isUpdate) {
                /// 服务器版本大于当前版本，提示跟新版本
                showUpdateDialog(context, info);
              } else {
                print("没有新版本");
              }
            } else {
              /// 版本一致，不处理
            }
          }
        } else {
          _isLoging = false;
          print(">>> Load Android Version Message:${result.message}");
        }
      } catch (error) {
        // EasyLoading.dismiss();
        /// 不处理
        _isLoging = false;
        print("Android version check error!");
      }
    }
  }

  /**
   * 显示更新对话框
   */
  void showUpdateDialog(BuildContext context, ApkInfoModel info) async {
    DefDialog.showDialog1(
        context: context,
        title: "检测到新版本V${info.version}",
        message: info.content,
        //"是否要更新到新版本？",
        cancelText: '下次再说',
        confirmText: '立即更新',
        confirm: () async {
          /// 存储权限
          Permission _storagePermission = await Permission.storage;

          /// 获取权限
          PermissionStatus _storageStatus = await _storagePermission.status;
          if (_storageStatus != PermissionStatus.granted) {
            _storageStatus =
                await _storagePermission.request() as PermissionStatus;
          }

          if (_storageStatus != PermissionStatus.granted) {
            _isLoging = false;
            EasyLoading.showToast('未授权存储权限，将前往web更新。');
            // 弹出网页
            Future.delayed(Duration(milliseconds: 500), () {
              launch(kReleaseBaseUrl + _aplUrl!);
            });
            return;
          }

          /// 软件内部安装权限
          Permission _installPermission = Permission.requestInstallPackages;
          PermissionStatus _installstatus = await _installPermission.status;
          if (_installstatus != PermissionStatus.granted) {
            _installstatus =
                await _installPermission.request() as PermissionStatus;
          }
          // 判断存储权限与内部安装权限
          if (_installstatus != PermissionStatus.granted) {
            _isLoging = false;
            EasyLoading.showToast('未授权应用安装，将前往web更新。');
            // 弹出网页
            Future.delayed(Duration(milliseconds: 500), () {
              launch(kReleaseBaseUrl + _aplUrl!);
            });
            return;
          }

          doVersionUpdate(context);
        },
        cancel: () {
          _isLoging = false;
          _isNextAgain = true;
        });
  }

  /**
   * 做版本更新
   */
  void doVersionUpdate(BuildContext context) async {
    // 检测平台
    if (Platform.isAndroid) {
      print("网络状态：---++++++++----");

      /// 检测网络状态
      final connectivityResult = await Connectivity().checkConnectivity();
      // 流量 下载提醒
      if (connectivityResult == ConnectivityResult.mobile) {
        print("流量 下载提醒");

        /// 提示用户网络状态
        DefDialog.showDialog1(
          context: context,
          title: '提示',
          message: '当前非Wifi网络，是否继续更新到新版本？',
          confirm: () {
            /// 获取权限
            doDownloadApk(context);
          },
          cancel: () {
            _isLoging = false;
          },
        );
      }
      // wifi 直接下载
      else if (connectivityResult == ConnectivityResult.wifi) {
        print("wifi 直接下载");
        doDownloadApk(context);
      }
      // 网络不可用
      else {
        _isLoging = false;
        EasyLoading.showToast("当前网络不可用，请检查网络！");
      }
    }
  }

  /**
   * 下载apk
   */
  void doDownloadApk(BuildContext context) async {
    // 检测平台
    if (Platform.isAndroid) {
      Permission permission = await Permission.storage;

      /// 获取权限
      PermissionStatus status = await permission.status;
      if (status != PermissionStatus.granted) {
        status = await permission.request() as PermissionStatus;
      }
      print("权限：${status}");
      if (status == PermissionStatus.granted) {
        print("已授权读写权限，开始下载");

        /// 已授权读写权限，开始下载
        /// 获取到apk存储路径
        String? savePath = await apkLocalPath();
        print("savePath:$savePath");
        if (savePath != null) {
          /// 显示弹窗
          showProgressDialog(context);

          try {
            /// 下载
            final response = await Dio().download(
              kReleaseBaseUrl + _aplUrl!,
              savePath,
              onReceiveProgress: (int count, int total) {
                _total = total;

                /// 进度刷新
                _progressSetter(() {
                  _length = count;
                });
                print("Progress >> ${count / total}");
              },
            );

            /// 判断响应状态
            if (response.statusCode == HttpStatus.ok) {
              /// 关闭进度提示
              Navigator.pop(_progressContext);

              /// 安装apk
              installApk();
            } else {
              /// 关闭进度提示
              Navigator.pop(_progressContext);

              /// 提示
              EasyLoading.showToast("下载失败！");

              _isLoging = false;
            }
          } catch (e) {
            print(">>> download apk error:$e");
            _isLoging = false;

            /// 关闭进度提示
            Navigator.pop(_progressContext);

            /// 提示
            EasyLoading.showToast("下载失败！");
          }
        }
      }
      // 弹出网页，从网页下载
      else {
        _isLoging = false;
        EasyLoading.showToast('未授权应用读写存储，将前往web更新。');
        // 弹出网页
        Future.delayed(Duration(milliseconds: 500), () {
          launch(kReleaseBaseUrl + _aplUrl!);
        });
      }
    }
  }

  /**
   * 显示进度对话框
   */
  void showProgressDialog(BuildContext context) {
    // 平台
    if (Platform.isAndroid) {
      /// 显示兑换框
      showDialog(
        context: context,
        // 空白处不能销毁对话框
        barrierDismissible: false,
        builder: (context) {
          _progressContext = context;
          return StatefulBuilder(builder: (_, setState) {
            _progressSetter = setState;
            return WillPopScope(
              onWillPop: () async => false,
              child: Container(
                child: Center(
                  child: Container(
                    width: 240,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                    ),
                    padding: EdgeInsets.only(
                        left: 20, top: 15, bottom: 15, right: 15),
                    child: Row(
                      children: [
                        SizedBox(
                          height: 32,
                          width: 32,
                          child: CircularProgressIndicator(
                            strokeWidth: 3,
                            backgroundColor: Colors.grey[200],
                            valueColor:
                                AlwaysStoppedAnimation(Business.mainColor),
                          ),
                        ),
                        SizedBox(
                          width: 15,
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              "下载中，请稍后……",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xff333333),
                                decoration: TextDecoration.none,
                              ),
                            ),
                            SizedBox(
                              height: 7,
                            ),
                            Text(
                              "${((_length / _total * 100).toStringAsFixed(0) + "%")}",
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Color(0xff666666),
                                decoration: TextDecoration.none,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          });
        },
      );
    }
  }

  /**
   * 获取apk存放地址（外部存储目录路径）
   */
  Future<String?> apkLocalPath() async {
    /// 设备判断
    if (Platform.isAndroid) {
      /// 获取外部存储目录
      Directory? directory = await getExternalStorageDirectory();
      if (directory == null) {
        return null;
      }
      return directory.path + '/' + "两只兔子.apk";
    }
    return null;
  }

  /**
   * 安装Apk
   */
  Future<void> installApk() async {
    /// 平台判断
    if (Platform.isAndroid) {
      Permission permission = Permission.requestInstallPackages;
      PermissionStatus status = await permission.status;
      if (status != PermissionStatus.granted) {
        status = await permission.request() as PermissionStatus;
      }
      if (status == PermissionStatus.granted) {
        /// 获取到apk存在路径
        String? path = await apkLocalPath();
        if (path != null) {
          /// 打开apk
          await OpenFile.open(path);
        }
      } else {
        _isLoging = false;
        EasyLoading.showToast("未允许应用程序请求安装包。");
        // 弹出网页
        Future.delayed(Duration(milliseconds: 500), () {
          launch(kReleaseBaseUrl + _aplUrl!);
        });
      }
    }
  }

  /**
   * 检测iOS版本信息内容
   */
  Future<void> checkIOSVersion(BuildContext context) async {
    if (Platform.isAndroid) return;

    // 下载再说
    if (_isNextAgain == true) return;
    // 在操作中kexin
    if (_isLoging == true) return;
    _isLoging = true;

    // http://itunes.apple.com/lookup?id=1636068599
    try {
      /// 加载网络版本信息

      final response =
          await Dio().get('http://itunes.apple.com/lookup?id=1636068599');
      if (response.statusCode == 200) {
        final data = jsonDecode(response.data);
        final _resultCount = data['resultCount'];
        if (_resultCount is num && _resultCount.toInt() == 1) {
          if (data['results'] != null && data['results'] is List) {
            Map<String, dynamic> _rls =
                (data['results'] as List).first as Map<String, dynamic>;
            // 版本号
            final _version = _rls["version"];
            // 版本信息
            final _releaseNotes = _rls["releaseNotes"];
            // 更新地址
            final _trackViewUrl = _rls["trackViewUrl"];

            /// 加载本地版本信息
            PackageInfo packageInfo = await PackageInfo.fromPlatform();
            String localVersion = packageInfo.version;

            final res = compareVersions(_version, localVersion);
            if (res) {
              showIOSAppUpdateDialog(
                  context, _version, _releaseNotes, _trackViewUrl);
            }
          }
        }
      }
      _isLoging = false;
      return;
    } catch (e) {
      _isLoging = false;
      return;
    }
  }

  /// 显示iOS版本更新提示框
  void showIOSAppUpdateDialog(
      BuildContext context, String version, String content, String url) {
    DefDialog.showDialog1(
        context: context,
        title: "检测到新版本V${version}",
        message: content,
        //"是否要更新到新版本？",
        cancelText: '下次再说',
        confirmText: '立即更新',
        confirm: () async {
          launchUrl(Uri.parse(url));
          _isLoging = false;
        },
        cancel: () {
          _isLoging = false;
          _isNextAgain = true;
        });
  }

  /// nv 是否大于 lv
  /// nv: 网络版本
  /// lv: 本地版本
  bool compareVersions(String nv, String lv) {
    bool _isUpdate = false;
    List<String> sp1 = nv.split('.'); // 网络
    List<String> sp2 = lv.split('.'); // 本地
    for (int i = 0; i < max(sp1.length, sp2.length); i++) {
      int _m1 = 0;
      int _m2 = 0;
      if (i < sp1.length) _m1 = int.tryParse(sp1[i] as String) ?? 0;
      if (i < sp2.length) _m2 = int.tryParse(sp2[i] as String) ?? 0;
      if (_m1 > _m2) {
        _isUpdate = true;
        break;
      } else if (_m2 > _m1) {
        break;
      }
    }
    if (_isUpdate) {
      /// 服务器版本大于当前版本，提示跟新版本
      return true;
    } else {
      print("没有新版本");
      return false;
    }
  }
}

/**
 * 安卓版本更新业务流程：
    1、请求接口，加载版本信息
    2、比对版本，版本不一致，显示更新提示框（取消，确定）
    3、更新-> 检测网络状态
    1、4G，提示非wifi网络，是否继续下载更新（取消，确定）
    2、wifi，直接下载
    4、下载->弹出下载进度提示框，检测磁盘读写权限
    1、授权读写，下载安装包
    2、未授权，弹出网页，用户自己下载，安装
    5、下载完成->关闭下载进度提示框，检测应用程序请求安装包权限
    1、授权，安装apk
    2、未授权，弹出网页，用户自己下载，安装
 */
