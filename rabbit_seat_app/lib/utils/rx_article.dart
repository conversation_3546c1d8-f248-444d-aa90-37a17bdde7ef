import 'dart:async';

import 'package:rxdart/rxdart.dart';

/**
 * 文章控制信号
 */

/// 多个订阅留
StreamController stream1 = StreamController.broadcast();

class RxArticle {
  /// 文章点赞状态
  static final PublishSubject articleThumbsUpSubject = PublishSubject<Map<String, dynamic>>();

  /// 圈子文章举报
  // static final PublishSubject articleReportSubject = PublishSubject<Map<String, dynamic>>();

  /// 圈子文章开放评论状态
  static final PublishSubject articleCommentsStateSubject = PublishSubject<Map<String, dynamic>>();

  /// 圈子文章刷新
  static final PublishSubject articleRefreshSubject = PublishSubject<bool>();

  /// 圈子文章删除
  static final PublishSubject articleDeleteSubject = PublishSubject<Map<String, dynamic>>();

  /// 清除缓存
  static void cancel() {
    articleThumbsUpSubject.close();
    // articleReportSubject.close();
    articleCommentsStateSubject.close();
    articleRefreshSubject.close();
  }
}
