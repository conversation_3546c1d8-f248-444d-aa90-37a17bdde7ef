

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';


typedef void EventChannelCallback(arg);

class MEventChannel {
  // 初始化一个广播流从channel中接收数据，返回的Stream调用listen方法完成注册，
  // 需要在页面销毁时调用Stream的cancel方法取消监听
  StreamSubscription? _subscription;

  static final MEventChannel _instance = MEventChannel._internal();
  // 提供了一个工厂方法来获取该类的实例
  factory MEventChannel() {
    return _instance;
  }

  // 通过私有方法_internal()隐藏了构造方法，防止被误创建
  MEventChannel._internal() {
    // 初始化
  }

  // void config(EventChannelCallback callback) {
  //   var c = const EventChannel('com.rabbit.seat.flutterEvent').receiveBroadcastStream();
  //   _subscription = c.listen(callback, onError: _onToDartError);
  // }

  void _onToDartError(error) {
    print(">>> _onToDartError:$error");
  }

  void dispose() {
    if (_subscription != null) {
      _subscription?.cancel();
      _subscription = null;
    }
  }
}