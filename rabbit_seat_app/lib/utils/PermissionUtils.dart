
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:permission_handler/permission_handler.dart';

import '../widgets/dialog/dialog.dart';


/**
 * 权限检测
 */
class PermissionUtils {

  // /// 麦克风权限验证
  // static Future<bool> checkMicrophonePermission(BuildContext context) async {
  //   try {
  //     /// 麦克风权限
  //     Permission permission = Permission.microphone;
  //     bool auth = await permission.request().isGranted;
  //     if (auth) {
  //       return true;
  //     } else {
  //       PermissionStatus status = await Permission.microphone.status;
  //       /// 权限拒绝，
  //       if (status.isDenied) {
  //         openAppSettingsByDialog(context, message: "您已经拒绝权限，无法访问麦克风权限，是否前往设置开启？");
  //       }
  //       /// 权限永久拒绝，且不在提示，需要进入设置界面
  //       else if (status.isPermanentlyDenied) {
  //         openAppSettingsByDialog(context, message: "您已经拒绝权限，无法访问麦克风权限，是否前往设置开启？");
  //       }
  //       /// 活动限制,仅在iOS以上受支持。
  //       else if (status.isRestricted) {
  //         openAppSettingsByDialog(context, message: "麦克风访问权限受限，是否前往设置开启？");
  //       }
  //       return false;
  //     }
  //   } catch (e) {
  //     EasyLoading.showToast(e.toString());
  //     return false;
  //   }
  // }
  //
  // /// 获取相机权限
  // static Future<bool> checkCameraPermission(BuildContext context) async {
  //   try {
  //     Permission permission = Permission.camera;
  //     bool auth = await permission.request().isGranted;
  //     if (auth) {
  //       return true;
  //     } else {
  //       /// 获取授权状态
  //       PermissionStatus status = await permission.status;
  //       /// 权限拒绝，
  //       if (status.isDenied) {
  //         openAppSettingsByDialog(context, message: "您已经拒绝权限，无法访问相机权限，是否前往设置开启？");
  //       }
  //       /// 权限永久拒绝，且不在提示，需要进入设置界面
  //       else if (status.isPermanentlyDenied) {
  //         openAppSettingsByDialog(context, message: "您已经拒绝权限，无法访问相机权限，是否前往设置开启？");
  //       }
  //       /// 活动限制,仅在iOS以上受支持。
  //       else if (status.isRestricted) {
  //         openAppSettingsByDialog(context, message: "相机访问权限受限，是否前往设置开启？");
  //       }
  //       return false;
  //     }
  //   } catch (e) {
  //     EasyLoading.showToast(e.toString());
  //     return false;
  //   }
  // }
  //
  // /// 检测 Android 存储权限
  // static Future<bool> checkStoragePermission(BuildContext context) async {
  //   try {
  //     Permission permission = Permission.storage;
  //     bool auth = await permission.request().isGranted;
  //     if (auth) {
  //       return true;
  //     } else {
  //       /// 获取授权状态
  //       PermissionStatus status = await permission.status;
  //       /// 权限拒绝，
  //       if (status.isDenied) {
  //         openAppSettingsByDialog(context, message: "您已经拒绝权限，无法访问存储权限，是否前往设置开启？");
  //       }
  //       /// 权限永久拒绝，且不在提示，需要进入设置界面
  //       else if (status.isPermanentlyDenied) {
  //         openAppSettingsByDialog(context, message: "您已经拒绝权限，无法访问存储权限，是否前往设置开启？");
  //       }
  //       return false;
  //     }
  //   } catch (e) {
  //     EasyLoading.showToast(e.toString());
  //     return false;
  //   }
  // }

  // /// 获取相机权限
  // static Future<bool> checkPhotosPermission(BuildContext context) async {
  //   try {
  //     Permission permission = Permission.photos;
  //     bool auth = await permission.request().isGranted;
  //     if (auth) {
  //       return true;
  //     } else {
  //       /// 获取授权状态
  //       PermissionStatus status = await permission.status;
  //       /// 权限拒绝，
  //       if (status.isDenied) {
  //         openAppSettingsByDialog(context, message: "您已经拒绝权限，无法访问相册权限，是否前往设置开启？");
  //       }
  //       /// 权限永久拒绝，且不在提示，需要进入设置界面
  //       else if (status.isPermanentlyDenied) {
  //         openAppSettingsByDialog(context, message: "您已经拒绝权限，无法访问相册权限，是否前往设置开启？");
  //       }
  //       /// 活动限制,仅在iOS以上受支持。
  //       else if (status.isRestricted) {
  //         openAppSettingsByDialog(context, message: "相册访问权限受限，是否前往设置开启？");
  //       }
  //       return false;
  //     }
  //   } catch (e) {
  //     EasyLoading.showToast(e.toString());
  //     return false;
  //   }
  // }
  //
  // /// 通过对话框打开应用程序设置
  // static void openAppSettingsByDialog(BuildContext context,
  //     {String? title, String? message}) {
  //   DefDialog.showDialog1(
  //     context: context,
  //     title: title,
  //     message: message,
  //     confirm: () {
  //       openAppSettings();
  //     },
  //   );
  // }

}