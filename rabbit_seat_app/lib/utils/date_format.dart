import 'package:date_format/date_format.dart';

/// 通过时间戳获取显示时间文本
class DateFormatter {
  /// 时间换算单位
  static final num MINUTE = 60000;
  static final num HOUR = 3600000;
  static final num DAY = 86400000;

  /// 换算格式
  static final String SECOND_AGO = "秒前";
  static final String MINUTE_AGO = "分钟前";
  static final String HOUR_AGO = "小时前";
  static final String DAY_AGO = "天前";

  static String formatByMilliseconds(int time) {
    return format(DateTime.fromMillisecondsSinceEpoch(time));
  }

  //时间转换
  static String format(DateTime date) {
    num delta =
        DateTime.now().millisecondsSinceEpoch - date.millisecondsSinceEpoch;
    if (delta < 1 * MINUTE) {
      num seconds = toSeconds(delta);
      return (seconds <= 0 ? 1 : seconds).toInt().toString() + SECOND_AGO;
    }
    if (delta < 45 * MINUTE) {
      num minutes = toMinutes(delta);
      return (minutes <= 0 ? 1 : minutes).toInt().toString() + MINUTE_AGO;
    }
    if (delta < 24 * HOUR) {
      num hours = toHours(delta);
      return (hours <= 0 ? 1 : hours).toInt().toString() + HOUR_AGO;
    }
    if (delta < 48 * HOUR) {
      return "昨天";
    }
    return formatDate(date, [yyyy, '-', mm, '-', dd, ' ', hh, ':', nn]);
  }

  static num toSeconds(num date) {
    return date / 1000;
  }

  static num toMinutes(num date) {
    return toSeconds(date) / 60;
  }

  static num toHours(num date) {
    return toMinutes(date) / 60;
  }

  static num toDays(num date) {
    return toHours(date) / 24;
  }
}