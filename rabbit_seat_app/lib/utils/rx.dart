import 'package:rxdart/rxdart.dart';

/**
 * Rxdart
 */
class Rx {

  /*
    // 监听
    rx_aaaa.where((event) => (event as Map<String, dynamic>).isNotEmpty).listen((event) {
      print(">>> value:$event");
    });
    // 发消息
    rx_aaaa.add({"key" : "value"});
     */

  /// 播放gif控制
  static final PublishSubject playGifSubject = PublishSubject<int>();

  static final PublishSubject rx_aaaa = PublishSubject<Map<String, dynamic>>();

  /// 文章点赞状态
  static final PublishSubject articleThumbsUpSubject = PublishSubject<Map<String, dynamic>>();

  /// 圈子文章举报
  static final PublishSubject articleReportSubject = PublishSubject<Map<String, dynamic>>();

  /// 圈子文章开放评论状态
  static final PublishSubject articleCommentsStateSubject = PublishSubject<Map<String, dynamic>>();

  /// 圈子文章刷新
  static final PublishSubject articleRefreshSubject = PublishSubject<bool>();

  /// 圈子刷新
  static final PublishSubject circleRefreshSubject = PublishSubject<int>();

  /// 系统消息
  static final PublishSubject noticeCountSubject = PublishSubject<int>();

  /// 系统消息-接受共享
  static final PublishSubject noticeAcceptSharedSubject = PublishSubject<String>();

  /// 首页按钮控制
  static final PublishSubject homeControlSubject = PublishSubject<int>();
  /// 首页按钮控制
  static final PublishSubject homeBgChangeSubject = PublishSubject<Map<String, dynamic>>();


  /// 删除设备
  static final PublishSubject removeDeviceByIdSubject = PublishSubject<Map<String, dynamic>>();
  /// 修改设备名称
  static final PublishSubject modifyDeviceNameByIdSubject = PublishSubject<Map<String, dynamic>>();


  /// 主页gif 播放控制
  static final PublishSubject homeGifPlaySubject = PublishSubject<int>();

  static final PublishSubject homeRefreshSubject = PublishSubject<int>();


  /// 清除消息
  static final PublishSubject sendMyItemBadgeSubject = PublishSubject<int>();

  /// 清除消息
  static final PublishSubject clearMyItemBadgeSubject = PublishSubject<int>();
}