import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/cache_config.dart';
import '../models/profile.dart';
import '../models/tuya/tuyaHome.dart';

// 提供五套可选主题色
const _themes = <MaterialColor>[
  Colors.blue,
  Colors.cyan,
  Colors.teal,
  Colors.green,
  Colors.red,
];

/// 全局管理
class Global {
  static late SharedPreferences _prefs;
  SharedPreferences get sharedPreferences => _prefs;

  static Profile profile = Profile();

  // 可选的主题列表
  static List<MaterialColor> get themes => _themes;

  static TuyaHome? homeDetail;

  // 是否为release版
  static bool get isRelease => const bool.fromEnvironment("dart.vm.product");

  //初始化全局信息，会在APP启动时执行
  static Future init() async {
    WidgetsFlutterBinding.ensureInitialized();
    _prefs = await SharedPreferences.getInstance();
    var _profile = _prefs.getString("profile");
    print(">>>> _profile:$_profile");

    if (_profile != null) {
      try {
        profile = Profile.fromJson(jsonDecode(_profile));
        print(">>>> cache:${profile.token}");
      } catch (e) {
        print(e);
      }
    } else {
      profile = Profile();
      // 默认主题索引为0，代表蓝色
      profile.theme = 0;
    }

    // 如果没有缓存策略，设置默认缓存策略
    profile.cache = profile.cache ?? CacheConfig();
    // saveProfile();
    // profile.token =
    //   "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJheTE2NTAxMjMzNjA1MjZuTUFrUiIsImlhdCI6MTY1MTMyODQ3MCwiZXhwIjoxNjUxNjg4NDcwfQ.ON8pU35Zji1Vy9F4HWD2fG2as7XMgQ5sdaTIFZGBt0UhDSiltx4MCLDGfVy1p-RcIBGmKuu9SA6bcHjmU1-46w";
    // DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    //
    // if (Platform.isAndroid) {
    //   AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    //   print('Running on ${androidInfo.model}'); // e.g. "Moto G (4)"
    // } else {
    //   IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
    //   print('Running on ${iosInfo.utsname.machine}'); // e.g. "iPod7,1"
    // }
  }

  // 持久化Profile信息
  static saveProfile() {
    var res = profile.toJson();
    print(">>> saveProfile:$res");
    print(">>> jsonEncode:${jsonEncode(profile.toJson())}");
    _prefs.setString("profile", jsonEncode(profile.toJson()));
  }
}
