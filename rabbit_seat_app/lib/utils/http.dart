import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/main.dart';
import 'package:rabbit_seat_app/models/profile.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/provider/device_provider.dart';

import '../models/error.dart';
import 'global.dart';

// 发布服务器地址
// const String kReleaseBaseUrl = 'https://www.buddybuzzy.com/seat';
const String kReleaseBaseUrl = 'https://api.buddybuzzy.com/seat';

// 测试服务器地址
const String kDebugBaseUrl = "http://175.24.182.188:3001/mock/11/api";

/**
 * 网络请求类
 */
class Http {
  // 请求配置
  static BaseOptions _baseOptions = BaseOptions(
    baseUrl: kReleaseBaseUrl,
    connectTimeout: Duration(minutes: 1),
    sendTimeout: Duration(minutes: 1),
    receiveTimeout: Duration(minutes: 1),
    contentType: Headers.jsonContentType,
    responseType: ResponseType.json,
    headers: {},
  );

  // 网络请求对象
  static Dio dio = Dio(
    _baseOptions,
  )..interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          /// 添加 Token
          if (Global.profile.token != null &&
              Global.profile.token!.isNotEmpty) {
            options.headers.addAll({"Authorization": Global.profile.token});
          }

          // print(">>> 请求" + options.toString());
          // print('>>> uri:' + options.uri.toString());
          // print(">>> params:" + options.queryParameters.toString());
          // print(">>> data:" + options.data.toString());
          // print(">>> headers:" + options.headers.toString());
          // 继续
          handler.next(options);
        },
        onResponse: (response, handler) {
          // print(">>> 响应" + response.toString());

          var res = ResultModel<Object?>.fromJson(response.data, (json) {
            return json;
          });

          if (res.code == 401 && !response.realUri.path.contains("token")) {
            // 跳转首页
            Future.delayed(Duration(seconds: 0)).then((onValue) {
              if (navigatorKey.currentState != null &&
                  navigatorKey.currentState!.overlay != null) {
                var context = navigatorKey.currentState!.overlay!.context;

                // 清除数据
                context.read<DeviceProvider>().clear();

                Global.profile.token = null;
                Global.profile = Profile();
                Global.saveProfile();
                Navigator.pushNamedAndRemoveUntil(
                    context, '/login-code', (route) => false);
              }
            });
          }
          // 继续
          handler.next(response);
        },
        onError: (error, handler) {
          print('>>> 错误' + error.toString());
          // 继续
          handler.next(error);
        },
      ),
    );

  /// 网络请求
  static void req(
    String method,
    String url, {
    dynamic data,
    Function(dynamic)? success,
    Function(String)? failure,
  }) async {
    try {
      Response? response;
      switch (method) {
        case 'get':
          response = await dio.get(url, queryParameters: data);
          break;
        case 'post':
          response = await dio.post(url, data: data);
          break;
        case 'put':
          response = await dio.put(url, data: data);
          break;
        case 'delete':
          response = await dio.delete(url, data: data);
          break;
      }
      if (response != null) {
        if (response.statusCode == HttpStatus.ok) {
          if (success != null) success(response.data);
        } else {
          if (failure != null) failure(response.statusMessage ?? "网络请求失败！");
        }
      } else {
        if (failure != null) failure("无匹配'$method'请求方式！");
      }
    } on DioError catch (e) {
      print(">>> Http Request Error:$e");
      if (failure != null) failure(_getDioErrorMessage(e));
    } catch (e) {
      print(">>> Http Request Error:$e");
      if (failure != null) failure("网络请求失败！");
    }
  }

  /**
   * GET 请求
   */
  static Future<Map<String, dynamic>> get(String url,
      {Map<String, dynamic>? data}) async {
    try {
      Response response = await dio.get(url, queryParameters: data);
      if (response.statusCode == HttpStatus.ok) {
        return response.data;
      } else {
        print(
            '>>> http Request Error:${response.statusMessage} code:${response.statusCode}');
        throw new Exception(response.statusMessage ?? "网络请求失败！");
      }
    } on DioError catch (e) {
      print(">>> Http Request Error:$e");
      throw new Exception(_getDioErrorMessage(e));
    } catch (e) {
      print(">>> Http Request Error:$e");
      throw new Exception(e.toString());
    }
  }

  /**
   * POST 请求
   */
  static Future<Map<String, dynamic>> post(String url,
      {dynamic data, Options? options}) async {
    try {
      Response response = await dio.post(url, data: data, options: options);
      if (response.statusCode == HttpStatus.ok) {
        return response.data;
      } else {
        print(
            '>>> http Request Error:${response.statusMessage} code:${response.statusCode}');
        throw new Exception(response.statusMessage ?? "网络请求失败！");
      }
    } on DioError catch (e) {
      print(">>> Http Request Error:$e");
      throw new Exception(_getDioErrorMessage(e));
    } catch (e) {
      print(">>> Http Request Error:$e");
      throw new Exception(e.toString());
    }
  }

  /**
   * PUT 请求
   */
  static Future<Map<String, dynamic>> put(String url,
      {Map<String, dynamic>? data}) async {
    try {
      Response response = await dio.put(url, data: data);
      if (response.statusCode == HttpStatus.ok) {
        return response.data;
      } else {
        print(
            '>>> http Request Error:${response.statusMessage} code:${response.statusCode}');
        throw new Exception(response.statusMessage ?? "网络请求失败！");
      }
    } on DioError catch (e) {
      print(">>> Http Request Error:$e");
      throw new Exception(_getDioErrorMessage(e));
    } catch (e) {
      print(">>> Http Request Error:$e");
      throw new Exception(e.toString());
    }
  }

  /**
   * DELETE 请求
   */
  static Future<Map<String, dynamic>> del(String url,
      {Map<String, dynamic>? data, Options? options}) async {
    try {
      Response response = await dio.delete(url, data: data, options: options);
      if (response.statusCode == HttpStatus.ok) {
        return response.data;
      } else {
        print(
            '>>> http Request Error:${response.statusMessage} code:${response.statusCode}');
        throw new MyError(
            message: response.statusMessage ?? "网络请求失败！", error: Error());
      }
    } on DioError catch (e) {
      print(">>> Http Request Error:$e");
      throw new MyError(message: _getDioErrorMessage(e), error: e);
    } catch (e) {
      print(">>> Http Request Error:$e");
      throw new MyError(message: e.toString(), error: e);
    }
  }

  /**
   * 获取网络错误信息
   */
  static String _getDioErrorMessage(DioException error) {
    // 判断类型
    if (error.type == DioExceptionType.connectionTimeout) {
      // 网络连接超时
      return "网络连接超时！";
    } else if (error.type == DioExceptionType.sendTimeout) {
      // 网络连接超时
      return "网络发送超时！";
    } else if (error.type == DioExceptionType.receiveTimeout) {
      // 网络连接超时
      return "网络接收超时！";
    } else if (error.type == DioExceptionType.badResponse) {
      // 当服务器响应时，但带有不正确的状态，如404、503…
      return error.message ?? "网络加载失败";
    } else if (error.type == DioExceptionType.cancel) {
      // 当请求被取消时，dio将抛出此类型的错误。
      return "网络请求被取消！";
    } else {
      // 默认错误类型，某些其他错误。在这种情况下，您可以使用DioError。如果不为空则返回错误。
      return "网络请求未知错误！";
    }
  }

  static Future<Map<String, dynamic>?> request(
      String url, dynamic data, String method) async {
    try {
      Response? res;
      switch (method) {
        case "get":
          res = await dio.get(url, queryParameters: data);
          break;
        case "post":
          res = await dio.post(url, data: data);
          break;
        case "put":
          res = await dio.put(url, data: data);
          break;
        case "delete":
          res = await dio.delete(url, data: data);
          break;
      }
      return res!.data;
    } on DioError catch (e) {
      print(e);
      return null;
      // // The request was made and the server responded with a status code
      // // that falls out of the range of 2xx and is also not 304.
      // if (e.response != null) {
      //   print(e.response.data);
      //   print(e.response.headers);
      //   print(e.response.requestOptions);
      // } else {
      //   // Something happened in setting up or sending the request that triggered an Error
      //   print(e.requestOptions);
      //   print(e.message);
      // }
    }
  }
}
