import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rabbit_seat_app/widgets/dialog/dialog.dart';

class Util {
  //toAndroid  是定义好的通道名称

  static bool hasInitLoading = false;

  static bool hasInitSdk = false;
  static void initOtherSdk() async {
    //pay是android代码中定义的方法
    if (!hasInitSdk) {
      try {
        var _android = const MethodChannel('toAndroid11');
        var result = await _android.invokeMethod('initOtherSdk');
        hasInitSdk = true;
      } catch (ex) {
        print("initOtherSdk error");
        print(ex);
      }
    }
  }

  static Future<void> checkScanDevicePermission() async {
    var b1 = await Permission.location.request().isGranted;
    var b2 = await Permission.bluetooth.request().isGranted;
    var b3 = await Permission.bluetoothConnect.request().isGranted;
    var b4 = await Permission.bluetoothScan.request().isGranted;
  }

  static Widget getButton(width, height, text, color, backround, tapCallBack,
      {borderColor = Colors.black}) {
    return InkWell(
      onTap: () {
        tapCallBack();
      },
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: backround,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Center(
            child: Text(
          text,
          style: TextStyle(fontSize: 18.sp, color: color),
        )),
      ),
    );
  }

  static Widget getText(text,
      {num fontSize = 12,
      color = Colors.black,
      fontWeight = FontWeight.normal}) {
    return text != null
        ? Text(text,
            style: TextStyle(
                fontSize: fontSize.sp, color: color, fontWeight: fontWeight))
        : Container();
  }

  static Widget getBox(width, height, color, radius, child,
      {borderColor = Colors.transparent, borderWidth = 0.0}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(radius),
          border: Border.all(color: borderColor, width: borderWidth)),
      child: Center(
        child: child,
      ),
    );
  }

  static Widget getTopBox(width, height, color, radius, child,
      {borderColor = Colors.transparent, borderWidth = 0.0}) {
    return Container(
      alignment: Alignment.topLeft,
      width: width,
      height: height,
      decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(radius),
          border: Border.all(color: borderColor, width: borderWidth)),
      child: child,
    );
  }

  static Future<bool?> showMessage(context, title, content, callback) {
    return showDialog(
        context: context,
        builder: (cnotext) {
          return AlertDialog(
            title:
                Center(child: Text(title, style: TextStyle(fontSize: 15.sp))),
            content: Container(
              child: Text(content, style: TextStyle(fontSize: 12.sp)),
              height: 40.h,
              alignment: Alignment.center,
            ),
            actions: [
              Padding(
                padding: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 10.h),
                child: Row(children: [
                  Util.getButton(
                      100.0, 42.h, "取消", Colors.black, const Color(0xFFF9F9F9),
                      () async {
                    Navigator.of(context).pop();
                  }),
                  Padding(
                    padding: const EdgeInsets.only(left: 5.0),
                    child: Util.getButton(100.0, 42.h, "确定", Colors.white,
                        const Color.fromARGB(255, 255, 119, 0), () {
                      Navigator.of(context).pop();
                      callback();
                    }),
                  ),
                ]),
              )
            ],
          );
        });
  }

  static BoxDecoration getBoxDecoration() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(10),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.1), // 阴影的颜色
          offset: const Offset(10, 20), // 阴影与容器的距离
          blurRadius: 60.0, // 高斯的标准偏差与盒子的形状卷积。
          spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
        ),
      ],
    );
  }

  /// 移除软键盘
  static void removePrimaryFocus(BuildContext context) {
    // 焦点管理器，通过焦点管理器取消关注焦点，从而关闭软键盘
    FocusScopeNode scopeNode = FocusScope.of(context);
    // 判断当前是否无主要关注焦点及焦点子类不为空
    if (!scopeNode.hasPrimaryFocus && scopeNode.focusedChild != null) {
      // 当前主要焦点取消关注
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  /// 获取应用缓存
  static Future loadApplicationCache() async {
    //获取文件夹
    Directory docDirectory = await getApplicationDocumentsDirectory();
    Directory tempDirectory = await getTemporaryDirectory();

    double size = 0;

    if (docDirectory.existsSync()) {
      size += await getTotalSizeOfFilesInDir(docDirectory);
    }
    if (tempDirectory.existsSync()) {
      size += await getTotalSizeOfFilesInDir(tempDirectory);
    }
    return size;
  }

  /// 缓存大小格式转换
  static String formatSize(double value) {
    if (null == value) {
      return '0';
    }
    List unitArr = []
      ..add('B')
      ..add('K')
      ..add('M')
      ..add('G');
    int index = 0;
    while (value > 1024) {
      index++;
      value = value / 1024;
    }
    String size = value.toStringAsFixed(2);
    return size + unitArr[index];
  }

  /// 递归计算文件、文件夹的大小
  static Future getTotalSizeOfFilesInDir(final FileSystemEntity file) async {
    if (file is File) {
      int length = await file.length();
      return double.parse(length.toString());
    }
    if (file is Directory) {
      final List children = file.listSync();
      double total = 0;
      if (children != null)
        for (final FileSystemEntity child in children)
          total += await getTotalSizeOfFilesInDir(child);
      return total;
    }
    return 0;
  }

  /// 删除缓存
  static Future clearApplicationCache() async {
    Directory docDirectory = await getApplicationDocumentsDirectory();
    Directory tempDirectory = await getTemporaryDirectory();

    if (docDirectory.existsSync()) {
      await deleteDirectory(docDirectory);
    }

    if (tempDirectory.existsSync()) {
      await deleteDirectory(tempDirectory);
    }

    return;
  }

  static Future deleteDirectory(FileSystemEntity file) async {
    if (file is Directory) {
      final List children = file.listSync();
      for (final FileSystemEntity child in children) {
        await deleteDirectory(child);
        await child.delete();
      }
    }
  }

  static String? decodeBase64(String? str) {
    try {
      final base64Decoder = base64.decoder;
      final decodedBytes = base64Decoder.convert(str!);
      return utf8.decode(decodedBytes);
    } catch (e) {}
    return null;
  }

  /// Random UUID
  static String getUuid() {
    // 随机因子
    final _random = Random();
    // 字符集
    const _charSets =
        'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
    // 9位
    final _r1 = List.generate(
        9, (index) => _charSets[_random.nextInt(_charSets.length)]).join();
    // 4位
    final _r2 = List.generate(
        4, (index) => _charSets[_random.nextInt(_charSets.length)]).join();
    // 6位
    final _r3 = List.generate(
        6, (index) => _charSets[_random.nextInt(_charSets.length)]).join();
    // 4位
    final _r4 = List.generate(
        4, (index) => _charSets[_random.nextInt(_charSets.length)]).join();
    // 时间戳
    final _num = DateTime.now().millisecondsSinceEpoch;
    // 返回字符串组
    String _uuid =
        _r1 + '-' + _r2 + '-' + _r3 + '-' + _r4 + '-' + _num.toString();
    print(">>> uuid:$_uuid");
    return _uuid;
  }

  static void checkStoragePermissionStatus(
      BuildContext context, String info, Function confirm) async {
    if (Platform.isIOS) {
      var res = await Permission.photos.request().isGranted;
    }

    bool status = await Permission.storage.isGranted;
    if (!status) {
      // ignore: use_build_context_synchronously
      DefDialog.showDialog1(
          context: context,
          title: '相册/文件权限未开启',
          message: '两只兔子需要相册/文件权限，请允许两只兔子访问你的相册/文件权限，用于$info',
          cancelText: '取消',
          confirmText: '确认',
          cancel: () {},
          confirm: () async {
            var res = await Permission.storage.request().isGranted;
            if (!res) {
              EasyLoading.showToast("权限申请失败");
            } else {
              confirm();
            }
          });
    } else {
      confirm();
    }
  }

  static void checkCameraPermissionStatus(
      BuildContext context, String info, Function confirm) async {
    bool status = await Permission.camera.isGranted;
    if (!status) {
      // ignore: use_build_context_synchronously
      DefDialog.showDialog1(
          context: context,
          title: '相机权限未开启',
          message: '两只兔子需要相机权限，请允许两只兔子访问你的相机权限，用于$info',
          cancelText: '取消',
          confirmText: '确认',
          cancel: () {},
          confirm: () async {
            var res = await Permission.camera.request().isGranted;
            if (!res) {
              EasyLoading.showToast("权限申请失败");
            } else {
              confirm();
            }
          });
    } else {
      confirm();
    }
  }

  static void checkLocationPermissionStatus(
      BuildContext context, String info, Function confirm) async {
    bool status = await Permission.location.isGranted;
    if (!status) {
      // ignore: use_build_context_synchronously
      DefDialog.showDialog1(
          context: context,
          title: '位置权限未开启',
          message: '两只兔子需要位置权限，请允许两只兔子访问你的位置权限，用于$info',
          cancelText: '取消',
          confirmText: '确认',
          cancel: () {},
          confirm: () async {
            var res = await Permission.location.request().isGranted;
            if (!res) {
              EasyLoading.showToast("位置权限申请失败");
            } else {
              confirm();
            }
          });
    } else {
      confirm();
    }
  }

  static void checkMicrophonePermissionStatus(
      BuildContext context, String info, Function confirm) async {
    bool status = await Permission.microphone.isGranted;
    if (!status) {
      // ignore: use_build_context_synchronously
      DefDialog.showDialog1(
          context: context,
          title: '麦克风权限未开启',
          message: '两只兔子需要麦克风权限，请允许两只兔子访问你的麦克风权限，用于$info',
          cancelText: '取消',
          confirmText: '确认',
          cancel: () {},
          confirm: () async {
            var res = await Permission.microphone.request().isGranted;
            if (!res) {
              EasyLoading.showToast("麦克风权限申请失败");
            } else {
              confirm();
            }
          });
    } else {
      confirm();
    }
  }

  static void checkBluetoothPermissionStatus(
      BuildContext context, String info, Function confirm) async {
    bool status = await Permission.bluetooth.isGranted;
    if (!status) {
      // ignore: use_build_context_synchronously
      DefDialog.showDialog1(
          context: context,
          title: '蓝牙权限未开启',
          message: '两只兔子需要蓝牙权限，请允许两只兔子访问你的蓝牙权限，用于$info',
          cancelText: '取消',
          confirmText: '确认',
          cancel: () {},
          confirm: () async {
            var res = await Permission.bluetooth.request().isGranted;
            if (!res) {
              EasyLoading.showToast("蓝牙权限申请失败");
            } else {
              confirm();
            }
          });
    } else {
      confirm();
    }
  }

  static void checkBluetoothConnectPermissionStatus(
      BuildContext context, String info, Function confirm) async {
    bool status = await Permission.bluetoothConnect.isGranted;
    if (!status) {
      // ignore: use_build_context_synchronously
      DefDialog.showDialog1(
          context: context,
          title: '蓝牙连接权限未开启',
          message: '两只兔子需要蓝牙连接权限，请允许两只兔子访问你的蓝牙连接权限，用于$info',
          cancelText: '取消',
          confirmText: '确认',
          cancel: () {},
          confirm: () async {
            var res = await Permission.bluetoothConnect.request().isGranted;
            if (!res) {
              EasyLoading.showToast("蓝牙连接权限申请失败");
            } else {
              confirm();
            }
          });
    } else {
      confirm();
    }
  }

  static void checkBluetoothScanPermissionStatus(
      BuildContext context, String info, Function confirm) async {
    bool status = await Permission.bluetoothScan.isGranted;
    if (!status) {
      // ignore: use_build_context_synchronously
      DefDialog.showDialog1(
          context: context,
          title: '蓝牙扫描权限未开启',
          message: '两只兔子需要蓝牙扫描权限，请允许两只兔子访问你的蓝牙扫描权限，用于$info',
          cancelText: '取消',
          confirmText: '确认',
          cancel: () {},
          confirm: () async {
            var res = await Permission.bluetoothScan.request().isGranted;
            if (!res) {
              EasyLoading.showToast("蓝牙扫描权限申请失败");
            } else {
              confirm();
            }
          });
    } else {
      confirm();
    }
  }
}
void printLog(String message) {
  final timestamp = DateTime.now().toIso8601String();
  print('[$timestamp] $message');
}
