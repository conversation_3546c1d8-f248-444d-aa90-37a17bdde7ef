import 'package:rabbit_seat_app/models/error.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/models/user/index.dart';
import 'package:rabbit_seat_app/models/user/user_model.dart';
import 'package:rabbit_seat_app/utils/http.dart';

import '../utils/global.dart';

class UserService {
  /// 获取用户信息
  static Future<UserModel?> getUser(String? id) async {
    var json = await Http.request("/api/appuser/user/$id", null, 'get');
    var res = ResultModel<UserModel>.fromJson(json!, (json) {
      return UserModel.fromJson(json! as Map<String, dynamic>);
    });
    Global.profile.user = res.data;
    Global.saveProfile();
    return res.data;
  }

  // static Future<void> getUser({
  //   required String id,
  //   Function(UserModel)? success,
  //   Function(String)? failure,
  // }) async {
  //   try {
  //     final data = await Http.get("/api/appuser/user/$id");
  //     final result = ResultModel<UserModel>.fromJson(
  //         data, (json) => UserModel.fromJson(json as Map<String, dynamic>));
  //     if (result.code == 200) {
  //       Global.profile.user = result.data;
  //       Global.saveProfile();
  //       if (success != null) success(result.data);
  //     } else {
  //       if (failure != null) failure(result.message);
  //     }
  //   } on MyError catch (e) {
  //     if (failure != null) failure(e.message!);
  //   } catch (e) {
  //     print('>>>> Error:$e');
  //     if (failure != null) failure(e.toString());
  //   }
  // }

  static Future<bool> updateUser(UserModel? user) async {
    var id = user?.id;
    var json = await Http.request("/api/appuser/$id", user, 'put');
    return json!["data"];
  }

  /**
   * 用户数据更新
   */
  static Future<void> updateUserInfo({
    required String uid,
    String? phone,
    String? nickName,
    String? avatar,
    String? city,
    Function(ResultModel)? success,
    Function(String)? failure,
  }) async {
    try {
      Map<String, dynamic> params = {
        "telephone": phone,
        "nickName": nickName,
        "avatar": avatar,
        "city": city
      };
      final data = await Http.put('/api/appuser/$uid', data: params);
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        if (success != null) success(result);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message ?? "网络请求失败！");
    } catch (e) {
      if (failure != null) failure(e.toString());
    }
  }

  static Future<bool> addBaby(String userId, BabyModel baby) async {
    await Http.request("/api/appuser/babyinfo/$userId", baby, 'post');
    return true;
  }

  static Future<bool> modifyBaby(String userId, BabyModel baby) async {
    var res = await Http.request("/api/appuser/babyinfo/$userId", baby, 'put');
    if (res != null) {
      return true;
    }
    return false;
  }

  static Future<bool> deleteBaby(String userId, BabyModel baby) async {
    var res = await Http.request("/api/appuser/babyinfo/$userId", baby, 'delete');
    if (res != null) {
      return true;
    }
    return false;
  }

  /// 添加紧急联系人
  /// 添加紧急联系人
  static Future<bool> addEmergencyContact(
      String userId, ContactsModel item) async {
    var res =
        await Http.request("/api/appuser/emergencyContact/$userId", item, 'post');
    if (res != null) {
      return true;
    }
    return false;
  }

  // static Future<void> addEmergencyContact({
  //   required String uid,
  //   required String name,
  //   required String phone,
  //   Function(ResultModel)? success,
  //   Function(String)? failure,
  // }) async {
  //   try {
  //     Map<String, dynamic> params = {'name': name, 'telephone': phone};
  //     final data = await Http.post("/api/appuser/emergencyContact/$uid");
  //     final result = ResultModel.fromJson(data, (json) => json);
  //     if (result.code == 200) {
  //       if (success != null) success(result);
  //     } else {
  //       if (failure != null) failure(result.message);
  //     }
  //   } on MyError catch (e) {
  //     if (failure != null) failure(e.message!);
  //   } catch (e) {
  //     print('>>>> Error:$e');
  //     if (failure != null) failure(e.toString());
  //   }
  // }

  static Future<bool> modifyEmergencyContact(
      String userId, ContactsModel item) async {
    var res =
        await Http.request("/api/appuser/emergencyContact/$userId", item, 'put');
    if (res != null) {
      return true;
    }
    return false;
  }

  static Future<bool> deleteEmergencyContact(
      String userId, ContactsModel item) async {
    var res =
        await Http.request("/api/appuser/emergencyContact/$userId", item, 'delete');
    if (res != null) {
      return true;
    }
    return false;
  }
}
