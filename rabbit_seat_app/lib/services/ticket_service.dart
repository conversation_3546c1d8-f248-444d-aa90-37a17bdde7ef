import 'package:rabbit_seat_app/models/list/list_model.dart';
import 'package:rabbit_seat_app/models/ticket/ticket_model.dart';

import '../models/error.dart';
import '../models/result/result_model.dart';
import '../utils/http.dart';

class TicketService {
  // 加载优惠券信息
  static void loadDatas({
    int pageIndex = 0,
    int pageSize = 10,
    Function(List<TicketModel>?)? success,
    Function(String)? failure,
  }) async {
    try {
      Map<String, dynamic> params = {
        "pageSize": pageSize,
        "pageNo": pageIndex,
      };
      final data = await Http.get("/api/coupon/applist", data: params);
      // final result = ResultModel<ListModel>.fromJson(
      //     data as Map<String, dynamic>,
      //     (json) => ListModel.fromJson(json as Map<String, dynamic>,
      //         (jsonT) => TicketModel.fromJson(jsonT as Map<String, dynamic>)));

      final result = ResultModel<List<TicketModel>>.fromJson(data, (json) {
        if (json != null && (json is List<dynamic>)) {
          return (json)
              .map((e) => TicketModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });

      if (result.code == 200) {
        if (success != null) success(result.data);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message!);
    } catch (e) {
      if (failure != null) failure("数据解析失败！");
    }
  }
}
