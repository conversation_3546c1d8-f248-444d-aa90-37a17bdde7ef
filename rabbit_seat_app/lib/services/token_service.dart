import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/models/login/login_model.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/utils/http.dart';

import '../models/error.dart';
import '../models/login/sms_code_model.dart';

class TokenService {
  /**
   * 短信验证码登录
   */
  static void loginByVerifyCode({
    required String phone,
    required String code,
    Function(LoginModel)? success,
    Function(String)? failure,
  }) async {
    try {
      Map<String, dynamic> params = {
        'telephone': phone,
        'smsCode': code,
      };
      final data = await Http.post('/api/token/app', data: params);
      final result = ResultModel<LoginModel?>.fromJson(
          data as Map<String, dynamic>,
              (json) => LoginModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (success != null) success(result.data!);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message!);
    } catch (e) {
      print('>>>> Error:$e');
      if (failure != null) failure("数据解析失败！");
    }
  }

  /**
   * 账户密码登录
   */
  static void loginByAccountPassword({
    required String phone,
    required String password,
    Function(LoginModel?)? success,
    Function(String)? failure,
  }) async {
    try {
      Map<String, dynamic> params = {
        'telephone': phone,
        'password': password,
      };
      final data = await Http.post('/api/token/app', data: params);
      print(">>>>> data **** $data");
      final result = ResultModel<LoginModel>.fromJson(
          data as Map<String, dynamic>,
              (json) => LoginModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (success != null) success(result.data);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message!);
    } catch (e) {
      print('>>>> Error:$e');
      if (failure != null) failure(e.toString());
    }
  }

  /**
   * 发送短信验
   */
  static void sendSMSCode({
    required String phone,
    Function(SMSCodeModel?)? success,
    Function(String)? failure,
  }) async {
    try {
      final data = await Http.post('/api/token/sendSms/$phone');
      final result = ResultModel<SMSCodeModel>.fromJson(
          data as Map<String, dynamic>,
              (json) => SMSCodeModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (success != null) success(result.data);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message!);
    } catch (e) {
      print('>>>> Error:$e');
      if (failure != null) failure("数据解析失败！");
    }
  }

  /**
   * 发送短信验
   */
  static void verifySMSCode({
    required String phone,
    required String code,
    Function(SMSCodeModel?)? success,
    Function(String)? failure,
  }) async {
    try {
      Map<String, dynamic> params = {
        'telephone': phone,
        'code': code,
      };
      final data = await Http.post('/api/token/verifySmsCode', data: params);
      final result = ResultModel<SMSCodeModel>.fromJson(
          data as Map<String, dynamic>,
              (json) => SMSCodeModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (success != null) success(result.data);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message!);
    } catch (e) {
      print('>>>> Error:$e');
      if (failure != null) failure("数据解析失败！");
    }
  }
}
