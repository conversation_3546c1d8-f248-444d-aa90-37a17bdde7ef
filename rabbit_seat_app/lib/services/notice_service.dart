import 'package:rabbit_seat_app/models/list/list_model.dart';
import 'package:rabbit_seat_app/models/message/inform_model.dart';

import '../models/error.dart';
import '../models/result/result_model.dart';
import '../utils/http.dart';

/**
 * 系统消息
 */
class NoticeService {
  // 加载未读消息通知
  static Future<void> loadDatas({
    int pageIndex = 0,
    int pageSize = 10,
    Function(ListModel?)? success,
    Function(String)? failure,
  }) async {
    try {
      Map<String, dynamic> params = {
        "pageSize": pageSize,
        "pageNo": pageIndex,
      };
      final data =
          await Http.get("/api/appMessage/list", data: params);
      final result = ResultModel<ListModel<InformModel>>.fromJson(data, (json) => ListModel.fromJson(json as Map<String, dynamic>, (jsonT) => InformModel.fromJson(jsonT as Map<String, dynamic>)));
      if (result.code == 200) {
        if (success != null) success(result.data);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message!);
    } catch (e) {
      print('>>>> Error:$e');
      if (failure != null) failure(e.toString());
    }
  }

  // 加载未读消息通知
  static Future<void> sendReadTag({
    required String id,
    Function(dynamic)? success,
    Function(String)? failure,
  }) async {
    try {
      final data =
          await Http.put("/api/appMessage/read/" + id, data: {"id": id});
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        if (success != null) success(result.data);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message!);
    } catch (e) {
      print('>>>> Error:$e');
      if (failure != null) failure(e.toString());
    }
  }
}
