import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/models/help/file_upload_result.dart';
import 'package:rabbit_seat_app/models/index.dart';
import 'package:rabbit_seat_app/models/list/list_model.dart';
import 'package:rabbit_seat_app/models/login/sms_code_model.dart';
import 'package:rabbit_seat_app/models/privacyPolicy/privacy_policy_model.dart';

import '../models/error.dart';
import '../models/result/result_model.dart';
import '../utils/http.dart';

class HelpService {
  static Future<FileUpResult?> uploadFile(String file) async {
    try {
      var formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(file,
            filename: file.substring(file.indexOf("/")))
      });
      final data = await Http.post('/api/common/upload', data: formData);
      final result = ResultModel<FileUpResult>.fromJson(
          data as Map<String, dynamic>, (json) {
        return FileUpResult.fromJson(json! as Map<String, dynamic>);
      });
      return result.data;
    } catch (e) {
      return null;
    }
  }

  /// 加载用户协议数据
  static void loadAgreementData({
    Function(PolicyModel)? success,
    Function(String)? failure,
  }) async {
    _loadAccordConfigData(
        type: "app.help.serviceAgreement",
        success: (data) {
          ListModel? page = data as ListModel;
          if (page != null &&
              page.content != null &&
              page.content!.length > 0) {
            if (success != null) success(page.content!.first);
          } else {
            if (failure != null) failure("没有数据！");
          }
        },
        failure: failure);
  }

  /// 加载政策数据
  static void loadPolicyData({
    Function(PolicyModel)? success,
    Function(String)? failure,
  }) async {
    _loadAccordConfigData(
        type: "app.help.privacyPolicy",
        success: (data) {
          ListModel? page = data as ListModel;
          if (page != null &&
              page.content != null &&
              page.content!.length > 0) {
            if (success != null) success(page.content!.first);
          } else {
            if (failure != null) failure("没有数据！");
          }
        },
        failure: failure);
  }

  /// 加载隐私政策
  static Future<List<PrivacyPolicyModel>?> loadPrivacyPolicyDatas() async {
    try {
      final data = await Http.get('/api/setting/privacyPolicy');
      final result =
          ResultModel<List<PrivacyPolicyModel>>.fromJson(data, (json) {
        if (json != null && (json is List<dynamic>)) {
          return (json as List<dynamic>)
              .map(
                  (e) => PrivacyPolicyModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        return result.data;
      } else {
        EasyLoading.showToast(result.message);
        return [];
      }
    } catch (e) {
      EasyLoading.showToast(e.toString());
      return [];
    }
  }

  static Future<PrivacyPolicyModel?> loadSimRechargePolicyDatas() async {
    try {
      final data =
          await Http.get('/api/setting/code/app.help.simRechargePolicy');
      final result = ResultModel<PrivacyPolicyModel>.fromJson(
          data as Map<String, dynamic>,
          (json) => PrivacyPolicyModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        return result.data;
      } else {
        EasyLoading.showToast(result.message);
        return PrivacyPolicyModel();
      }
    } catch (e) {
      EasyLoading.showToast(e.toString());
      return PrivacyPolicyModel();
    }
  }

  static Future<PrivacyPolicyModel?> getDeviceDataRefreshTime() async {
    try {
      final data =
          await Http.get('/api/setting/code/app.deviceDataRefreshTime');
      final result = ResultModel<PrivacyPolicyModel>.fromJson(
          data as Map<String, dynamic>,
          (json) => PrivacyPolicyModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        return result.data;
      } else {
        EasyLoading.showToast(result.message);
        return PrivacyPolicyModel();
      }
    } catch (e) {
      EasyLoading.showToast(e.toString());
      return PrivacyPolicyModel();
    }
  }

  /// 加载协议条约配置数据
  static void _loadAccordConfigData({
    required String type,
    int pageIndex = 0,
    int pageSize = 10,
    Function(PolicyModel?)? success,
    Function(String)? failure,
  }) async {
    try {
      Map<String, dynamic> params = {
        "name": type,
        "pageSize": pageSize,
        "pageNo": pageIndex,
      };
      final data = await Http.get('/api/setting/list', data: params);
      final result = ResultModel<PolicyModel>.fromJson(
          data as Map<String, dynamic>,
          (json) => PolicyModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (success != null) success(result.data);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message!);
    } catch (e) {
      if (failure != null) failure("数据解析失败！");
    }
  }
}
