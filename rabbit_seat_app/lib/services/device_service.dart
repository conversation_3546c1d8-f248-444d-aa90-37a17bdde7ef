import 'package:dio/dio.dart';
import 'package:rabbit_seat_app/models/device/device_model.dart';
import 'package:rabbit_seat_app/models/error.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/models/share/share_model.dart';

import '../utils/global.dart';
import '../utils/http.dart';

class DeviceService {
  static Future<bool> sendCommand({
    String? deviceId,
    String? code,
    dynamic value,
  }) async {
    List<Map<String, dynamic>> command = [
      {'code': code, 'value': value}
    ];
    Map<String, dynamic> params = {'commands': command};
    var json =
        await Http.request('/api/device/controll/$deviceId', params, 'post');
    if (json == null) return false;
    return true;
  }

  static Future<bool> sendRemoteInstruction(String deviceId) async {
    List<Map<String, dynamic>> command = [
      {'code': 'protectionLeftSw', 'value': false},
      {'code': 'protectionRightSw', 'value': false},
      {'code': 'F_light', 'value': false},
      {'code': 'fanSw', 'value': false},
      {'code': 'hotSw', 'value': false},
      {'code': 'autoMode', 'value': false}
    ];
    Map<String, dynamic> params = {'commands': command};
    var json =
    await Http.request('/api/device/controll/$deviceId', params, 'post');
    if (json == null) return false;
    return true;
  }

  /// 添加共享
  static void addShareDevice({
    required Map<String, dynamic> params,
    Function(dynamic)? success,
    Function(String)? failure,
  }) async {
    /*
    expiredTime	    string	必须
    homeId	        number	必须
    shareStatus	    string	必须  默认Init
    shareTelephone	string	必须
    deviceId	      string	必须  设备ID
     */
    try {
      Map<String, dynamic> params = {
        "expiredTime": '',
        "homeId": 0,
        "shareStatus": '',
        "shareTelephone": '',
        "deviceId": ''
      };
      final data = await Http.post('/api/deviceShare/', data: params);
      final result =
          ResultModel.fromJson(data as Map<String, dynamic>, (json) => json);
      if (result.code == 200) {
        if (success != null) success(result);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message ?? '网络请求失败！');
    } catch (e) {
      if (failure != null) failure(e.toString());
    }
  }

  /// 修改共享
  static void modifiyShare({
    required String id,
    Function(dynamic)? success,
    Function(String)? failure,
  }) async {
    try {
      Map<String, dynamic> params = {
        'shareStatus': '',
        'user': {
          'id': Global.profile.user!.id,
        }
      };
      final data = await Http.put('/api/deviceShare/' + id, data: params);
      final result =
          ResultModel.fromJson(data as Map<String, dynamic>, (json) => json);
      if (result.code == 200) {
        if (success != null) success(result);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message ?? '网络请求失败！');
    } catch (e) {
      if (failure != null) failure(e.toString());
    }
  }

  /// 获取列表
  static void loadDatas({
    required String homeId,
    Function(dynamic)? success,
    Function(String)? failure,
  }) async {
    try {
      final data = await Http.get('/deviceShare/list/' + homeId);
      final result =
          ResultModel.fromJson(data as Map<String, dynamic>, (json) => json);
      if (result.code == 200) {
        if (success != null) success(result);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message ?? '网络请求失败！');
    } catch (e) {
      if (failure != null) failure(e.toString());
    }
  }

  /// 加载共享设备详情
  static void loadShareDetails({
    required String id,
    Function(ShareModel?)? success,
    Function(String)? failure,
  }) async {
    try {
      final data = await Http.get('/api/deviceShare/' + id);
      if (data != null) {
        final result = ResultModel<ShareModel>.fromJson(
            data as Map<String, dynamic>,
            (json) => ShareModel.fromJson(json as Map<String, dynamic>));
        if (result.code == 200) {
          if (success != null) success(result.data);
        } else {
          if (failure != null) failure(result.message);
        }
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message ?? '网络请求失败！');
    } catch (e) {
      if (failure != null) failure(e.toString());
    }
  }

  /// 接收共享设备
  static void acceptShareDevices({
    required String id,
    Function(dynamic)? success,
    Function(String)? failure,
  }) async {
    try {
      final data = await Http.post('/api/deviceShare/accept/' + id,
          data: {"id": id},
          options: Options(
              headers: {'Content-Type': 'application/x-www-form-urlencoded'}));
      final result =
          ResultModel.fromJson(data as Map<String, dynamic>, (json) => json);
      if (result.code == 200) {
        if (success != null) success(result);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message ?? '网络请求失败！');
    } catch (e) {
      if (failure != null) failure(e.toString());
    }
  }

  /// 撤销设备共享
  static void repealDeviceShare({
    required String id,
    Function(dynamic)? success,
    Function(String)? failure,
  }) async {
    try {
      final data = await Http.del('/api/deviceShare/' + id);
      final result =
          ResultModel.fromJson(data as Map<String, dynamic>, (json) => json);
      if (result.code == 200) {
        if (success != null) success(result);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message ?? '网络请求失败！');
    } catch (e) {
      if (failure != null) failure(e.toString());
    }
  }

  static void getDeviceDetail({
    required String id,
    Function(DeviceModel?)? success,
    Function(String)? failure,
  }) async {
    try {
      final data = await Http.get('/api/device/' + id);
      final result = ResultModel<DeviceModel>.fromJson(
          data, (json) => DeviceModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (success != null) success(result.data);
      } else {
        if (failure != null) failure(result.message);
      }
    } on MyError catch (e) {
      if (failure != null) failure(e.message ?? '网络请求失败！');
    } catch (e) {
      if (failure != null) failure(e.toString());
    }
  }
}
