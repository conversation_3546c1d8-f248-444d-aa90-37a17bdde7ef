import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/controls/LoadingOverlay.dart';
import 'package:rabbit_seat_app/events/active_device_event.dart';

/// Our custom fork of https://pub.dev/packages/modal_progress_hud adding a fading effect
///
/// wrapper around any widget that makes an async call to show a modal progress
/// indicator while the async call is in progress.
///
/// The progress indicator can be turned on or off using [isLoading]
///
/// The progress indicator defaults to a [CircularProgressIndicator] but can be
/// any kind of widget
///
/// The color of the modal barrier can be set using [color]
///
/// The opacity of the modal barrier can be set using [opacity]
///
/// HUD=Heads Up Display
///
class AddDeviceSelect extends StatefulWidget {
  @override
  _AddDeviceSelectState createState() => _AddDeviceSelectState();
}

class _AddDeviceSelectState extends State<AddDeviceSelect>
    with SingleTickerProviderStateMixin {
  bool isLoading = false;
  StreamSubscription? eventSub;

  @override
  void initState() {
    super.initState();

    eventSub = Business.eventBus.on<ActiveDeviceEvent>().listen((event) {
      switch (event.type) {
        case ActiveDeviceEventType.activeDeviceSuccess:
          {
            Navigator.pop(context);
            setState(() {
              isLoading = false;
            });
          }
          break;
        case ActiveDeviceEventType.activeDeviceFail:
          {
            setState(() {
              isLoading = false;
            });
          }
          break;
        // TODO: Handle this case.
        case ActiveDeviceEventType.activeDeviceCancel:
          // TODO: Handle this case.
          break;
        case ActiveDeviceEventType.startActiving:
          {
            setState(() {
              isLoading = true;
            });
          }
          break;
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    eventSub?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: isLoading,
      opacity: 0.6,
      title: "设备绑定中......",
      color: Color.fromARGB(255, 167, 167, 167),
      child: Center(
        child: Container(
          height: 550,
          width: 1.sw - 30,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.0), // 设置圆角
          ),
          padding: EdgeInsets.fromLTRB(14, 12, 14, 30),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  padding: EdgeInsets.only(left: 5, bottom: 5),
                  alignment: Alignment.topRight,
                  child: Icon(Icons.close, size: 22),
                ),
              ),
              const Text(
                '选择合适的添加方式',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 35),
              Image.asset(
                'assets/images/add_bg.png',
                width: 1.sw - 30 - 14 * 2,
                fit: BoxFit.fitWidth,
              ),
              SizedBox(height: 20),
              Container(
                padding: EdgeInsets.only(left: 20, right: 20),
                child: const Text(
                  "图示位置贴有连接二维码，请选择“二维码扫一扫”，否则请选择“搜索附近设备”。",
                  style: TextStyle(color: Color.fromARGB(255, 116, 116, 116)),
                ),
              ),
              SizedBox(height: 35),
              InkWell(
                onTap: () {
                  Future.delayed(Duration(milliseconds: 100), () {
                    Navigator.pushReplacementNamed(context, "/add-device");
                  });
                },
                child: Container(
                  height: 45,
                  width: 1.sw - 30 - 100,
                  decoration: BoxDecoration(
                    color: Color.fromARGB(255, 240, 240, 240),
                    borderRadius: BorderRadius.circular(24.0), // 设置圆角
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text("搜索附近设备",
                          style: TextStyle(fontSize: 17, color: Colors.black))
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 15),
              InkWell(
                onTap: () {
                  Future.delayed(const Duration(milliseconds: 100), () {
                    Navigator.pushNamed(context, "/addDeviceScann");
                  });
                },
                child: Container(
                  height: 45,
                  width: 1.sw - 30 - 100,
                  decoration: BoxDecoration(
                    color: Color.fromARGB(255, 240, 240, 240),
                    borderRadius: BorderRadius.circular(24.0), // 设置圆角
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text("二维码扫一扫",
                          style: TextStyle(fontSize: 17, color: Colors.black))
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
