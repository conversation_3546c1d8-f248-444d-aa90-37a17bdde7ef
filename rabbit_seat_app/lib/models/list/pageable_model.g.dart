// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pageable_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PageableModel _$PageableModelFromJson(Map<String, dynamic> json) => PageableModel()
  ..sort = SortModel.fromJson(json['sort'] as Map<String, dynamic>)
  ..offset = json['offset'] as num
  ..pageNumber = json['pageNumber'] as num
  ..pageSize = json['pageSize'] as num
  ..paged = json['paged'] as bool
  ..unpaged = json['unpaged'] as bool;

Map<String, dynamic> _$PageableModelToJson(PageableModel instance) => <String, dynamic>{
  'sort': instance.sort,
  'offset': instance.offset,
  'pageNumber': instance.pageNumber,
  'pageSize': instance.pageSize,
  'paged': instance.paged,
  'unpaged': instance.unpaged,
};
