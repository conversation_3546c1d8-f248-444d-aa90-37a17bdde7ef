import 'package:json_annotation/json_annotation.dart';

import 'pageable_model.dart';
import 'sort_model.dart';

part 'list_model.g.dart';

@JsonSerializable(
    genericArgumentFactories: true, fieldRename: FieldRename.snake)
class ListModel<T> {
  ListModel();

  @Json<PERSON>ey(name: 'content')
  List<T>? content;
  @Json<PERSON>ey(name: 'pageable')
  PageableModel? pageable;
  @JsonKey(name: 'totalElements')
  late num totalElements;
  @JsonKey(name: 'last')
  late bool last;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'totalPages')
  late num totalPages;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'number')
  late num number;
  @Json<PERSON>ey(name: 'size')
  late num size;
  @<PERSON>son<PERSON><PERSON>(name: 'sort')
  SortModel? sort;
  @JsonKey(name: 'numberOfElements')
  late num numberOfElements;
  @JsonKey(name: 'first')
  late bool first;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'empty')
  late bool empty;


  factory ListModel.fromJson(Map<String, dynamic> json,
      T Function(Object? json) fromJsonT) =>
      _$ListModelFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) => _$ListModelToJson<T>(this, toJsonT);
}
