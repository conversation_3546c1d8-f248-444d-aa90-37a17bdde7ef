import 'package:json_annotation/json_annotation.dart';
import 'sort_model.dart';
part 'pageable_model.g.dart';

@JsonSerializable()
class PageableModel {
  PageableModel();

  late SortModel sort;
  late num offset;
  late num pageNumber;
  late num pageSize;
  late bool paged;
  late bool unpaged;

  factory PageableModel.fromJson(Map<String,dynamic> json) => _$PageableModelFromJson(json);
  Map<String, dynamic> toJson() => _$PageableModelToJson(this);
}
