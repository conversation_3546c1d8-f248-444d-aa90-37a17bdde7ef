
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sort_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SortModel _$SortModelFromJson(Map<String, dynamic> json) => SortModel()
  ..empty = json['empty'] as bool
  ..sorted = json['sorted'] as bool
  ..unsorted = json['unsorted'] as bool;

Map<String, dynamic> _$SortModelToJson(SortModel instance) => <String, dynamic>{
  'empty': instance.empty,
  'sorted': instance.sorted,
  'unsorted': instance.unsorted,
};
