// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ListModel<T> _$ListModelFromJson<T>(Map<String, dynamic> json, T Function(Map<String, dynamic>? json) fromJson) => ListModel<T>()
  ..content = (json['content'] as List<dynamic>?)
      ?.map((e) => fromJson(e as Map<String, dynamic>))
      .toList()
  ..pageable = json['pageable'] == null
      ? null
      : PageableModel.fromJson(json['pageable'] as Map<String, dynamic>)
  ..totalElements = json['totalElements'] as num
  ..last = json['last'] as bool
  ..totalPages = json['totalPages'] as num
  ..number = json['number'] as num
  ..size = json['size'] as num
  ..sort = json['sort'] == null
      ? null
      : SortModel.fromJson(json['sort'] as Map<String, dynamic>)
  ..numberOfElements = json['numberOfElements'] as num
  ..first = json['first'] as bool
  ..empty = json['empty'] as bool;

Map<String, dynamic> _$ListModelToJson<T>(ListModel instance, Object Function(T value) toJsonT) => <String, dynamic>{
  'content': instance.content,
  'pageable': instance.pageable,
  'totalElements': instance.totalElements,
  'last': (instance.last as List).map((e) => toJsonT(e)).toList().toString(),
  'totalPages': instance.totalPages,
  'number': instance.number,
  'size': instance.size,
  'sort': instance.sort,
  'numberOfElements': instance.numberOfElements,
  'first': instance.first,
  'empty': instance.empty,
};
