/**
 * 异常数包装
 */
class MyError implements Exception {
  MyError({
    this.message,
    this.code = -1,
    this.error,
  });

  int code = -1;

  dynamic error;

  String? message;

  StackTrace? _stackTrace;

  set stackTrace(StackTrace? stack) => _stackTrace = stack;

  StackTrace? get stackTrace => _stackTrace;

  String get description => (error?.toString() ?? '');

  @override
  String toString() {
    var msg = 'MyError [$code]: $description';
    if (error is Error) {
      msg += '\n${(error as Error).stackTrace}';
    }
    if (_stackTrace != null) {
      msg += '\nSource stack:\n$stackTrace';
    }
    return msg;
  }
}