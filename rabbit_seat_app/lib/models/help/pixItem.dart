import 'package:json_annotation/json_annotation.dart';
import 'package:video_player/video_player.dart';

part 'pixItem.g.dart';

@JsonSerializable()
class PixItem {
  PixItem();

  String? id;
  // 文件路径
  String? path;
  // 文件名
  String? name;
  // 类型 0-图片；1-视频
  int type = 0;

  // 上传后的数据
  String? fileId;
  String? fileUrl;

  VideoPlayerController? controller;

  // 便捷初始化
  PixItem.easy({this.id, this.path, this.name, this.type = 0, this.fileId, this.fileUrl});

  factory PixItem.fromJson(Map<String,dynamic> json) => _$PixItemFromJson(json);
  Map<String, dynamic> toJson() => _$PixItemToJson(this);
}
