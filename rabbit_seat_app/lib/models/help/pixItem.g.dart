// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pixItem.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************


PixItem _$PixItemFromJson(Map<String, dynamic> json) => PixItem()
  ..id = json['id'] as String?
  ..path = json['path'] as String?
  ..name = json['name'] as String?
  ..type = json['type'] as int
  ..fileId = json['fileId'] as String?
  ..fileUrl = json['fileUrl'] as String?;


Map<String, dynamic> _$PixItemToJson(PixItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'path': instance.path,
      'name': instance.name,
      'type': instance.type,
      'fileId': instance.fileId,
      'fileUrl': instance.fileUrl,
    };
