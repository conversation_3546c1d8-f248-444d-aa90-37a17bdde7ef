import 'package:json_annotation/json_annotation.dart';

part 'likeModel.g.dart';

@JsonSerializable()
class LikeModel {
  LikeModel();

  String? id;
  String? createBy;
  String? updateBy;
  String? status;
  String? userId;
  late num createOn;
  late num updateOn;

  factory LikeModel.fromJson(Map<String,dynamic> json) => _$LikeModelFromJson(json);
  Map<String, dynamic> toJson() => _$LikeModelToJson(this);
}
