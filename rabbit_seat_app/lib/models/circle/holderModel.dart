import 'package:json_annotation/json_annotation.dart';
import 'package:rabbit_seat_app/models/user/baby_model.dart';
import 'package:rabbit_seat_app/models/user/contacts_model.dart';
part 'holderModel.g.dart';

@JsonSerializable()
class HolderModel {
  HolderModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? name;
  String? tuyaId;
  // String? password;
  String? avatar;
  late num sex;
  String? nickName;
  String? city;
  String? telephone;
  late List<ContactsModel>? emergencyContacts;
  late List<BabyModel>? babyInfos;
  
  factory HolderModel.fromJson(Map<String,dynamic> json) => _$HolderModelFromJson(json);
  Map<String, dynamic> toJson() => _$HolderModelToJson(this);
}
