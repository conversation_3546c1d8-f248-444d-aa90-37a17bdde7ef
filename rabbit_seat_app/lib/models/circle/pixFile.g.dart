// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pixFile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PixFile _$PixFileFromJson(Map<String, dynamic> json) => PixFile()
  ..fileUrl = json['fileUrl'] as String?
  ..id = json['id'] as String?
  ..createBy = json['createBy'] as String?
  ..createOn = json['createOn'] as num
  ..updateBy = json['updateBy'] as String?
  ..updateOn = json['updateOn'] as num
  ..status = json['status'] as String?
  ..filePath = json['filePath'] as String?
  ..fileName = json['fileName'] as String?
  ..originalFilename = json['originalFilename'] as String?;

Map<String, dynamic> _$PixFileToJson(PixFile instance) => <String, dynamic>{
      'fileUrl': instance.fileUrl,
      'id': instance.id,
      'createBy': instance.createBy,
      'createOn': instance.createOn,
      'updateBy': instance.updateBy,
      'updateOn': instance.updateOn,
      'status': instance.status,
      'filePath': instance.filePath,
      'fileName': instance.fileName,
      'originalFilename': instance.originalFilename,
    };
