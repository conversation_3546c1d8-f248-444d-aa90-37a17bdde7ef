import 'package:json_annotation/json_annotation.dart';
import 'package:rabbit_seat_app/models/user/index.dart';

part 'commentModel.g.dart';

@JsonSerializable()
class CommentModel {
  CommentModel();

  String? id;
  String? createBy;
  String? updateBy;
  String? status;
  String? content;
  String? articleId;
  late num createOn;
  late num updateOn;
  List<CommentModel>? children;
  UserModel? user;
  bool showAll = false;

  factory CommentModel.fromJson(Map<String,dynamic> json) => _$CommentModelFromJson(json);
  Map<String, dynamic> toJson() => _$CommentModelToJson(this);
}
