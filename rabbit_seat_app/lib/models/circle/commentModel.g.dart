// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'commentModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommentModel _$CommentModelFromJson(Map<String, dynamic> json) => CommentModel()
  ..id = json['id'] as String?
  ..createBy = json['createBy'] as String?
  ..updateBy = json['updateBy'] as String?
  ..status = json['status'] as String?
  ..content = json['content'] as String?
  ..articleId = json['articleId'] as String?
  ..createOn = json['createOn'] as num
  ..updateOn = json['updateOn'] as num
  ..children = (json['children'] as List<dynamic>?)
      ?.map((e) => CommentModel.fromJson(e as Map<String, dynamic>))
      .toList()
  ..user = UserModel.fromJson(json['user'] as Map<String, dynamic>);

Map<String, dynamic> _$CommentModelToJson(CommentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
    };
