// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'holderModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HolderModel _$HolderModelFromJson(Map<String, dynamic> json) => HolderModel()
  ..id = json['id'] as String?
  ..createBy = json['createBy'] as String?
  ..createOn = json['createOn'] as num
  ..updateBy = json['updateBy'] as String?
  ..updateOn = json['updateOn'] as num
  ..status = json['status'] as String?
  ..name = json['name'] as String?
  ..tuyaId = json['tuyaId'] as String?
  // ..password = json['password'] as String?
  ..avatar = json['avatar'] as String?
  ..sex = json['sex'] as num
  ..nickName = json['nickName'] as String?
  ..city = json['city'] as String?
  ..telephone = json['telephone'] as String?
  ..emergencyContacts = (json['emergencyContacts'] as List<dynamic>?)
      ?.map((e) => ContactsModel.fromJson(e as Map<String, dynamic>))
      .toList()
  ..babyInfos = (json['babyInfos'] as List<dynamic>?)
      ?.map((e) => BabyModel.fromJson(e as Map<String, dynamic>))
      .toList();

Map<String, dynamic> _$HolderModelToJson(HolderModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createBy': instance.createBy,
      'createOn': instance.createOn,
      'updateBy': instance.updateBy,
      'updateOn': instance.updateOn,
      'status': instance.status,
      'name': instance.name,
      'tuyaId': instance.tuyaId,
      // 'password': instance.password,
      'avatar': instance.avatar,
      'sex': instance.sex,
      'nickName': instance.nickName,
      'city': instance.city,
      'telephone': instance.telephone,
      'emergencyContacts': instance.emergencyContacts,
      'babyInfos': instance.babyInfos,
    };
