import 'package:json_annotation/json_annotation.dart';
import "pixFile.dart";
part 'banner.g.dart';

@JsonSerializable()
class BannerModel {
  BannerModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? name;
  PixFile? file;
  String? fileUrl;
  late num orderIndex;
  String? linkUrl;

  factory BannerModel.fromJson(Map<String, dynamic> json) =>
      _$BannerFrom<PERSON>son(json);
  Map<String, dynamic> toJson() => _$BannerTo<PERSON>son(this);
}
