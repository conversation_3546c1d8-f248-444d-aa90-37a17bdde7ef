import 'package:json_annotation/json_annotation.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:video_player/video_player.dart';

part 'fileModel.g.dart';

@JsonSerializable()
class FileModel {
  FileModel();

  // 0 图片；1 视频
  int type = 0;

  String? id;
  String? createBy;
  num? createOn;
  String? updateBy;
  num? updateOn;
  String? status;
  String? filePath;
  String? originalFilename;

  String? fileUrl;
  String? fileName;

  /// 缩略图
  String? get thumbnailUrl {
    if (fileUrl == null) return null;
    late String _url;
    if (type == 0) {
      _url = fileUrl! + '?x-oss-process=image/resize,m_fill,w_345';
    } else {
      _url = fileUrl! +
          '?x-oss-process=video/snapshot,t_1000,f_jpg,w_345,m_fast';
    }
    if (_url.startsWith('http')) {
      return _url;
    } else {
      return kReleaseBaseUrl + _url;
    }
  }

  // VideoPlayerController? controller;

  factory FileModel.fromJson(Map<String, dynamic> json) =>
      _$FileModelFromJson(json);

  Map<String, dynamic> toJson() => _$FileModelToJson(this);
}
