import 'package:json_annotation/json_annotation.dart';
import 'package:video_player/video_player.dart';

part 'videoModel.g.dart';

@JsonSerializable()
class VideoModel {
  VideoModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? filePath;
  String? fileName;
  String? originalFilename;
  String? fileUrl;

  VideoPlayerController? controller;

  factory VideoModel.fromJson(Map<String,dynamic> json) => _$VideoModelFromJson(json);
  Map<String, dynamic> toJson() => _$VideoModelToJson(this);
}

