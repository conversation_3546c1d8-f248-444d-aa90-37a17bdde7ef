// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'circleModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CircleModel _$CircleModelFromJson(Map<String, dynamic> json) {
  CircleModel circleModel = CircleModel()
    ..isUserLike = json['isUserLike'] as bool
    ..commentCount = json['commentCount'] as num
    ..refusedInfo = json['refusedInfo'] as String?
    ..articlePhoto = json['articlePhoto'] as String?
    ..id = json['id'] as String?
    ..createBy = HolderModel.fromJson(json['createBy'] as Map<String, dynamic>)
    ..createOn = json['createOn'] as int
    ..updateBy = json['updateBy'] as String?
    ..updateOn = json['updateOn'] as num
    ..content = json['content'] as String?
    ..locationInfo = json['locationInfo'] as String?
    ..longitude = json['longitude'] as num?
    ..latitude = json['latitude'] as num?
    // ..photos = (json['photos'] as List<dynamic>?)
    //     ?.map((e) => PhotoModel.fromJson(e as Map<String, dynamic>))
    //     .toList()
    // ..videos = (json['videos'] as List<dynamic>?)
    //     ?.map((e) => VideoModel.fromJson(e as Map<String, dynamic>))
    //     .toList()
    ..photos = (json['photos'] as List<dynamic>?)
        ?.map((e) => FileModel.fromJson(e as Map<String, dynamic>)..type = 0)
        .toList()
    ..videos = (json['videos'] as List<dynamic>?)
        ?.map((e) => FileModel.fromJson(e as Map<String, dynamic>)..type = 1)
        .toList()
    ..photosJson = json['photosJson'] as String?
    ..videosJson = json['videosJson'] as String?
    ..likeCount = json['likeCount'] as num
    ..viewCount = json['viewCount'] as num
    ..canComment = json['canComment'] as bool
    ..comments = (json['comments'] as List<dynamic>?)
        ?.map((e) => CommentModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..postStatus = json['postStatus'] as String?
    ..systemUpdateBy = json['systemUpdateBy'] as String?
    ..status = json['status'] as String?
    ..likeList = (json['likeList'] as List<dynamic>?)
        ?.map((e) => LikeModel.fromJson(e as Map<String, dynamic>))
        .toList();

  if (circleModel.photosJson != null) {
    try {
      List<dynamic> _items = jsonDecode(circleModel.photosJson!);
      circleModel.photoFiles = _items.map((e) => FileModel.fromJson(e)..type = 0).toList();
    } catch (e) {
      print(">>> $e");
    }
  }

  if (circleModel.videosJson != null) {
    try {
      List<dynamic> _items = jsonDecode(circleModel.videosJson!);
      circleModel.videoFiles = _items.map((e) => FileModel.fromJson(e)..type = 1).toList();
    } catch (e) {
      print(">>> $e");
    }
  }

  return circleModel;
}

Map<String, dynamic> _$CircleModelToJson(CircleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createBy': instance.createBy,
      'createOn': instance.createOn,
      'updateBy': instance.updateBy,
      'updateOn': instance.updateOn,
      'content': instance.content,
      'locationInfo': instance.locationInfo,
      'longitude': instance.longitude,
      'latitude': instance.latitude,
      'photos': instance.photos,
      'videos': instance.videos,
      'likeCount': instance.likeCount,
      'viewCount': instance.viewCount,
      'canComment': instance.canComment,
      'comments': instance.comments,
      'postStatus': instance.postStatus,
      'systemUpdateBy': instance.systemUpdateBy,
      'status': instance.status,
      'likeList': instance.likeList,
      "isUserLike": instance.isUserLike,
      'commentCount': instance.commentCount,
      'refusedInfo': instance.refusedInfo,
      'articlePhoto': instance.articlePhoto
    };
