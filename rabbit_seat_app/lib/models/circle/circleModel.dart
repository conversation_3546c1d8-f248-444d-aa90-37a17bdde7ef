import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'fileModel.dart';
import "holderModel.dart";
import "commentModel.dart";
import "likeModel.dart";
part 'circleModel.g.dart';

@JsonSerializable()
class CircleModel {
  CircleModel();

  String? id;
  HolderModel? createBy;
  late int createOn;
  String? updateBy;
  late num updateOn;
  String? content;
  String? locationInfo;
  num? longitude;
  num? latitude;
  // List<PhotoModel>? photos;
  // List<VideoModel>? videos;
  List<FileModel>? photos;
  List<FileModel>? videos;
  String? photosJson;
  String? videosJson;
  late num likeCount;
  late num viewCount;
  late bool canComment;
  late num commentCount;    // 新增
  String? refusedInfo;      // 新增
  bool isUserLike = false;  // 新增
  String? articlePhoto;     // 新增
  List<CommentModel>? comments;
  String? postStatus;
  String? systemUpdateBy;
  String? status;
  List<LikeModel>? likeList;

  // 文件数据列表
  List<FileModel>? photoFiles;
  List<FileModel>? videoFiles;

  // /// 是否电站
  // bool isLike = false;
  // /// 是否浏览
  // bool isRead = false;

  // 是否被举报
  // bool isReport = false;

  // /// 获取图片影像数据
  List<FileModel> getPixDatas() {
    List<FileModel> _datas = [];

    if (photoFiles != null || videoFiles != null) {
      if (photoFiles != null && photoFiles!.length > 0) {
        _datas.addAll(photoFiles!);
      }
      if (videoFiles != null && videoFiles!.length > 0) {
        _datas.addAll(videoFiles!);
      }
    } else {
      if (videos != null && videos!.length > 0) {
        _datas.addAll(videos!);
      }
      if (photos != null && photos!.length > 0) {
        _datas.addAll(photos!);
      }
    }
    return _datas;
  }

  factory CircleModel.fromJson(Map<String,dynamic> json) => _$CircleModelFromJson(json);
  Map<String, dynamic> toJson() => _$CircleModelToJson(this);
}



