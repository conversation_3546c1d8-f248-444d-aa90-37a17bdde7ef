import 'package:json_annotation/json_annotation.dart';

part 'photoModel.g.dart';

@JsonSerializable()
class PhotoModel {
  PhotoModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? filePath;
  String? fileName;
  String? originalFilename;
  String? fileUrl;
  
  factory PhotoModel.fromJson(Map<String,dynamic> json) => _$PhotoModelFromJson(json);
  Map<String, dynamic> toJson() => _$PhotoModelToJson(this);
}
