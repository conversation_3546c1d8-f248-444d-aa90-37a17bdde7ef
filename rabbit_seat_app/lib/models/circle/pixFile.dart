import 'package:json_annotation/json_annotation.dart';

part 'pixFile.g.dart';

@JsonSerializable()
class PixFile {
  PixFile();

  String? fileUrl;
  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? filePath;
  String? fileName;
  String? originalFilename;
  
  factory PixFile.fromJson(Map<String,dynamic> json) => _$PixFileFromJson(json);
  Map<String, dynamic> toJson() => _$PixFileToJson(this);
}
