import 'package:json_annotation/json_annotation.dart';

part 'pixModel.g.dart';

@JsonSerializable()
class PixModel {
  PixModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? filePath;
  String? fileName;
  String? originalFilename;
  String? fileUrl;

  factory PixModel.fromJson(Map<String,dynamic> json) => _$PixModelFromJson(json);
  Map<String, dynamic> toJson() => _$PixModelToJson(this);
}

