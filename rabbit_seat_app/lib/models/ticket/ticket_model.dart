import 'package:json_annotation/json_annotation.dart';
part 'ticket_model.g.dart';

@JsonSerializable()
class TicketModel {
  TicketModel();


  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? name;
  late num expiredTime;
  late num price;
  late num fullPrice;
  String? description;
  String? taobaoLink;

  factory TicketModel.fromJson(Map<String,dynamic> json) => _$TicketModelFromJson(json);
  Map<String, dynamic> toJson() => _$TicketModelToJson(this);
}
