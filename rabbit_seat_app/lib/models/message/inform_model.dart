import 'package:json_annotation/json_annotation.dart';
part 'inform_model.g.dart';

@JsonSerializable()
class InformModel {
  InformModel();

  String? title;
  String? content;
  // Normal普通消息 DeviceShare设备共享DeviceEvent设备消息
  String? messageType;
  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? businessId;
  String? messageStatus;
  String? toUserId;
  // 自定义参数，是否已读
  bool isRead = false;

  factory InformModel.fromJson(Map<String,dynamic> json) => _$InformModelFromJson(json);
  Map<String, dynamic> toJson() => _$InformModelToJson(this);
}
