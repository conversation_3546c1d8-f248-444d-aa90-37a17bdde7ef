import 'package:json_annotation/json_annotation.dart';

part 'tuyaScanDevice.g.dart';

@JsonSerializable()
class TuyaScanDevice {
  TuyaScanDevice();

  String? address;
  String? configType;
  int? deviceType;
  num? flag;
  String? id;
  bool? isbind;
  String? mac;
  String? name;
  String? productId;
  String? providerName;
  String? uuid;
  
  factory TuyaScanDevice.fromJson(Map<String,dynamic> json) => _$TuyaScanDeviceFromJson(json);
  Map<String, dynamic> toJson() => _$TuyaScanDeviceToJson(this);
}
