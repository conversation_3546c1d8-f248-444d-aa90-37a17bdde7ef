import 'package:json_annotation/json_annotation.dart';
import 'package:rabbit_seat_app/models/home/<USER>';
import "tuyaDevice.dart";
part 'tuyaHome.g.dart';

@JsonSerializable()
class TuyaHome {
  TuyaHome();


  // 改字段
  num? homeId;
  String? name;
  num? dealStatus;
  num? longitude;
  num? latitude;
  bool? admin;
  String? backgroundUrl;
  num? role;
  num? displayOrder;
  String? geoName;

  /// 自定义数据
  List<HomeDeviceModel> devices = [];

  factory TuyaHome.fromJson(Map<String,dynamic> json) => _$TuyaHomeFromJson(json);
  Map<String, dynamic> toJson() => _$TuyaHomeToJson(this);
}
