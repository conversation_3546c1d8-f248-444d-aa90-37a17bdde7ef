// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tuyaHome.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TuyaHome _$Tuya<PERSON>ome<PERSON>rom<PERSON>son(Map<String, dynamic> json) => TuyaHome()
  ..name = json['name'] as String?
  // ..background = json['background'] as String?
  // ..lon = json['lon'] as num?
  // ..lat = json['lat'] as num?
  ..geoName = json['geoName'] as String?
  ..homeId = json['homeId'] as num?
  ..admin = json['admin'] as bool?
  // ..inviteName = json['inviteName'] as String?
  // ..deviceList = (json['deviceList'] as List<dynamic>?)
  //     ?.map((e) => TuyaDevice.fromJson(e as Map<String, dynamic>))
  //     .toList()
  ..backgroundUrl = json['backgroundUrl'] as String?
  ..dealStatus = json['dealStatus'] as num?
  ..longitude = json['longitude'] as num?
  ..latitude = json['latitude'] as num?
  ..role = json['role'] as num?
  ..displayOrder = json['displayOrder'] as num?;

Map<String, dynamic> _$TuyaHomeToJson(TuyaHome instance) => <String, dynamic>{
      'name': instance.name,
      // 'background': instance.background,
      // 'lon': instance.lon,
      // 'lat': instance.lat,
      'geoName': instance.geoName,
      'homeId': instance.homeId,
      'admin': instance.admin,
      // 'inviteName': instance.inviteName,
      // 'deviceList': instance.deviceList,
      'backgroundUrl': instance.backgroundUrl,
      'dealStatus': instance.dealStatus,
      'longitude': instance.longitude,
      'latitude': instance.latitude,
      'role': instance.role,
      'displayOrder': instance.displayOrder,
      'devices': instance.devices.map((e) => e.toJson()).toList()
      // 'deviceList': instance.deviceList,
    };
