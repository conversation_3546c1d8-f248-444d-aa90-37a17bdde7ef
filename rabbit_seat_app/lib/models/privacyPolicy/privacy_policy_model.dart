import 'package:json_annotation/json_annotation.dart';
import 'package:video_player/video_player.dart';

part 'privacy_policy_model.g.dart';

@JsonSerializable()
class PrivacyPolicyModel {
  PrivacyPolicyModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? name;
  String? codeName;
  String? codeValue;

  factory PrivacyPolicyModel.fromJson(Map<String,dynamic> json) => _$PrivacyPolicyModelFromJson(json);
  Map<String, dynamic> toJson() => _$PrivacyPolicyModelToJson(this);
}