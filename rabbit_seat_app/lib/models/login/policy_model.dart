import 'package:json_annotation/json_annotation.dart';
part 'policy_model.g.dart';

@JsonSerializable()
class PolicyModel {
  PolicyModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? name;
  String? codeName;
  String? codeValue;

  factory PolicyModel.fromJson(Map<String,dynamic> json) => _$PolicyModelFromJson(json);
  Map<String, dynamic> toJson() => _$PolicyModelToJson(this);
}
