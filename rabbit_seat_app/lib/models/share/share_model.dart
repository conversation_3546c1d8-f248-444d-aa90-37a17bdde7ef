import 'package:json_annotation/json_annotation.dart';
import 'package:rabbit_seat_app/models/user/index.dart';
part 'share_model.g.dart';

@JsonSerializable()
class ShareModel {
  ShareModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? shareStatus;
  late num expiredTime;
  UserModel? user;
  String? shareTelephone;
  late num homeId;
  String? deviceId;

  factory ShareModel.fromJson(Map<String,dynamic> json) => _$ShareModelFromJson(json);
  Map<String, dynamic> toJson() => _$ShareModelToJson(this);
}
