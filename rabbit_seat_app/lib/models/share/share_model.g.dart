// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'share_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShareModel _$ShareModelFromJson(Map<String, dynamic> json) => ShareModel()
  ..id = json['id'] as String?
  ..shareStatus = json['shareStatus'] as String?
  ..createBy = json['createBy'] as String?
  ..createOn = json['createOn'] as num
  ..updateBy = json['updateBy'] as String?
  ..updateOn = json['updateOn'] as num
  ..status = json['status'] as String?
  ..expiredTime = json['expiredTime'] as num
  ..user =  json['user'] != null ? UserModel.fromJson(json['user'] as Map<String, dynamic>) : null
  ..homeId = json['homeId'] as num
  ..shareTelephone = json['shareTelephone'] as String?
  ..deviceId = json['deviceId'] as String?;


Map<String, dynamic> _$ShareModelToJson(ShareModel instance) => <String, dynamic>{
  'id': instance.id,
  'shareStatus': instance.shareStatus,
  'createBy': instance.createBy,
  'createOn': instance.createOn,
  'updateBy': instance.updateBy,
  'updateOn': instance.updateOn,
  'status': instance.status,
  'expiredTime': instance.expiredTime,
  'user': instance.user,
  'homeId': instance.homeId,
  'shareTelephone': instance.shareTelephone,
  'deviceId': instance.deviceId,
};
