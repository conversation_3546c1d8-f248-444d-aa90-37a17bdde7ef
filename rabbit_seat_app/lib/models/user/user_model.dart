import 'package:json_annotation/json_annotation.dart';
import "baby_model.dart";
import "contacts_model.dart";
part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  UserModel();

  String? id;
  String? createBy;
  num? createOn;
  String? updateBy;
  num? updateOn;
  String? status;
  String? name;
  String? tuyaId;
  String? avatar;
  late num sex;
  String? nickName;
  String? city;
  String? telephone;
  List<BabyModel>? babyInfos;
  List<ContactsModel>? emergencyContacts;
  String? openId;
  // 点赞评论
  bool? hasNewAppComment;

  factory UserModel.fromJson(Map<String,dynamic> json) => _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}
