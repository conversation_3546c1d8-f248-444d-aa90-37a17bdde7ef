import 'package:json_annotation/json_annotation.dart';

part 'baby_model.g.dart';

@JsonSerializable()
class BabyModel {
  BabyModel();


  String? id;
  String? name;
  String? sex;
  String? birthday;

  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;


  factory BabyModel.fromJson(Map<String,dynamic> json) => _$BabyModelFromJson(json);
  Map<String, dynamic> toJson() => _$BabyModelToJson(this);
}
