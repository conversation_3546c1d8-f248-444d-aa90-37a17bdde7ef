// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel()
  ..name = json['name'] as String?
  ..id = json['id'] as String?
  ..createOn = json['createOn'] as num?
  ..createBy = json['createBy'] as String?
  ..updateOn = json['updateOn'] as num?
  ..updateBy = json['updateBy'] as String?
  ..status = json['status'] as String?
  ..tuyaId = json['tuyaId'] as String?
  ..avatar = json['avatar'] as String?
  ..nickName = json['nickName'] as String?
  ..sex = json['sex'] as num
  ..city = json['city'] as String?
  ..telephone = json['telephone'] as String?
  ..babyInfos = (json['babyInfos'] as List<dynamic>?)
      ?.map((e) => BabyModel.fromJson(e as Map<String, dynamic>))
      .toList()
  ..emergencyContacts = (json['emergencyContacts'] as List<dynamic>?)
      ?.map((e) => ContactsModel.fromJson(e as Map<String, dynamic>))
      .toList()
  ..openId = json['openId'] as String?
  ..hasNewAppComment = json['hasNewAppComment'] as bool?;

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
  'name': instance.name,
  'id': instance.id,
  'createOn': instance.createOn,
  'createBy': instance.createBy,
  'updateBy': instance.updateBy,
  'updateOn': instance.updateOn,
  'tuyaId': instance.tuyaId,
  'avatar': instance.avatar,
  'nickName': instance.nickName,
  'status': instance.status,
  'sex': instance.sex,
  'city': instance.city,
  'telephone': instance.telephone,
  'babyInfos': instance.babyInfos,
  'emergencyContacts': instance.emergencyContacts,
  'openId': instance.openId,
  'hasNewAppComment': instance.hasNewAppComment
};
