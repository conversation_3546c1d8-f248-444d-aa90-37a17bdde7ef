// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Profile _$ProfileFromJson(Map<String, dynamic> json) => Profile()
  ..user = json['user'] == null
      ? null
      : UserModel.fromJson(json['user'] as Map<String, dynamic>)
  ..token = json['token'] as String?
  ..theme = json['theme'] as num?
  ..cache = json['cache'] == null
      ? null
      : CacheConfig.fromJson(json['cache'] as Map<String, dynamic>)
  ..lastLogin = json['lastLogin'] as String?
  ..locale = json['locale'] as String?
  ..devices = (json['devices'] as List<dynamic>?)
      ?.map((e) => HomeDeviceModel.fromJson(e))
      .toList()
  ..currentDevice = json['currentDevice'] != null ?
      HomeDeviceModel.fromJson(json['currentDevice'] as Map<String, dynamic>) : null;

Map<String, dynamic> _$ProfileToJson(Profile instance) => <String, dynamic>{
      'user': instance.user,
      'token': instance.token,
      'theme': instance.theme,
      'cache': instance.cache,
      'lastLogin': instance.lastLogin,
      'locale': instance.locale,
      'devices': instance.devices,
      'currentDevice': instance.currentDevice,
    };
