import 'package:json_annotation/json_annotation.dart';

part 'interact_model.g.dart';

@JsonSerializable()
class InteractModel {
  InteractModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? userName;
  String? userAvatar;
  String? messageType;
  String? comment;
  String? parentComment;
  String? parentCommentUserName;
  String? articleContent;
  String? articlePhoto;
  String? toUser;

  factory InteractModel.fromJson(Map<String,dynamic> json) => _$InteractModelFromJson(json);
  Map<String, dynamic> toJson() => _$InteractModelToJson(this);
}
