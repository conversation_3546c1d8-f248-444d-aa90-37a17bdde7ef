// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'interact_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InteractModel _$InteractModelFromJson(Map<String, dynamic> json) =>
    InteractModel()
      ..id = json['id'] as String?
      ..createBy = json['createBy'] as String?
      ..createOn = json['createOn'] as num
      ..updateBy = json['updateBy'] as String?
      ..updateOn = json['updateOn'] as num
      ..status = json['status'] as String?
      ..userName = json['userName'] as String?
      ..userAvatar = json['userAvatar'] as String?
      ..messageType = json['messageType'] as String?
      ..comment = json['comment'] as String?
      ..parentComment = json['parentComment'] as String?
      ..parentCommentUserName = json['parentCommentUserName'] as String?
      ..articleContent = json['articleContent'] as String?
      ..articlePhoto = json['articlePhoto'] as String?
      ..toUser = json['toUser'] as String?;

Map<String, dynamic> _$InteractModelToJson(InteractModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createBy': instance.createBy,
      'createOn': instance.createOn,
      'updateBy': instance.updateBy,
      'updateOn': instance.updateOn,
      'userName': instance.userName,
      'userAvatar': instance.userAvatar,
      'messageType': instance.messageType,
      'comment': instance.comment,
      'parentComment': instance.parentComment,
      'parentCommentUserName': instance.parentCommentUserName,
      'articleContent': instance.articleContent,
      'articlePhoto': instance.articlePhoto,
      'toUser': instance.toUser
    };
