// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_device.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HomeDeviceModel _$HomeDeviceModelFromJson(Map<String, dynamic> json) =>
    HomeDeviceModel()
      ..id = json['id'] as String?
      ..name = json['name'] as String?
      ..active_time = json['active_time'] as num?
      ..biz_type = json['biz_type'] as num?
      ..uid = json['uid'] as String?
      ..local_key = json['local_key'] as String?
      ..category = json['category'] as String?
      ..product_id = json['product_id'] as String?
      ..product_name = json['product_name'] as String?
      ..uuid = json['uuid'] as String?
      ..owner_id = json['owner_id'] as String?
      ..name = json['name'] as String?
      ..sub = (json['sub'] as bool?) ?? false
      ..online = (json['online'] as bool?) ?? false
      ..isShare = (json['isShare'] as bool?) ?? false
      ..status = (json['status'] as List<dynamic>?)
          ?.map((e) => HDStatusModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..icon = json['icon'] as String?
      ..ip = json['ip'] as String?
      ..model = json['model'] as String?
      ..lon = json['lon'] as String?
      ..lat = json['lat'] as String?;

Map<String, dynamic> _$HomeDeviceModelToJson(HomeDeviceModel instance) =>
    <String, dynamic>{
      "id": instance.id,
      'name': instance.name,
      'active_time': instance.active_time,
      'biz_type': instance.biz_type,
      'uid': instance.uid,
      'local_key': instance.local_key,
      'category': instance.category,
      'product_id': instance.product_id,
      'product_name': instance.product_name,
      'uuid': instance.uuid,
      'owner_id': instance.owner_id,
      'sub': instance.sub,
      'online': instance.online,
      'isShare': instance.isShare,
      'status': instance.status,
      'icon': instance.icon,
      'ip': instance.ip,
      'model': instance.model,
      'lon': instance.lon,
      'lat': instance.lat
    };
