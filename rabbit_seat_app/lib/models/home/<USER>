import 'package:date_format/date_format.dart';
import 'package:json_annotation/json_annotation.dart';

import 'home_device_status.dart';

part 'home_device.g.dart';

@JsonSerializable()
class HomeDeviceModel {
  HomeDeviceModel();

  String? id;
  String? name;
  String? uid;
  String? local_key;
  String? category;
  String? product_id;
  String? product_name;
  String? uuid;
  String? owner_id;
  bool sub = false;
  bool online = false;
  bool isShare = false;
  List<HDStatusModel>? status;
  num? active_time;
  num? biz_type;
  String? icon;
  String? ip;
  String? model;
  String? lon;
  String? lat;

  String get getExpTime {
    if (active_time == null) return '';
    return formatDate(DateTime.fromMillisecondsSinceEpoch(active_time!.toInt()),
        [yyyy, '-', mm, '-', dd]);
  }

  factory HomeDeviceModel.fromJson(Map<String, dynamic> json) =>
      _$HomeDeviceModelFromJson(json);
  Map<String, dynamic> toJson() => _$HomeDeviceModelToJson(this);
}
