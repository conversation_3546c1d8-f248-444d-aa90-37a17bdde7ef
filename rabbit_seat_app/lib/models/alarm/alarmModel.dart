import 'package:json_annotation/json_annotation.dart';
import 'package:rabbit_seat_app/models/user/index.dart';

import '../device/device_model.dart';

part 'alarmModel.g.dart';

@JsonSerializable()
class AlarmModel {
  AlarmModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? eventType;
  DeviceModel? device;
  UserModel? user;
  String? eventStatus;
  String? eventTime;
  String? handleNote;
  
  factory AlarmModel.fromJson(Map<String,dynamic> json) => _$AlarmModelFromJson(json);
  Map<String, dynamic> toJson() => _$AlarmModelToJson(this);
}
