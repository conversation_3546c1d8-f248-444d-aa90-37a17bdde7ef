import 'package:json_annotation/json_annotation.dart';


part 'device_sim_model.g.dart';

@JsonSerializable()
class DeviceSimModel {
  DeviceSimModel();

  String? activeTime;
  String? create_time;
  String? effectiveTime;
  String? expiredTime;
  String? imei;
  String? status;
  double? totalFlow;
  double? usedFlow;

  factory DeviceSimModel.fromJson(Map<String, dynamic> json) =>
      _$DeviceSimModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceSimModelToJson(this);
}
