import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:rabbit_seat_app/models/user/index.dart';

import '../home/<USER>';

part 'device_model.g.dart';

@JsonSerializable()
class DeviceModel {
  DeviceModel();

  String? iccid;
  String? expiredTime;
  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? name;
  String? model;
  late bool online;
  String? ownerId;
  String? originalId;
  late num lastEventTime;
  String? productId;
  String? productName;
  String? statusJson;
  String? uuid;
  late num activeTime;
  List<HDStatusModel>? statusList;
  String? lat;
  String? lon;
  String? ip;
  String? icon;
  late num homeId;
  UserModel? user;
  String? userId;
  String? userName;
  String? userTelephone;
  String? deviceStatus;
  String? leaveWarnStatus;
  String? configJson;
  // 告警
  bool leaveWarm = false;

  bool? isShare = false;

  /// --------------------------------------
  // 自定义参数
  // 是否在线
  bool isOnline = false;

  bool isHighTemp = false;

  // 远程模式
  bool isRemote = false;

  // 通风
  bool isWind = false;
  bool isWindDoing = false;

  // 加热
  bool isHost = false;
  bool isHostDoing = false;

  // 自动
  bool isAuto = false;
  bool isAutoDoing = false;

  // 左保护
  bool isLeft = false;
  bool isLeftDoing = false;

  // 右保护
  bool isRight = false;
  bool isRightDoing = false;

  // 氛围灯
  bool isLamp = false;
  bool isLampDoing = false;

  // 温度
  String? temp;

  // 电量
  int energy = 100;

  // 控制
  late num mcuLpTimer;

  bool autoRotate = false;

  bool assistRotate = false;
  bool muteModeSwitch = false;
  num autoFanTemp = 26;
  num autoHeatTemp = 26;

  /// 低功耗
  bool lptimOnoff = false;

  num errValue = 0;

  num? remoteExpiredTime;

  String? mcuVersion;

  String? networkVersion;

  /// 获取状态数据
  List<HDStatusModel> getStateusDatas() {
    if (statusList != null && statusList!.isNotEmpty) {
      return statusList!;
    }
    if (statusJson != null && statusJson!.isNotEmpty) {
      try {
        print(">>> statusJson:$statusJson");

        List<dynamic>? _list = jsonDecode(statusJson!);
        if (_list != null) {
          List<HDStatusModel> _datas = _list
              .map((e) => HDStatusModel.fromJson(e as Map<String, dynamic>))
              .toList();
          return _datas;
        }
      } catch (e) {
        return [];
      }
    }
    return [];
  }

  factory DeviceModel.fromJson(Map<String, dynamic> json) =>
      _$DeviceModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceModelToJson(this);
}
