// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceModel _$DeviceModelFromJson(Map<String, dynamic> json) {
  DeviceModel deviceModel = DeviceModel()
    ..expiredTime = json['expiredTime'] as String?
    ..iccid = json['iccid'] as String?
    ..id = json['id'] as String?
    ..createBy = json['createBy'] as String?
    ..createOn = json['createOn'] as num
    ..updateBy = json['updateBy'] as String?
    ..updateOn = json['updateOn'] as num
    ..status = json['status'] as String?
    ..name = json['name'] as String?
    ..model = json['model'] as String?
    ..online = json['online'] as bool
    ..isOnline = json['online'] as bool
    ..isShare = json['isShare'] as bool?
    ..ownerId = json['ownerId'] as String?
    ..originalId = json['originalId'] as String?
    ..lastEventTime = json['lastEventTime'] as num
    ..productId = json['productId'] as String?
    ..productName = json['productName'] as String?
    ..statusJson = json['statusJson'] as String?
    ..uuid = json['uuid'] as String?
    ..activeTime = json['activeTime'] as num
    ..lat = json['lat'] as String?
    ..lon = json['lon'] as String?
    ..ip = json['ip'] as String?
    ..icon = json['icon'] as String?
    ..homeId = json['homeId'] as num
    ..user = json['user'] == null
        ? null
        : UserModel.fromJson(json['user'] as Map<String, dynamic>)
    ..userId = json['userId'] as String?
    ..userName = json['userName'] as String?
    ..userTelephone = json['userTelephone'] as String?
    ..deviceStatus = json['deviceStatus'] as String?
    ..configJson = json['configJson'] as String?
    ..remoteExpiredTime = json['remoteExpiredTime'] as num?
    ..mcuVersion = json['mcuVersion'] as String?
    ..networkVersion = json['networkVersion'] as String?
    ..leaveWarnStatus = json['leaveWarnStatus'] as String?;

  // 初始化状态值
  deviceModel.statusList = (json['statusList'] as List<dynamic>?)?.map((e) {
    HDStatusModel model = HDStatusModel.fromJson(e as Map<String, dynamic>);
    if (model.code == 'recTemp') {
      deviceModel.temp = (model.value as num).toInt().toString();
    } else if (model.code == 'batPercent') {
      deviceModel.energy = (model.value as num).toInt();
    } else if (model.code == 'mcuLpTimer' && model.value is num) {
      var _val = (model.value as num).toInt();
      if (_val < 0) {
        deviceModel.isOnline = false;
      }
      if (_val == 0) {
        deviceModel.isRemote = true;
      }
      deviceModel.mcuLpTimer = model.value as num;
    } else if (model.code == 'protectionLeftSw') {
      deviceModel.isLeft = model.value as bool;
    } else if (model.code == 'protectionRightSw') {
      deviceModel.isRight = model.value as bool;
    } else if (model.code == 'F_light') {
      deviceModel.isLamp = model.value as bool;
    } else if (model.code == 'autoMode') {
      deviceModel.isAuto = model.value as bool;
    } else if (model.code == 'fanSw') {
      deviceModel.isWind = model.value as bool;
    } else if (model.code == 'hotSw') {
      deviceModel.isHost = model.value as bool;
    } else if (model.code == 'leaveWarm') {
      deviceModel.leaveWarm = model.value as bool;
    } else if (model.code == 'auto_rotate') {
      deviceModel.autoRotate = model.value as bool;
    } else if (model.code == 'assist_rotate') {
      deviceModel.assistRotate = model.value as bool;
    } else if (model.code == 'mute_mode_switch') {
      deviceModel.muteModeSwitch = model.value as bool;
    } else if (model.code == 'auto_fan_temp') {
      deviceModel.autoFanTemp = model.value as num;
    } else if (model.code == 'auto_heat_temp') {
      deviceModel.autoHeatTemp = model.value as num;
    } else if (model.code == 'err_value') {
      deviceModel.errValue = model.value as num;
    }
     else if (model.code == 'lptime_onoff') {
      deviceModel.lptimOnoff = model.value as bool;
    }
    
    return model;
  }).toList();

  if (deviceModel.isOnline == false) {
    deviceModel
      ..isLeft = false
      ..isRight = false
      ..isLamp = false
      ..isWind = false
      ..isHost = false
      ..isAuto = false;
  }
  return deviceModel;
}

Map<String, dynamic> _$DeviceModelToJson(DeviceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createBy': instance.createBy,
      'createOn': instance.createOn,
      'updateBy': instance.updateBy,
      'updateOn': instance.updateOn,
      'status': instance.status,
      'name': instance.name,
      'model': instance.model,
      'online': instance.online,
      'isShare': instance.isShare,
      'ownerId': instance.ownerId,
      'originalId': instance.originalId,
      'lastEventTime': instance.lastEventTime,
      'productId': instance.productId,
      'productName': instance.productName,
      'statusJson': instance.statusJson,
      'uuid': instance.uuid,
      'activeTime': instance.activeTime,
      'statusList': instance.statusList,
      'lat': instance.lat,
      'lon': instance.lon,
      'ip': instance.ip,
      'icon': instance.icon,
      'homeId': instance.homeId,
      'user': instance.user,
      'userId': instance.userId,
      'userName': instance.userName,
      'userTelephone': instance.userTelephone,
      'deviceStatus': instance.deviceStatus,
      'configJson': instance.configJson,
      'iccid': instance.iccid,
      'expiredTime': instance.expiredTime,
      'remoteExpiredTime': instance.remoteExpiredTime,
      'mcuVersion': instance.mcuVersion,
      'networkVersion': instance.networkVersion,
      'leaveWarnStatus': instance.leaveWarnStatus
    };
