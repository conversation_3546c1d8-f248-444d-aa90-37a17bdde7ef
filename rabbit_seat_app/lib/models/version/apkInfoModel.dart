import 'package:json_annotation/json_annotation.dart';
import "apkFileModel.dart";
part 'apkInfoModel.g.dart';

@JsonSerializable()
class ApkInfoModel {
  ApkInfoModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? name;
  String? content;
  ApkFileModel? file;
  String? version;
  String? appType;
  
  factory ApkInfoModel.fromJson(Map<String,dynamic> json) => _$ApkInfoModelFromJson(json);
  Map<String, dynamic> toJson() => _$ApkInfoModelToJson(this);
}
