import 'package:json_annotation/json_annotation.dart';

part 'apkFileModel.g.dart';

@JsonSerializable()
class ApkFileModel {
  ApkFileModel();

  String? id;
  String? createBy;
  late num createOn;
  String? updateBy;
  late num updateOn;
  String? status;
  String? filePath;
  String? fileName;
  String? originalFilename;
  String? fileUrl;
  
  factory ApkFileModel.fromJson(Map<String,dynamic> json) => _$ApkFileModelFromJson(json);
  Map<String, dynamic> toJson() => _$ApkFileModelToJson(this);
}
