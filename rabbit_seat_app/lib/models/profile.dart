import 'package:json_annotation/json_annotation.dart';
import 'package:rabbit_seat_app/models/home/<USER>';
import 'package:rabbit_seat_app/models/user/index.dart';

import 'cache_config.dart';
// import "cacheConfig.dart";
part 'profile.g.dart';

@JsonSerializable()
class Profile {
  Profile();

  UserModel? user;
  String? token;
  num? theme;
  CacheConfig? cache;
  String? lastLogin;
  String? locale;
  String? currentDeviceId;

  // 设备列表
  List<HomeDeviceModel>? devices;
  // 当前设备
  HomeDeviceModel? currentDevice;

  factory Profile.fromJson(Map<String,dynamic> json) => _$ProfileFromJson(json);
  Map<String, dynamic> toJson() => _$ProfileToJson(this);
}