// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'result_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ResultModel<T> _$ResultModelFromJson<T>(Map<String, dynamic> json,
    T Function(Object? json) fromJsonT) =>
    ResultModel<T>(
      (json['data'] != null ? fromJsonT(json['data']) : json['data']),
      json['code'] as int,
      json['message'] as String,
      json['success'] as bool,
    );

Map<String, dynamic> _$ResultModelToJson<T>(
    ResultModel<T> instance,
    Object? Function(T value) toJsonT,
    ) =>
    <String, dynamic>{
      'code': instance.code,
      'success': instance.success,
      'message': instance.message,
      'data': instance.data != null ? toJsonT(instance.data!) : null,
    };
