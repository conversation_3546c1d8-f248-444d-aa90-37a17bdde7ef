import 'package:json_annotation/json_annotation.dart';

part 'result_model.g.dart';

@JsonSerializable(
    genericArgumentFactories: true, fieldRename: FieldRename.snake)
class ResultModel<T> {
  @Json<PERSON>ey(name: 'code')
  final int code;
  @<PERSON>son<PERSON>ey(name: 'success')
  final bool success;
  @Json<PERSON>ey(name: 'message')
  final String message;
  @<PERSON>son<PERSON>ey(name: 'data')
  final T? data;

  ResultModel(this.data, this.code, this.message, this.success);

  factory ResultModel.fromJson(Map<String, dynamic> json,
      T Function(Object? json) fromJsonT) =>
      _$ResultModelFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ResultModelToJson(this, toJsonT);
}
