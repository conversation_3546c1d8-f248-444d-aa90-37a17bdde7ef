import 'package:flutter/material.dart';
import 'package:rabbit_seat_app/models/user/contacts_model.dart';
import 'package:rabbit_seat_app/views/circle/circle_details_view.dart';
import 'package:rabbit_seat_app/views/device/add_device_scan_page.dart';
import 'package:rabbit_seat_app/views/device/device_info_view.dart';
import 'package:rabbit_seat_app/views/home/<USER>';
import 'package:rabbit_seat_app/views/launch/launch_view.dart';
import 'package:rabbit_seat_app/views/main_view.dart';
import 'package:rabbit_seat_app/views/my/BarcodeScannerSimple.dart';
import 'package:rabbit_seat_app/views/my/contacts_add_view.dart';
import 'package:rabbit_seat_app/views/my/contacts_modify_view.dart';
import 'package:rabbit_seat_app/views/my/course_view.dart';
import 'package:rabbit_seat_app/views/my/interact_view.dart';
import 'package:rabbit_seat_app/views/my/setting/about_view.dart';
import 'package:rabbit_seat_app/views/my/setting_view.dart';
import 'package:rabbit_seat_app/views/my/support_view.dart';
import 'package:rabbit_seat_app/views/my/system_message_view.dart';
import 'package:rabbit_seat_app/views/my/ticket_view.dart';
import 'package:rabbit_seat_app/views/my/user_info_view.dart';
import 'package:rabbit_seat_app/views/temp_view.dart';
import 'package:rabbit_seat_app/views/user/step3.dart';

import '../views/baby/baby_add_view.dart';
import '../views/baby/baby_view.dart';
import '../views/baby/bady_modify_view.dart';
import '../views/device/add_device_view.dart';
import '../views/login/index.dart';
import '../views/my/contacts_view.dart';
import '../views/my/faq_view.dart';
import '../views/user/modify_name_view.dart';
import '../views/user/step1.dart';
import '../views/user/step2.dart';

/// 路由管理
class RoutesUtils {
  /// 页面管理
  static final Map<String, WidgetBuilder> routes = {
    // 根
    '/': (context, {arguments}) => const MainView(),
    // 测试
    '/temp': (context, {arguments}) => const TempView(),
    // 协议
    '/agreement': (context, {arguments}) => const AgreementView(),
    // 政策
    '/policy': (context, {arguments}) => const PolicyView(),
    // 绑定手机号
    // '/bind-phone': (context, {arguments}) => BindPhoneView(),
    // 快捷登录
    '/login-quick': (context, {arguments}) => const LoginQuickView(),
    // 短信登录
    '/login-code': (context, {arguments}) => const LoginPhoneView(),
    // 优惠券
    '/ticket': (context, {arguments}) => const TicketView(),
    // 安装教程
    '/course': (context, {arguments}) => const CourseView(),
    // 系统消息
    '/sys-msg': (context, {arguments}) => const SystemMsgView(),
    // 常见问题
    '/faq': (context, {arguments}) => const FAQView(),
    // 技术支持
    '/support': (context, {arguments}) => const SupportView(),
    // 用户设置
    '/setting': (context, {arguments}) => const SettingView(),
    '/setting/about': (context, {arguments}) => const AboutView(),
    // 用户信息
    '/user-info': (context, {arguments}) => const UserInfoView(),
    // 添加紧急联系人
    '/contacts-add': (context, {arguments}) => const ContactsAddView(),
    // 紧急联系人列表
    '/contacts': (context, {arguments}) => const ContactsView(),
    // 编辑紧急联系人
    '/contacts-modify': (context, {arguments}) =>
        ContactsModifyView(arguments: arguments),
    // 我的发布/互动
    '/interact': (context, {arguments}) => const InteractView(),
    // 朋友圈文章详情
    '/circle-info': (context, {arguments}) => CircleDetailsView(
          arguments: arguments,
        ),
    // 宝贝信息
    '/bady-info': (context, {arguments}) => const BadyModifyView(),
    // 添加宝贝
    '/bady-add': (context, {arguments}) => const BabyAddView(),
    // 宝贝列表
    '/bady-list': (context, {arguments}) => const BabyView(),
    // 修改昵称
    '/modify-name': (context, {arguments}) => const ModifyNameView(),
    // 修改手机号
    '/tel-step1': (context, {arguments}) => const Step1Route(),
    '/tel-step2': (context, {arguments}) => const Step2Route(),
    '/tel-step3': (context, {arguments}) => const Step3Route(),
    // 添加设备
    '/add-device': (context, {arguments}) => const DeviceAddView(),
    '/device-info': (context, {arguments}) => const DeviceInfoView(),
    '/launch': (context, {arguments}) => const LaunchPage(),
    '/barcodeScann': (context, {arguments}) => const BarcodeScannerSimple(),
    '/addDeviceScann': (context, {arguments}) => const AddDeviceScanPage(),
    '/device':(context, {arguments}) => const DeviceView(),
    // '
  };

  /// 在生成的路线
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    print(">>> onGenerateRoute:$settings");
    // 异常驳货
    try {
      // 获取到路由名称
      final String? name = settings.name;
      // 判断是否存在路由
      if (name != null && routes.containsKey(name)) {
        // 获取到路由构建函数
        final Function buider = routes[name] as Function;
        // 判断是否有函数携带
        if (settings.arguments != null) {
          // 有参数配置
          final Route _route = MaterialPageRoute(
            builder: (context) =>
                buider(context, arguments: settings.arguments),
          );
          print("路由参数携带" + settings.arguments.toString());
          return _route;
        } else {
          // 无参数配置
          final Route _route = MaterialPageRoute(
            builder: (context) => buider(context),
          );
          return _route;
        }
      } else {
        // 不存在路由地址
        print("不存在该路由地址'$name'");
        return null;
      }
    } catch (error) {
      print("在线路由生成错误！error:$error");
      return null;
    }
  }
}
