import 'package:adaptive_dialog/adaptive_dialog.dart';
import 'package:event_bus/event_bus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/controls/LoadingOverlay.dart';
import 'package:rabbit_seat_app/controls/add_device_select.dart';
import 'package:rabbit_seat_app/models/circle/banner.dart';
import 'package:rabbit_seat_app/models/device/device_model.dart';
import 'package:rabbit_seat_app/models/error.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/models/tuya/tuyaHome.dart';
import 'package:rabbit_seat_app/utils/http.dart';

class Business {
  static Color mainColor = const Color(0xFFF06428);
  static EventBus eventBus = EventBus();
  static Future<TuyaHome?> getHome() async {
    try {
      final data = await Http.get('/api/device/home');
      final result = ResultModel.fromJson(data, (json) {
        return TuyaHome.fromJson(json as Map<String, dynamic>);
      });
      if (result.code == 200) {
        return result.data;
      } else {
        EasyLoading.showToast(result.message);
      }
      return null;
    } catch (e) {
      print(">>> Error:$e");
      EasyLoading.showToast(e.toString());
      return null;
    }
  }

  static void showAddDeviceSelect(BuildContext context) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Material(
            type: MaterialType.transparency, child: AddDeviceSelect());
      },
    );

    // title: "添加设备",
    // message: "请选择添加设备类型",
    // actions: [
    //   const AlertDialogAction(
    //     key: '1',
    //     label: '搜索附近设备',
    //   ),
    //   const AlertDialogAction(
    //     key: '2',
    //     label: '二维码扫一扫',
    //   ),
    // ],
    // onPopInvokedWithResult: (isOk, result) {
    // Future.delayed(Duration(milliseconds: 100), () {
    //   if(isOk){
    //     if(result == '1'){
    //       Navigator.pushNamed(context, "/add-device");
    //     }else if(result == '2'){
    //       Navigator.pushNamed(context, "/addDeviceScann");
    //     }
    //   }
    // });

    // },
  }

  // 加载横幅数据
  static Future<List<BannerModel>> loadBannerDatas() async {
    try {
      final data = await Http.get("/api/banner/applist");
      final result = ResultModel<List<BannerModel>?>.fromJson(data, (json) {
        if (json is List) {
          return (json as List<dynamic>?)
              ?.map((e) => BannerModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        return result.data as List<BannerModel>;
      } else {
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      EasyLoading.showToast(e.message ?? "网络请求失败！");
    } catch (e) {
      EasyLoading.showToast(e.toString());
    }
    return [];
  }

  var testDeviceData = "";

  static Future<List<DeviceModel>> getDevices() async {
    try {
      // state.clone()..isAlarm = false;
      final data = await Http.get("/api/device/appList");
      final result = ResultModel<List<DeviceModel>?>.fromJson(data, (json) {
        if (json != null && (json is List<dynamic>)) {
          return (json)
              .map((e) => DeviceModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        return result.data ?? [];
      } else {
        EasyLoading.showToast(result.message);
      }
    } catch (e) {
      print('>>> Error:$e');
      EasyLoading.showToast(e.toString());
    }
    return [];
  }

   static Future<DeviceModel?> getDevice(String id) async {
    try {
      final data = await Http.get('/api/device/$id');
      final result = ResultModel<DeviceModel>.fromJson(
          data, (json) => DeviceModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (result.data != null) {
          return result.data;
        }
      } else {
        EasyLoading.showToast(result.message);
      }
    } catch (e) {
      print('>>> Error:$e');
      EasyLoading.showToast(e.toString());
    }
    return null;
  }
}
