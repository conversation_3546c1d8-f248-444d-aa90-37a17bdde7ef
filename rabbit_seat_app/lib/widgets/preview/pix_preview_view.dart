import 'package:flutter/material.dart';


/**
 * 图片预览
 */
class PixPreviewView extends StatefulWidget {

  /// 资源数据
  final List sources;
  /// 初始化页码
  final int pageIndex;

  const PixPreviewView({Key? key, required this.sources, this.pageIndex = 0}) : super(key: key);

  @override
  State<PixPreviewView> createState() => _PixPreviewViewState();
}

class _PixPreviewViewState extends State<PixPreviewView> {

  // 翻页控制
  late PageController _controller;

  @override
  void initState() {
    super.initState();

    // 初始化控制器
    _controller = PageController(initialPage: widget.pageIndex);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
       title: Text("图片预览"),
      ),
      backgroundColor: Colors.black,
      body: PageView.builder(
        controller: _controller,
        itemCount: widget.sources.length,
        itemBuilder: (context, index) {
          // 数据类型
          String _url = widget.sources[index];
          // return ImageScaleWidget.network(url: _url,);
          return Container();
        },
      ),
    );
  }
}

