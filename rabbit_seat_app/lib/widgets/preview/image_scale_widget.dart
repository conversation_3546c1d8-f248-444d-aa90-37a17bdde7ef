import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

/// =============================================
/// 图片缩放部件
class ImageScaleWidget extends StatefulWidget {
  /// 显示类型 0-网络图片，1-file图片，2-资源图片
  late int type;

  /// 网络图片url
  final String? url;

  /// 文件图片
  final File? file;

  /// 资源图片
  final String? asset;

  /// 回调按钮
  // final Function()? onExpand;

  /// 边界回调
  // final Function()? toorder;

  /// 加载网络图片
  ImageScaleWidget.network({Key? key, this.url})
      : type = 0,
        file = null,
        asset = null,
        super(key: key);

  /// 加载file图片
  ImageScaleWidget.file({Key? key, this.file})
      : type = 1,
        url = null,
        asset = null,
        super(key: key);

  /// 加载资源图片
  ImageScaleWidget.asset({Key? key, this.asset})
      : type = 2,
        url = null,
        file = null,
        super(key: key);

  @override
  State<ImageScaleWidget> createState() => _ImageScaleWidgetState();
}

class _ImageScaleWidgetState extends State<ImageScaleWidget> {

  /// 偏移量
  Offset _offset = Offset.zero;

  /// 变化中心点
  Offset _middleOffset = Offset.zero;

  /// 缩放比例
  double _scale = 1.0;

  /// 最后一次缩放比例
  double _lastScale = 1.0;

  /// 是否展开
  bool _isExpand = false;

  @override
  Widget build(BuildContext context) {

    // 图片部件
    Widget _imageWidget = Container();
    if (widget.type == 0) {
      // _imageWidget = Image.network(widget.url!);
      _imageWidget = CachedNetworkImage(
        fit: BoxFit.fitWidth,
        imageUrl: widget.url!,
      );
    }
    else if (widget.type == 1) {
      _imageWidget = Image.file(widget.file!);
    }
    else if (widget.type == 2) {
      _imageWidget = Image.asset(widget.asset!);
    }

    return GestureDetector(
      // 缩放开始
      onScaleStart: _onScaleStartHandler,
      // 缩放进行中
      onScaleUpdate: _onScaleUpdateHandler,
      // 缩放结束
      onScaleEnd: _onScaleEndHandler,
      // 双击
      onDoubleTap: _onDoubleTapHandler,
      child: Container(
        color: Colors.black,
        child: SizedBox.expand(
          child: Transform(
            transform: Matrix4.identity()
              ..translate(_offset.dx, _offset.dy)
              ..scale(_scale),
            child: _imageWidget,
          ),
        ),
      ),
    );
  }

  /// 缩放开始处理
  void _onScaleStartHandler(ScaleStartDetails details) {
    _lastScale = _scale;
    _middleOffset = (details.focalPoint - _offset) / _scale;
  }

  /// 缩放更新中
  void _onScaleUpdateHandler(ScaleUpdateDetails details) {
    print(">>>");
    setState(() {
      // 限制放大倍数 1~4倍
      _scale = (_lastScale * details.scale).clamp(1.0, 4.0);
      // 更新当前位置
      _offset = _clampOffset(details.focalPoint - _middleOffset * _scale);
    });

    // 是否到左右边界
    if (context.size != null) {
      if (_offset.dx == 0) {
        // 左侧边界
        print(">>> 到左侧边界");
      }
      final _mxdx = (Offset(context.size!.width, context.size!.height) * (1.0 - _scale)).dx;
      if (_offset.dx <= _mxdx) {
        // 右侧边界
        print(">>> 到右侧边界");
      }
    }
  }

  /// 缩放开始处理
  void _onScaleEndHandler(ScaleEndDetails details) {
    if (_scale > 1.0) {
      _isExpand = true;
    } else {
      _isExpand = false;
    }
  }

  /// 保持偏移量
  Offset _clampOffset(Offset offset) {
    Size? size = context.size;
    if (size != null) {
      // widget的屏幕宽度
      final Offset minOffset = Offset(size.width, size.height) * (1.0 - _scale);
      // 限制他的最小尺寸
      return Offset(
        offset.dx.clamp(minOffset.dx, 0.0),
        offset.dy.clamp(minOffset.dy, 0.0),
      );
    }
    return Offset.zero;
  }

  /// 双机
  void _onDoubleTapHandler() {
    Size? size = context.size;
    if (size != null) {
      _isExpand = !_isExpand;
      setState(() {
        if (_isExpand) {
          // 放大一倍
          _scale = 2.0;
          _offset = Offset(-(size.width / 2), -(size.height / 2));
        } else {
          // 还原默认
          _scale = 1.0;
          _offset = Offset.zero;
        }
      });
    }
  }
}