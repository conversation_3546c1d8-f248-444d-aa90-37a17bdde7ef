import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/widgets/buttom/ripple_button.dart';
import 'package:tuya/tuya.dart';

import '../../models/tuya/tuyaHome.dart';
import '../../utils/global.dart';
import '../../utils/utils.dart';

/*
class NoDevice extends StatefulWidget {
  const NoDevice({
    Key? key,
  }) : super(key: key);

  @override
  _NoDeviceState createState() => _NoDeviceState();
}

class _NoDeviceState extends State<NoDevice> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _buildBody();
  }

  Widget _buildBody() {
    return Container(
      padding: EdgeInsets.only(top: 39.h, left: 17.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "暂无设备",
            style: TextStyle(fontSize: 25.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 500.h,
            width: 360.w,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Stack(
                  children: [
                    Center(
                      child: Image(
                          image: const AssetImage("assets/images/nodevice.png"),
                          width: 186.w),
                    ),
                    Center(
                      child: Container(
                        margin: EdgeInsets.only(top: 100.h),
                        child: Text(
                          "暂无设备请添加设备",
                          style: TextStyle(fontSize: 15.sp),
                        ),
                      ),
                    )
                  ],
                ),
                InkWell(
                  onTap: () {
                    Navigator.pushNamed(context, "/add-device");
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 18.h),
                    height: 42.h,
                    width: 210.w,
                    decoration: BoxDecoration(
                      color: const Color.fromARGB(255, 255, 119, 0),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Center(
                      child: Text(
                        "添加设备",
                        style: TextStyle(fontSize: 18.sp, color: Colors.white),
                      ),
                    ),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
*/

/// ======================================================================
/// ======================================================================

/// 暂无设备
class NoDeviceWidget extends StatelessWidget {
  /// 右侧点点点点击
  final Function()? onTap;

  /// 添加按钮点击
  final Function()? onAddTap;

  const NoDeviceWidget({Key? key, this.onTap, this.onAddTap}) : super(key: key);

  /*
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          alignment: Alignment.center,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  Image.asset(
                    'assets/images/nodevice.png',
                    width: 221,
                    fit: BoxFit.fill,
                  ),
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Text(
                      '暂无设备请添加设备',
                      textAlign: TextAlign.center,
                      style:
                      TextStyle(fontSize: 15.sp, color: Color(0xFF666666)),
                    ),
                  )
                ],
              ),
              SizedBox(
                height: 27,
              ),
              GestureDetector(
                onTap: () async {
                  Navigator.pushNamed(context, "/add-device");
                },
                child: Container(
                  width: 250,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Business.mainColor,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.add,
                        color: Color(0xFFFFFFFF),
                        size: 24,
                      ),
                      SizedBox(
                        width: 5,
                      ),
                      Text(
                        '添加设备',
                        style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFFFFFFFF)),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
   */

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Container(
            alignment: Alignment.center,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/nodevice.png',
                  width: 221,
                  fit: BoxFit.fill,
                ),
                SizedBox(
                  height: 8.h,
                ),
                Text(
                  '暂无设备请添加设备',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 15.sp, color: Color(0xFF666666)),
                )
              ],
            ),
          ),
        ),
        Container(
          padding: EdgeInsets.only(bottom: 45.h, top: 35.h),
          alignment: Alignment.center,
          child: GestureDetector(
            onTap: () {
              Business.showAddDeviceSelect(context);
            },
            child: Container(
              width: 250.w,
              height: 50,
              decoration: BoxDecoration(
                color: Business.mainColor,
                borderRadius: BorderRadius.circular(25),
              ),
              alignment: Alignment.center,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.add,
                    color: Color(0xFFFFFFFF),
                    size: 24,
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  Text(
                    '添加设备',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFFFFFFF),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
