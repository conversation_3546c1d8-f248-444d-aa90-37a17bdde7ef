import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'drop_button.dart';


class SelectDeviceMenu extends StatefulWidget {
  SelectDeviceMenu({
    Key? key,
    required this.hideSelectDevice,
  }) : super(key: key);

  bool hideSelectDevice;

  @override
  _SelectDeviceMenuState createState() => _SelectDeviceMenuState();
}

class _SelectDeviceMenuState extends State<SelectDeviceMenu> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _buildBody();
  }

  Widget _buildBody() {
    return ConstrainedBox(
        constraints: const BoxConstraints.expand(),
        child: Stack(
          children: [
            Positioned(
                left: 10.w,
                top: 80.h,
                child: Offstage(
                  offstage: widget.hideSelectDevice,
                  child: DropButton(
                    items: [
                      ButtonItem('设备1', '1'),
                      ButtonItem('设备2', '2'),
                      ButtonItem('设备3', '3')
                    ],
                    initItem: "1",
                    onSelect: (String value) {
                      setState(() {
                        widget.hideSelectDevice = true;
                      });
                    },
                  ),
                ))
          ],
        ));
  }
}
