import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// ======================================================================
/// ======================================================================

/// 设备离线重连
class RemoteTipWidget extends StatelessWidget {
  final Function()? onTap;

  const RemoteTipWidget({Key? key, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 25.w, top: 5.w, bottom: 1.w, right: 9.w),
      // height: 38.h,
      // decoration: BoxDecoration(
      //   color: Color(0x99000000),
      //   borderRadius: BorderRadius.circular(38.h / 2),
      // ),
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 3.w, bottom: 1.w,right: 9.w),
            child: SizedBox(
              width: 20.w,
              height: 20.w,
              child: Image.asset("assets/images/home/<USER>"),
            ),
          ),
          Expanded(
            child: Text(
              '远程模式下，指令下达可能会有几分钟的延迟',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: Color.fromARGB(140, 126, 125, 125),
              ),
            ),
          ),
          SizedBox(
            width: 10.w,
          ),
        ],
      ),
    );
  }
}
