
import 'package:flutter/material.dart';
import 'package:rabbit_seat_app/business.dart';

/// ======================================================================
/// ======================================================================

/// 菜单选项
class HMenuItemWidget extends StatelessWidget {
  String text;
  bool isSelected;
  bool isAdd;
  Function()? onTap;

  HMenuItemWidget(
      {Key? key,
        required this.text,
        this.isSelected = false,
        this.isAdd = false,
        this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget _icon = Container();
    Color _bgColor = Colors.white;
    Color _textColor = Color(0xFF000000);

    if (isAdd == true) {
      _icon = SizedBox(
        width: 20,
        height: 20,
        child: Image.asset("assets/images/home/<USER>", fit: BoxFit.fill),
      );
      _bgColor = Color(0xFFF9F9F9);
    } else if (isSelected == true) {
      _icon = SizedBox(
        width: 20,
        height: 20,
        child: Image.asset("assets/images/home/<USER>", fit: BoxFit.fill),
      );
      _bgColor = Business.mainColor.withOpacity(0.08);
      _textColor = Business.mainColor;
    }

    return Column(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.only(left: 15, right: 10),
            height: 50,
            decoration: BoxDecoration(
              color: _bgColor,
              borderRadius: BorderRadius.all(Radius.circular(6)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    text,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 15,
                      color: _textColor,
                    ),
                  ),
                ),
                _icon,
              ],
            ),
          ),
        ),
        Divider(
          height: 1,
          color: Color(0xFFeeeeee),
        ),
      ],
    );
  }
}