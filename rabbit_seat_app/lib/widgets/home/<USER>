import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'botton_control.dart';

/// ======================================================================
/// ======================================================================

/**
 * 右侧控制按钮
 */
class RightControlButton extends StatelessWidget {

  final ActionButtonStatus status;
  final String text;
  final String icon;
  final String activeIcon;
  final Function()? onTap;

  const RightControlButton({
    Key? key,
    this.status = ActionButtonStatus.disable,
    required this.text,
    required this.icon,
    required this.activeIcon,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color bgColor = const Color(0xFFEAEAEA);
    Color _textColor = const Color(0xFF939393);
    List<BoxShadow>? boxShadow;
    Widget imageWidget = Image.asset(icon);
    switch (status) {
      case ActionButtonStatus.disable:
        break;
      case ActionButtonStatus.on:
        {
          bgColor = const Color(0xFFFFFFFF);
          boxShadow = const [
            BoxShadow(
              color: Color(0x14000000), // 阴影的颜色
              offset: Offset(0, 0), // 阴影与容器的距离
              blurRadius: 20.0, // 高斯的标准偏差与盒子的形状卷积。
              spreadRadius: 5.0, // 在应用模糊之前，框应该膨胀的量。
            )
          ];
          imageWidget = Image.asset(activeIcon);
          _textColor = const Color(0xFFEF7C30);
        }
        break;
      case ActionButtonStatus.off:
        break;
      case ActionButtonStatus.doing:
        {
          bgColor = const Color(0xF0E1E1E1);
          imageWidget = LoadingAnimationWidget.fourRotatingDots(
            color: const Color(0xFF1A1A3F),
            size: 20.h,
          );
        }
        break;
      default:
        break;
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 74.w,
        height: 74.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.circular(37.w),
            boxShadow: boxShadow,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 3.w, bottom: 6.w),
              child: SizedBox(
                width: 25.w,
                height: 25.w,
                child: imageWidget,
              ),
            ),
            Text(
              text,
              style: TextStyle(
                  fontSize: 11.sp,
                  color: _textColor,
            ),
            )
          ],
        ),
      ),
    );
  }
}
