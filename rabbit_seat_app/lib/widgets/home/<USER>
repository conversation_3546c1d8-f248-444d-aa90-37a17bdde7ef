import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// ======================================================================
/// ======================================================================

/// 设备离线重连
class RebindingWidget extends StatelessWidget {
  final Function()? onTap;

  const RebindingWidget({Key? key, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 18.w, top: 5.w, bottom: 5.w, right: 9.w),
      height: 38.h,
      decoration: BoxDecoration(
        color: Color(0x99000000),
        borderRadius: BorderRadius.circular(38.h / 2),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              '设备已离线 请检查电源和网络',
              style: TextStyle(
                fontSize: 13.sp,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(
            width: 10.w,
          ),
        ],
      ),
    );
  }
}
