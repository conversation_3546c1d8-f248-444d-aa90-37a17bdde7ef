import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/home/<USER>';
import 'package:rabbit_seat_app/services/device_service.dart';

import '../../models/tuya/remoteCommandRequest.dart';
import '../../models/tuya/tuyaCommand.dart';
import '../../utils/global.dart';
import 'device_status.dart';


class Device extends StatefulWidget {
  final HomeDeviceModel? data;
  const Device({
    Key? key,
    this.data,
  }) : super(key: key);

  @override
  _DeviceState createState() => _DeviceState();
}

class _DeviceState extends State<Device> {
  @override
  void initState() {
    super.initState();

    if (widget.data != null && widget.data!.status != null) {
    // hotSw:'手动加热开关',
    // autoHotTempTH:'手动加热开关',
    // autoFanTempTH:'自动风扇设定温度上限',
    // autoHotTempTL:'自动加热设定温度下限',
    // fanSw:'手动通风开关',
    // IMEI:'IMEI',
    // mcuLpTimer:'主动低功耗计时器',
    // autoFanTempTL:'自动风扇设定温度下限',
    // recTemp:'座椅温度',
    // batPercent:'电量百分比',
    // batCharge:'电池充电',
    // rssi:'信号强度',
    // protectionLeftSw:'侧保护开关(左)',
    // protectionRightSw:'侧保护开关(右)',
    // autoMode:'自动模式',
    // voiceModuleVersion:'语音模组版本号',

      widget.data!.status!.forEach((element) {
        if (element.code == 'autoMode') {}
        if (element.code == 'fanSw') {
          fanSw = element.value as bool;
        }
        if (element.code == 'hotSw') {
          hotSw = element.value as bool;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildBody();
  }

  /// 手动风扇
  bool fanSw = false;

  /// 手动加热
  bool hotSw = false;

  Widget _buildBody() {
    return Stack(
      children: [
        Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            margin: EdgeInsets.only(bottom: 35.h),
            padding: EdgeInsets.only(left: 15.w, right: 15.w),
            width: 300.w,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                DeviceStatus(
                    image: "assets/images/device/8.png",
                    activeImage: "assets/images/device/3.png",
                    left: 0,
                    active: fanSw,
                    text: "通风",
                    onTap: () {
                      // var command = TuyaCommand();
                      // command.code = "fanSw";
                      // command.value = !fanSw;
                      // var list = [];
                      // list.add(command);
                      final _deviceId = Global.profile.currentDeviceId;
                      // var jsonStr = json.encode(list);
                      // final _commandsJson = base64Encode(utf8.encode(jsonStr));
                      DeviceService.sendCommand(deviceId: _deviceId, code: 'fanSw', value: !fanSw);
                      setState(() {
                        fanSw = !fanSw;
                      });
                    }),
                DeviceStatus(
                    active: hotSw,
                    image: "assets/images/device/9.png",
                    activeImage: "assets/images/device/4.png",
                    text: "加热",
                    onTap: () {
                      // var command = TuyaCommand();
                      // command.code = "hotSw";
                      // command.value = !hotSw;
                      // var list = [];
                      // list.add(command);
                      final _deviceId = Global.profile.currentDeviceId;
                      // var jsonStr = json.encode(list);
                      // final _commandsJson = base64Encode(utf8.encode(jsonStr));
                      DeviceService.sendCommand(deviceId: _deviceId, code: 'hotSw', value: !hotSw);
                      setState(() {
                        hotSw = !hotSw;
                      });
                    }),
                DeviceStatus(
                    active: true,
                    isCircle: false,
                    image: "assets/images/device/10.png",
                    activeImage: "assets/images/device/5.png",
                    text: "自动模式",
                    onTap: () {})
              ],
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(60.w),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2), // 阴影的颜色
                  offset: Offset(2.w, 2.h), // 阴影与容器的距离
                  blurRadius: 60.0, // 高斯的标准偏差与盒子的形状卷积。
                  spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
