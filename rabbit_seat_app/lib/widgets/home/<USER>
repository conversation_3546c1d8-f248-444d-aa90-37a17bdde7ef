import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/provider/device_provider.dart';

import '../../models/device/device_model.dart';
import '../../models/home/<USER>';
import '../../services/device_service.dart';

/// ======================================================================
/// ======================================================================

// /// 底部按钮
// class HBottomWidget extends StatefulWidget {
//   /// 数据源
//   DeviceModel? data;
//
//   HBottomWidget({Key? key, this.data}) : super(key: key);
//
//   @override
//   State<HBottomWidget> createState() => _HBottomWidgetState();
// }
//
// class _HBottomWidgetState extends State<HBottomWidget> {
//   // 通风
//   bool _isWind = false;
//
//   // 加热
//   bool _isHost = false;
//
//   // 自动
//   bool _isAuto = false;
//
//   // 离线
//   bool _online = false;
//
//   bool isInit = false;
//
//   @override
//   void initState() {
//     super.initState();
//
//     if (widget.data != null) {
//       _online = widget.data!.isOnline;
//       init();
//     }
//   }
//
//   void init() {
//     if (widget.data!.statusList != null) {
//       for (var element in widget.data!.statusList!) {
//         if (element.code == 'autoMode') {
//           _isAuto = element.value as bool;
//         }
//         if (element.code == 'fanSw') {
//           _isWind = element.value as bool;
//         }
//         if (element.code == 'hotSw') {
//           _isHost = element.value as bool;
//         }
//       }
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     if (!isInit) {
//       isInit = true;
//       context.read<DeviceProvider>().addListener(() {
//         init();
//       });
//     }
//
//     _online = widget.data!.isOnline;
//
//     return Container(
//       height: 84.h,
//       padding: const EdgeInsets.only(left: 15, right: 15, top: 12, bottom: 12),
//       decoration: BoxDecoration(
//           color: const Color(0xFFFFFFFF),
//           borderRadius: BorderRadius.circular(42.h),
//           boxShadow: const [
//             BoxShadow(
//               color: Color(0x14000000), // 阴影的颜色
//               offset: Offset(0, 0), // 阴影与容器的距离
//               blurRadius: 30.0, // 高斯的标准偏差与盒子的形状卷积。
//               spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
//             )
//           ]),
//       child: Row(
//         children: [
//           Expanded(
//               child: _itemWidget(
//             selected: _isWind,
//             text: '通风',
//             icon: "assets/images/device/8.png",
//             activeIcon: "assets/images/device/3.png",
//             onTap: () async {
//               _ventilateAction(!_isWind);
//             },
//           )),
//           SizedBox(
//             width: 15.w,
//           ),
//           Expanded(
//               child: _itemWidget(
//             selected: _isHost,
//             text: "加热",
//             icon: "assets/images/device/9.png",
//             activeIcon: "assets/images/device/4.png",
//             onTap: () async {
//               _warmAction(!_isHost);
//             },
//           )),
//           SizedBox(
//             width: 15.w,
//           ),
//           Expanded(
//               child: _itemWidget(
//             selected: _isAuto,
//             text: "自动模式",
//             icon: "assets/images/device/10.png",
//             activeIcon: "assets/images/device/5.png",
//             onTap: () async {
//               _autoAction(!_isAuto);
//             },
//           )),
//         ],
//       ),
//     );
//   }
//
//   Widget _itemWidget({
//     required ActionButtonStatus status,
//     required String text,
//     required String icon,
//     required String activeIcon,
//     Function()? onTap,
//   }) {
//     Color _textColor = const Color(0xFF333333);
//     Color _bgColor = const Color(0xFFFFFFFF);
//     List<BoxShadow>? _boxShadow;
//     String _asset = icon;
//
//     switch (status) {
//       case ActionButtonStatus.off:
//         break;
//       case ActionButtonStatus.doing:
//         {
//           _bgColor = const Color(0xF0C5C5C5);
//         }
//         break;
//       case ActionButtonStatus.on:
//         {
//           _textColor = const Color(0xFFFFFFFF);
//           _bgColor = const Business.mainColor;
//           _boxShadow = const [
//             BoxShadow(
//               color: Color(0x8AFF7500), // 阴影的颜色
//               offset: Offset(0, 7), // 阴影与容器的距离
//               blurRadius: 10.0, // 高斯的标准偏差与盒子的形状卷积。
//               spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
//             )
//           ];
//           _asset = activeIcon;
//         }
//         break;
//       default:
//         break;
//     }
//
//     return GestureDetector(
//       onTap: onTap,
//       child: Container(
//         padding: EdgeInsets.only(top: 3.h),
//         height: 60.h,
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(60.h / 2),
//           color: _bgColor,
//           boxShadow: _boxShadow,
//         ),
//         alignment: Alignment.center,
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             SizedBox(
//               width: 26.h,
//               height: 26.h,
//               child: Image.asset(_asset),
//             ),
//             SizedBox(
//               height: 5.h,
//             ),
//             Text(
//               text,
//               style: TextStyle(fontSize: 11.sp, color: _textColor),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   /// 通风事件
//   void _ventilateAction(bool state) async {
//     if (_online == true) {
//       final _deviceId = widget.data!.id;
//       var res = await DeviceService.sendCommand(
//           deviceId: _deviceId, code: 'fanSw', value: state);
//       if (res == true) {
//         setState(() {
//           _isWind = state;
//           if (_isWind == true) {
//             _warmAction(false);
//             _autoAction(false);
//           }
//         });
//       }
//     }
//   }
//
//   /// 加热事件
//   void _warmAction(bool state) async {
//     if (_online == true) {
//       final _deviceId = widget.data!.id;
//       var res = await DeviceService.sendCommand(
//           deviceId: _deviceId, code: 'hotSw', value: state);
//       if (res) {
//         setState(() {
//           _isHost = state;
//           if (_isHost == true) {
//             _ventilateAction(false);
//             _autoAction(false);
//           }
//         });
//       }
//     }
//   }
//
//   /// 自动模式事件
//   void _autoAction(bool state) async {
//     if (_online == true) {
//       final _deviceId = widget.data!.id;
//       var res = await DeviceService.sendCommand(
//           deviceId: _deviceId, code: 'autoMode', value: state);
//       if (res == true) {
//         setState(() {
//           _isAuto = state;
//           if (_isAuto == true) {
//             _ventilateAction(false);
//             _warmAction(false);
//           }
//         });
//       }
//     }
//   }
// }

/**
 *
 */
class BottomControlButton extends StatelessWidget {
  ActionButtonStatus status;
  String text;
  String icon;
  String activeIcon;
  Function()? onTap;

  BottomControlButton(
      {Key? key,
      this.status = ActionButtonStatus.disable,
      required this.text,
      required this.icon,
      required this.activeIcon,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color _textColor = const Color(0xFF333333);
    Color _bgColor = const Color(0xFFFFFFFF);
    List<BoxShadow>? _boxShadow = null;
    Widget imageWidget = Image.asset(icon);
    switch (status) {
      case ActionButtonStatus.disable:
        break;
      case ActionButtonStatus.on:
        {
          _textColor = const Color(0xFFFFFFFF);
          _bgColor = Business.mainColor;
          _boxShadow = [
            const BoxShadow(
              color: Color(0x8AFF7500), // 阴影的颜色
              offset: Offset(0, 7), // 阴影与容器的距离
              blurRadius: 10.0, // 高斯的标准偏差与盒子的形状卷积。
              spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
            )
          ];
          imageWidget = Image.asset(activeIcon);
        }
        break;
      case ActionButtonStatus.off:
        break;
      case ActionButtonStatus.doing:
        {
          _bgColor = const Color(0xF0E1E1E1);
          imageWidget = LoadingAnimationWidget.fourRotatingDots(
            color: const Color(0xFF1A1A3F),
            size: 20.h,
          );
        }
        break;
      default:
        break;
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.only(top: 5, bottom: 5),
        // height: 60.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(60.h/2),
          color: _bgColor,
          boxShadow: _boxShadow,
        ),
        alignment: Alignment.center,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 26.h,
              height: 26.h,
              child: imageWidget, // Image.asset(_asset),
            ),
            SizedBox(
              height: 10,
            ),
            Text(
              text,
              style: TextStyle(fontSize: 12.sp, color: _textColor),
            ),
          ],
        ),
      ),
    );
  }
}

enum ActionButtonStatus { off, doing, on, disable }
