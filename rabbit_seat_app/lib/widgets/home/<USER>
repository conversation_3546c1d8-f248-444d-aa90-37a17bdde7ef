import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../shape/triangle_painter.dart';

class ButtonItem {
  ButtonItem(this.label, this.value);

  String label;
  String value;
}

typedef SelectCallback<String> = void Function(String value);

class DropButton extends StatefulWidget {
  const DropButton(
      {Key? key, this.onSelect, required this.items, required this.initItem})
      : super(key: key);

  final List<ButtonItem> items;
  final String initItem;
  final SelectCallback<String>? onSelect;

  @override
  _DropButtonState createState() => _DropButtonState();
}

class _DropButtonState extends State<DropButton> {
  @override
  void initState() {
    super.initState();
    selectItem = widget.initItem;
  }

  String selectItem = "";

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(left: 20.w),
          child: CustomPaint(
            painter: <PERSON><PERSON>ain<PERSON>(
              strokeColor: Colors.white,
              paintingStyle: PaintingStyle.fill,
            ),
            child: Container(
              height: 5.h,
              width: 15.w,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.w),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15), // 阴影的颜色
                offset: Offset(2.w, 2.h), // 阴影与容器的距离
                blurRadius: 10.0, // 高斯的标准偏差与盒子的形状卷积。
                spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
              ),
            ],
          ),
          padding: EdgeInsets.only(top: 10.h, bottom: 10.h),
          child: Column(
            children: widget.items.map((ButtonItem m) {
              return InkWell(
                onTap: () {
                  setState(() {
                    selectItem = m.value;
                  });
                  if (widget.onSelect != null) {
                    widget.onSelect!(m.value);
                  }
                },
                highlightColor: const Color.fromARGB(20, 255, 119, 0),
                child: Container(
                    height: 42.h,
                    constraints: BoxConstraints(minWidth: 100.w),
                    padding: EdgeInsets.only(left: 12.5.w, right: 12.5.w),
                    color: m.value == selectItem
                        ? const Color.fromARGB(20, 255, 119, 0)
                        : Colors.white,
                    child: Center(
                        child: Row(
                      children: [
                        Text(
                          m.label,
                          style: TextStyle(
                              color: m.value == selectItem
                                  ? const Color.fromARGB(255, 255, 119, 0)
                                  : Colors.black),
                        ),
                        m.value == selectItem
                            ? Container(
                                width: 20.w,
                                padding: EdgeInsets.only(left: 12.w),
                                child: Icon(
                                  Icons.done,
                                  color: const Color.fromARGB(255, 255, 119, 0),
                                  size: 15.sp,
                                ),
                              )
                            : Container(
                                width: 20.w,
                              ),
                      ],
                    ))),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
