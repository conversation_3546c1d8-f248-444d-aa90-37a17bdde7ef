import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';

import '../../utils/utils.dart';

class DeviceStatus extends StatefulWidget {
  const DeviceStatus({
    Key? key,
    required this.onTap,
    required this.active,
    required this.image,
    required this.activeImage,
    required this.text,
    this.isCircle = false,
    this.left = 12,
  }) : super(key: key);

  final void Function() onTap;
  final bool active;
  final num left;
  final String image;
  final String activeImage;
  final String text;
  final bool isCircle;

  @override
  _DeviceStatusState createState() => _DeviceStatusState();
}

class _DeviceStatusState extends State<DeviceStatus> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        widget.onTap();
      },
      child: Container(
        width: widget.isCircle ? 61.w : 75.w,
        height: widget.isCircle ? 61.w : 55.h,
        margin: EdgeInsets.only(top: 10.h, bottom: 10.h),
        decoration: BoxDecoration(
          color: widget.active ? Business.mainColor : Colors.white,
          borderRadius: widget.isCircle ? null : BorderRadius.circular(30.w),
          shape: widget.isCircle ? BoxShape.circle : BoxShape.rectangle,
          boxShadow: [
            BoxShadow(
              color: widget.active
                  ? Business.mainColor.withOpacity(0.6)
                  : Colors.transparent, // 阴影的颜色
              offset: Offset(3.w, 3.h), // 阴影与容器的距离
              blurRadius: 10.0, // 高斯的标准偏差与盒子的形状卷积。
              spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image(
                image: AssetImage(
                    widget.active ? widget.activeImage : widget.image),
                height: 20.w),
            Container(
              margin: EdgeInsets.only(top: 5.h),
              child: Util.getText(widget.text,
                  fontSize: 9.5,
                  color: widget.active ? Colors.white : Colors.black),
            )
          ],
        ),
      ),
    );
  }
}
