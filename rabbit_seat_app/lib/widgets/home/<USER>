import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/home/<USER>';
import 'package:rabbit_seat_app/models/tuya/tuyaCommand.dart';
import 'package:rabbit_seat_app/services/device_service.dart';

import '../../utils/global.dart';
import 'device_status.dart';

class RightDeviceStatus extends StatefulWidget {
  final HomeDeviceModel? data;
  const RightDeviceStatus({
    Key? key,
    this.data,
  }) : super(key: key);

  @override
  _RightDeviceStatusState createState() => _RightDeviceStatusState();
}

class _RightDeviceStatusState extends State<RightDeviceStatus> {
  @override
  void initState() {
    super.initState();

    if (widget.data != null && widget.data!.status != null) {
      // hotSw:'手动加热开关',
      // autoHotTempTH:'手动加热开关',
      // autoFanTempTH:'自动风扇设定温度上限',
      // autoHotTempTL:'自动加热设定温度下限',
      // fanSw:'手动通风开关',
      // IMEI:'IMEI',
      // mcuLpTimer:'主动低功耗计时器',
      // autoFanTempTL:'自动风扇设定温度下限',
      // recTemp:'座椅温度',
      // batPercent:'电量百分比',
      // batCharge:'电池充电',
      // rssi:'信号强度',
      // protectionLeftSw:'侧保护开关(左)',
      // protectionRightSw:'侧保护开关(右)',
      // autoMode:'自动模式',
      // voiceModuleVersion:'语音模组版本号',

      widget.data!.status!.forEach((element) {
        if (element.code == 'protectionLeftSw') {
          protectionLeftSw = element.value as bool;
        }
        if (element.code == 'protectionRightSw') {
          protectionRightSw = element.value as bool;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildBody();
  }

  /// 左侧保护
  bool protectionLeftSw = false;

  /// 右侧保护
  bool protectionRightSw = false;
  Widget _buildBody() {
    return Align(
      alignment: Alignment.topRight,
      child: Container(
        margin: EdgeInsets.only(top: 190.h, right: 28.w),
        child: Column(
          children: [
            DeviceStatus(
                active: protectionLeftSw,
                image: "assets/images/device/7.png",
                activeImage: "assets/images/device/2.png",
                text: "左保护",
                isCircle: true,
                onTap: () {
                  // var command = TuyaCommand();
                  // command.code = "protectionLeftSw";
                  // command.value = !protectionLeftSw;
                  // var list = [];
                  // list.add(command);

                  final _deviceId = Global.profile.currentDeviceId;
                  // var jsonStr = json.encode(list);
                  // final _commandsJson = base64Encode(utf8.encode(jsonStr));
                  DeviceService.sendCommand(deviceId: _deviceId, code: 'protectionLeftSw', value: !protectionLeftSw);

                  setState(() {
                    protectionLeftSw = !protectionLeftSw;
                  });
                }),
            DeviceStatus(
                active: protectionRightSw,
                image: "assets/images/device/12.png",
                activeImage: "assets/images/device/1.png",
                text: "右保护",
                isCircle: true,
                onTap: () {
                  // var command = TuyaCommand();
                  // command.code = "protectionRightSw";
                  // command.value = !protectionRightSw;
                  // var list = [];
                  // list.add(command);

                  final _deviceId = Global.profile.currentDeviceId;
                  // var jsonStr = json.encode(list);
                  // final _commandsJson = base64Encode(utf8.encode(jsonStr));
                  DeviceService.sendCommand(deviceId: _deviceId, code: 'protectionRightSw', value: !protectionRightSw);

                  setState(() {
                    protectionRightSw = !protectionRightSw;
                  });
                }),
            DeviceStatus(
                active: false,
                image: "assets/images/device/11.png",
                activeImage: "assets/images/device/6.png",
                text: "氛围灯",
                isCircle: true,
                onTap: () {
                  // EasyLoading.showToast('暂不可用！');
                })
          ],
        ),
      ),
    );
  }
}
