import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/home/<USER>';

import '../../utils/utils.dart';
import '../shape/triangle_painter.dart';

class Header extends StatefulWidget {
  final HomeDeviceModel? data;
  Header({
    Key? key,
    this.data,
    required this.hideSelectDevice,
    required this.homeName,
    required this.onSelectDevice,
  }) : super(key: key);

  bool hideSelectDevice;
  String homeName;
  final Function() onSelectDevice;

  @override
  State<StatefulWidget> createState() => _HeaderState();
}

class _HeaderState extends State<Header> {
  /// 设备在线情况
  bool _online = false;

  /// 电量 '电量百分比',
  String _energy = '0';

  /// 温度:'座椅温度',
  String _temp = '0';

  bool isShare = false;

  @override
  void initState() {
    super.initState();
    if (widget.data != null) {
      _online = widget.data!.online;
      isShare = widget.data!.isShare;

      if (widget.data!.status != null) {
        // hotSw:'手动加热开关',
        // autoHotTempTH:'手动加热开关',
        // autoFanTempTH:'自动风扇设定温度上限',
        // autoHotTempTL:'自动加热设定温度下限',
        // fanSw:'手动通风开关',
        // IMEI:'IMEI',
        // mcuLpTimer:'主动低功耗计时器',
        // autoFanTempTL:'自动风扇设定温度下限',
        // recTemp:'座椅温度',
        // batPercent:'电量百分比',
        // batCharge:'电池充电',
        // rssi:'信号强度',
        // protectionLeftSw:'侧保护开关(左)',
        // protectionRightSw:'侧保护开关(右)',
        // autoMode:'自动模式',
        // voiceModuleVersion:'语音模组版本号',

        widget.data!.status!.forEach((element) {
          if (element.code == 'recTemp') {
            _temp = element.value.toString();
          }
          if (element.code == 'batPercent') {
            _energy = element.value.toString();
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: 360.w,
        child: Stack(
          alignment: Alignment.topLeft,
          children: [
            Container(
                padding: EdgeInsets.only(left: 18.w, right: 18.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            InkWell(
                                onTap: () {
                                  widget.onSelectDevice();
                                },
                                child: Util.getText(widget.homeName,
                                    fontSize: 18, fontWeight: FontWeight.bold)),
                            Container(
                              padding: EdgeInsets.only(left: 8.w),
                              child: DecoratedBox(
                                decoration: const BoxDecoration(
                                    color: Colors.transparent),
                                child: Transform.rotate(
                                  angle: pi,
                                  child: CustomPaint(
                                    painter: TrianglePainter(
                                      strokeColor: Colors.black,
                                      paintingStyle: PaintingStyle.fill,
                                    ),
                                    child: SizedBox(
                                      height: 6.h,
                                      width: 10.w,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        Container(
                          padding: EdgeInsets.only(top: 5.h),
                          child: Row(
                            children: [
                              Container(
                                width: 7.w,
                                height: 7.w,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: _online == true
                                      ? Color(0xFF64C23C)
                                      : Color(0xFFED0000),
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.only(left: 5.w),
                                child: _online == true
                                    ? Util.getText("在线丨电量:$_energy%",
                                        fontSize: 10,
                                        color: const Color(0xFF515151))
                                    : Util.getText('离线',
                                        color: Color(0xFFED0000)),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                    Row(
                      children: [
                        Container(
                          width: 93.w,
                          height: 25.h,
                          // padding: EdgeInsets.only(left: 10.w),
                          alignment: Alignment.center,
                          child: _online == true
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Util.getText("座椅温度:"),
                                    Util.getText("${_temp}°C",
                                        color: const Color(0xFF64C13C))
                                  ],
                                )
                              : Center(
                                  child: Util.getText(
                                  '设备已离线',
                                  color: Color(0xFF4B4B4B),
                                )),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(30.w),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.3), // 阴影的颜色
                                offset: Offset(10.w, 20.w), // 阴影与容器的距离
                                blurRadius: 60.0, // 高斯的标准偏差与盒子的形状卷积。
                                spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.only(left: 10.w),
                          child: const Icon(
                            Icons.more_vert,
                          ),
                        )
                      ],
                    )
                  ],
                ))
          ],
        ));
  }
}
