import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SelectSexStatus extends StatefulWidget {
  SelectSexStatus(
      {Key? key,
      this.active = false,
      required this.activeImage,
      required this.deActiveImage,
      required this.activeColor,
      required this.text})
      : super(key: key);

  bool active;
  final String activeImage;
  final String deActiveImage;
  final String text;
  final Color activeColor;

  @override
  _SelectSexStatusState createState() => _SelectSexStatusState();
}

class _SelectSexStatusState extends State<SelectSexStatus> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _buildBody();
  }

  Widget _buildBody() {
    return Container(
      child: Column(
        children: [
          Image(
              image: AssetImage(
                  widget.active ? widget.activeImage : widget.deActiveImage),
              width: 82.w),
          Container(
            padding: EdgeInsets.only(top: 10.h),
            child: Text(
              widget.text,
              style: TextStyle(
                  color: widget.active
                      ? widget.activeColor
                      : const Color(0xFF868A8E)),
            ),
          )
        ],
      ),
    );
  }
}
