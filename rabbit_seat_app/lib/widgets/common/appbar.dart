import 'package:flutter/material.dart';


/**
 * 统一样式导航栏
 */
AppBar DefAppBar(
    {required BuildContext context,
      String? title,
      bool hasBack = true,
      Widget? leading,
      List<Widget>? actions}) {
  Widget? _leading = leading;
  if (_leading == null && hasBack == true) {
    _leading = IconButton(
      onPressed: () {
        Navigator.pop(context);
      },
      icon: Icon(
        Icons.arrow_back_ios,
        color: Color(0xFF000000),
        size: 14,
      ),
    );
  }
  return AppBar(
    title: Text(
      title ?? "",
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Color(0xFF000000),
      ),
    ),
    centerTitle: true,
    leading: _leading,
    actions: actions,
    backgroundColor: Colors.white,
    elevation: 1,
  );
}