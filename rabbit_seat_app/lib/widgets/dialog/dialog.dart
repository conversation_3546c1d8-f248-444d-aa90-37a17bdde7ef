import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:rabbit_seat_app/business.dart';

import '../buttom/ripple_button.dart';

/// 自定义Dialog
class DefDialog {
  /**
   * 显示提示框
   *
   * 样式：提示，文本，取消，确认
   */
  static showDialog1(
      {required BuildContext context,
      String? title,
      String? message,
      String? confirmText,
      String? cancelText,
      Function()? confirm,
      Function()? cancel}) {
    // 显示自定义 Dialog
    showDialog(
      barrierColor: Color(0x4D000000),
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Material(
          color: Colors.transparent,
          child: Center(
            child: Container(
              width: 345,
              padding:
                  EdgeInsets.only(left: 30, right: 30, top: 28, bottom: 24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(
                  Radius.circular(18),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题
                  Text(
                    title ?? "提示",
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF000000),
                    ),
                  ),
                  // 内容
                  Padding(
                    padding: EdgeInsets.only(top: 30, bottom: 40),
                    child: Text(
                      message ?? "",
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF666666),
                      ),
                    ),
                  ),
                  // 按钮
                  Container(
                    height: 50,
                    child: Row(
                      children: [
                        Expanded(
                            child: GestureDetector(
                          onTap: () {
                            Navigator.pop(context, false);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: Color(0xFFF9F9F9),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(25)),
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              cancelText ?? "取消",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF000000),
                              ),
                            ),
                          ),
                        )),
                        SizedBox(
                          width: 15,
                        ),
                        Expanded(
                            child: GestureDetector(
                          onTap: () {
                            Navigator.pop(context, true);
                          },
                          child: Container(
                            height: 50,
                            decoration: BoxDecoration(
                              color: Business.mainColor,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(25)),
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              confirmText ?? "确定",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        )),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    ).then((value) {
      print(">>>> value:$value");
      if (value != null) {
        if (value == true) {
          if (confirm != null) confirm();
        } else {
          if (cancel != null) cancel();
        }
      }
    });
  }

  static showDialog2({
    required BuildContext context,
    String? title,
    String? message,
    String? itemText,
    Function()? confirm,
  }) {
    // 显示自定义 Dialog
    showDialog(
      barrierColor: Color(0x4D000000),
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Material(
          color: Colors.transparent,
          child: Center(
            child: Container(
              width: 345,
              padding:
                  EdgeInsets.only(left: 30, right: 30, top: 28, bottom: 24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(
                  Radius.circular(18),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题
                  Text(
                    title ?? "提示",
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF000000),
                    ),
                  ),
                  // 内容
                  Padding(
                    padding: EdgeInsets.only(top: 30, bottom: 40),
                    child: Text(
                      message ?? "",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF666666),
                      ),
                    ),
                  ),
                  // 按钮
                  Container(
                    height: 50,
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context, true);
                      },
                      child: Container(
                        height: 50,
                        decoration: BoxDecoration(
                          color: Business.mainColor,
                          borderRadius: BorderRadius.all(Radius.circular(25)),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          itemText ?? "确定",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    ).then((value) {
      print(">>>> value:$value");
      if (confirm != null) confirm();
    });
  }

  /**
   * 宝宝遗忘，告警弹框
   */
  static showDialog3({
    required BuildContext context,
    String? title,
    String? message,
    String? itemText,
    Function()? confirm,
  }) {
    // 计时器
    Timer? _timer;
    int _seconds = 3;

    // 显示自定义 Dialog
    showDialog(
      barrierColor: Color(0x4D000000),
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          Widget _widget = Material(
            color: Colors.transparent,
            child: Center(
              child: Container(
                width: 345,
                padding:
                    EdgeInsets.only(left: 30, right: 30, top: 28, bottom: 24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(
                    Radius.circular(18),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题
                    Text(
                      title ?? "提示",
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF000000),
                      ),
                    ),
                    // 内容
                    Padding(
                      padding: EdgeInsets.only(top: 30, bottom: 40),
                      child: Text(
                        message ?? "",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF666666),
                        ),
                      ),
                    ),
                    // 按钮
                    Container(
                      height: 50,
                      child: GestureDetector(
                        onTap: () {
                          if (_seconds <= 0) Navigator.pop(context, true);
                        },
                        child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            color: _seconds == 0
                                ? Business.mainColor
                                : Color(0xFFFFBA7F),
                            borderRadius: BorderRadius.all(Radius.circular(25)),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            _seconds == 0 ? '知道了' : "$_seconds秒知道了",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
          // 1秒后到显示到极
          if (_timer == null && _seconds == 3) {
            _timer = Timer.periodic(Duration(milliseconds: 1500), (timer) {
              setState(() {
                _seconds--;
                if (_seconds <= 0) {
                  _timer!.cancel();
                  _timer = null;
                }
              });
            });
          }
          return _widget;
        });
      },
    ).then((value) {
      if (_timer != null) {
        _timer!.cancel();
        _timer = null;
      }
      print(">>>> value:$value");
      if (confirm != null) confirm();
    });
  }

  /**
   * 显示图片获取途径弹出
   *
   * type: 1-拍摄、2-相册
   */
  /*
  static showPixBottomSheet(BuildContext context, Function(int) onTap) {
    // 弹出选择框
    showModalBottomSheet(
      barrierColor: Colors.black.withOpacity(0.3),
      backgroundColor: Colors.transparent,
      context: context,
      builder: (context) {
        return Material(
          color: Colors.transparent,
          child: Container(
            padding:
                EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(18)),
            ),
            child: Container(
              padding: EdgeInsets.only(left: 15, right: 15),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context, 1);
                    },
                    child: Container(
                      height: 55,
                      alignment: Alignment.center,
                      child: Text(
                        "拍照",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF000000),
                        ),
                      ),
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: Color(0xFFF5F5F5),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context, 2);
                    },
                    child: Container(
                      height: 55,
                      alignment: Alignment.center,
                      child: Text(
                        "从手机相册选择图片",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF000000),
                        ),
                      ),
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: Color(0xFFF5F5F5),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context, 3);
                    },
                    child: Container(
                      height: 55,
                      alignment: Alignment.center,
                      child: Text(
                        "视频拍摄",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF000000),
                        ),
                      ),
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: Color(0xFFF5F5F5),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context, 4);
                    },
                    child: Container(
                      height: 55,
                      alignment: Alignment.center,
                      child: Text(
                        "从手机相册选择视频",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF000000),
                        ),
                      ),
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: Color(0xFFF5F5F5),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context, 0);
                    },
                    child: Container(
                      height: 55,
                      alignment: Alignment.center,
                      child: Text(
                        "取消",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF000000),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    ).then((value) {
      if (value != null && value is int) {
        onTap(value);
      }
    });
  }
   */

  /**
   * 显示图片获取途径弹出
   *
   * type: 1-拍摄、2-相册图片、3-录像、4-相册视频
   */
  static showPixBottomSheet(
      BuildContext context, Function(int) onTap, bool showVideo) {
    var all = [
      _pixSheetWidget("图片拍摄", () {
        Navigator.pop(context, 1);
      }),
      _pixSheetWidget("视频拍摄", () {
        Navigator.pop(context, 3);
      }),
      _pixSheetWidget("从相册获取图片", () {
        Navigator.pop(context, 2);
      }),
      _pixSheetWidget("从相册获取视频", () {
        Navigator.pop(context, 4);
      }),
      _pixSheetWidget("取消", () {
        Navigator.pop(context, 0);
      }, showLine: false),
    ];
    var noVideo = [
      _pixSheetWidget("图片拍摄", () {
        Navigator.pop(context, 1);
      }),
      _pixSheetWidget("从相册获取图片", () {
        Navigator.pop(context, 2);
      }),
      _pixSheetWidget("取消", () {
        Navigator.pop(context, 0);
      }, showLine: false),
    ];

    // 弹出选择框
    showModalBottomSheet(
      barrierColor: Colors.black.withOpacity(0.3),
      backgroundColor: Colors.transparent,
      context: context,
      builder: (context) {
        return Material(
          color: Colors.transparent,
          child: Container(
            padding:
                EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(18)),
            ),
            child: Container(
              padding: EdgeInsets.only(left: 15, right: 15),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: showVideo ? all : noVideo,
              ),
            ),
          ),
        );
      },
    ).then((value) {
      if (value != null && value is int) {
        onTap(value);
      }
    });
  }

  static Widget _pixSheetWidget(String text, Function() onTap,
      {bool showLine = true}) {
    return RippleButton(
      color: Colors.white,
      radius: 18,
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 55,
            alignment: Alignment.center,
            child: Text(
              text,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: Color(0xFF000000),
              ),
            ),
          ),
          showLine
              ? Divider(
                  height: 1,
                  color: Color(0xFFF5F5F5),
                )
              : SizedBox(),
        ],
      ),
    );
  }
}
