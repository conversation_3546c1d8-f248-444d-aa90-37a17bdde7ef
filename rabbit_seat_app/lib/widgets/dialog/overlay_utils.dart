import 'package:flutter/material.dart';

import 'trigon_path.dart';

/// 弹出层
class OverlayUtils {
  static OverlayEntry? _overlayEntry;

  /// 弹窗
  static void show(
      BuildContext context, Widget Function(BuildContext context) builder) {
    _overlayEntry = OverlayEntry(builder: (context) {
      return builder(context);
    });
    Overlay.of(context)?.insert(_overlayEntry!);
  }

  /// 弹出菜单
  static void showMenuView(
      {required BuildContext context,
        required GlobalKey key,
        required Size size,
        required Widget child}) {
    RenderObject? obj = key.currentContext!.findRenderObject();

    double window_width = MediaQuery.of(context).size.width;
    double window_height = MediaQuery.of(context).size.height;

    double _itemOffsetX = 0;
    double _itemOffsetY = 0;
    double _itemWidth = 0;
    double _itemHeight = 0;
    double _itemMiddleX = 0;

    double _boxWidth = size.width;
    double _boxHeight = size.height;

    double _boxOffsetX = 0;
    double _boxOffsetY = 0;

    bool _isUp = true;
    double _arrowWidth = 16;
    double _arrowHeight = 8;
    double _arrowOffsetX = 0;
    double _arrowOffsetY = 0;

    if (obj != null && obj is RenderBox) {
      RenderBox box = obj as RenderBox;

      // box 在当前 全局 的位置
      Offset boxOffset = box.localToGlobal(Offset.zero);

      /// 按钮位置
      _itemOffsetX = boxOffset.dx;
      _itemOffsetY = boxOffset.dy;

      /// 按钮大小
      _itemWidth = box.size.width;
      _itemHeight = box.size.height;

      _itemMiddleX = _itemOffsetX + _itemWidth / 2;

      // 距离两边的边距
      double _margin = 5;
      if (_itemMiddleX + _boxWidth / 2 > window_width - _margin) {
        // 右侧溢出
        _boxOffsetX = window_width - _boxWidth - _margin;
      } else if (_itemMiddleX - _boxWidth / 2 < _margin) {
        // 左侧溢出
        _boxOffsetX = _margin;
      } else {
        // 居中
        _boxOffsetX = _itemMiddleX - _boxWidth / 2;
      }

      _boxOffsetY = _itemOffsetY + _itemHeight + 15;
      _arrowOffsetX = _itemMiddleX - _arrowWidth / 2;
      _arrowOffsetY = _boxOffsetY - _arrowHeight + 1;

      if (_boxOffsetY > window_height * 0.6) {
        // 底部溢出
        if (_boxHeight > window_height / 2) {
          _boxHeight = window_height / 2;
          _boxOffsetY = _itemOffsetY - 15 - _boxHeight;
        } else {
          _boxOffsetY = _itemOffsetY - 15 - _boxHeight;
        }
        _isUp = false;
        _arrowOffsetY = _boxOffsetY + _boxHeight - 1;
      }
    }

    show(
      context,
          (context) {
        return Material(
          color: Colors.transparent,
          child: Stack(
            children: [
              // 空白处点击销毁
              GestureDetector(
                onTap: () {
                  close();
                },
                child: Container(
                  color: Colors.transparent,
                ),
              ),
              // 菜单
              Positioned(
                left: _boxOffsetX,
                top: _boxOffsetY,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(6)),
                    boxShadow: [
                      BoxShadow(
                        color: Color(0x38000000),//Color(0x63828282),
                        offset: Offset(0, 0),
                        blurRadius: 50,
                        spreadRadius: 5,
                      )
                    ],
                  ),
                  height: _boxHeight,
                  width: _boxWidth,
                  alignment: Alignment.center,
                  child: child,
                ),
              ),
              // 箭头
              Positioned(
                top: _arrowOffsetY,
                left: _arrowOffsetX,
                child: ClipPath(
                  clipper: _isUp ? TrigonUpPath() : TrigonDownPath(),
                  child: Container(
                    width: _arrowWidth,
                    height: _arrowHeight,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 关闭
  static void close() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
  }
}