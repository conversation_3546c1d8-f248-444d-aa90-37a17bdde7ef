import 'package:flutter/material.dart';
import 'dart:async';


/// 水波纹控件
class RippleWidget extends StatefulWidget {
  // 控件大小
  final Size size;
  // 动画 值
  final Tween<double> tween;
  // 线条颜色
  final Color color;
  // 时间
  final int duration;
  // 延迟
  final int delay;

  const RippleWidget({Key? key, required this.size, required this.tween, required this.color, this.duration = 4000, this.delay = 1250}) : super(key: key);

  @override
  State<RippleWidget> createState() => _RippleWidgetState();
}

class _RippleWidgetState extends State<RippleWidget>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // 动画控制器
  final List<AnimationController> _controllers = [];

  // 波纹组件
  final List<Widget> _waves = [];

  // 动画计时器
  Timer? _timer;

  // 波纹条数
  final int _count = 3;

  @override
  void initState() {
    super.initState();
    // 启动动画
    _startAnimation();
    //添加应用生命周期监听
    WidgetsBinding.instance!.addObserver(this);
  }

  @override
  void dispose() {
    // 销毁动画
    _disposeWaveAnimation();
    // 销毁应用生命周期观察者
    WidgetsBinding.instance!.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size.width,
      height: widget.size.height,
      child: Stack(
        alignment: Alignment.center,
        children: _waves,
      ),
    );
  }

  /// 启动动画，依次添加 count(默认3个) 个缩放动画，形成水波纹动画效果
  void _startAnimation() {
    //动画启动前确保_children控件总数为0
    _waves.clear();
    int count = 0;
    //添加第一个圆形缩放动画
    _addWaveAnimation(true);
    // 以后每隔1.5秒，再次添加一个缩放动画
    _timer = Timer.periodic(Duration(milliseconds: widget.delay), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      _addWaveAnimation(true);
      count++;
      if (count >= _count) {
        timer.cancel();
      }
    });
  }

  /// 添加蓝波纹动画控件
  /// init: 首次添控件时，=true，
  void _addWaveAnimation(bool init) {
    // 初始化动画控制器
    var controller = _initAnimationController();
    _controllers.add(controller);

    var animation = widget.tween
        .animate(CurvedAnimation(parent: controller, curve: Curves.linear));

    if (!init) {
      // 动画控件初始化完成的情况下，每次添加新的动画控件时，移除第一个，确保动画控件始终保持 count 个
      _waves.removeAt(0);
      //添加新的动画控件
      Future.delayed(Duration(milliseconds: widget.delay), () {
        if (!mounted) return;
        //动画页面没有执行退出情况下，继续添加动画
        _waves.add(AnimatedBuilder(
            animation: controller,
            builder: (BuildContext context, Widget? child) {
              return Opacity(
                opacity: 1.0 - ((animation.value - (widget.tween.begin as double)) / (widget.size.width - (widget.tween.begin as double))),
                child: Container(
                  width: animation.value,
                  height: animation.value,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(animation.value),
                    border: Border.all(color: widget.color, width: 1),
                  ),
                ),
              );
            }));
        try {
          //动画页退出时，捕获可能发生的异常
          controller.forward();
          setState(() {});
        } catch (e) {
          return;
        }
      });
    } else {
      _waves.add(AnimatedBuilder(
          animation: controller,
          builder: (BuildContext context, Widget? child) {
            return Opacity(
              opacity: 1.0 - ((animation.value - (widget.tween.begin as double)) / (widget.size.width - (widget.tween.begin as double))),
              child: ClipOval(
                child: Container(
                  width: animation.value,
                  height: animation.value,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(animation.value),
                    border: Border.all(color: widget.color, width: 1),
                  ),
                ),
              ),
            );
          }));
      // 启动动画
      controller.forward();
      setState(() {});
    }
  }

  /// 创建单个波纹动画控制器
  AnimationController _initAnimationController() {
    var controller = AnimationController(
        duration: Duration(milliseconds: widget.duration), vsync: this);
    controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        controller.dispose();
        if (_controllers.contains(controller)) {
          _controllers.remove(controller);
        }
        //每次动画控件结束时，添加新的控件，保持动画的持续性
        if (mounted) _addWaveAnimation(false);
      }
    });
    return controller;
  }

  /// 销毁动画
  void _disposeWaveAnimation() {
    // 释放动画所有controller
    for (var element in _controllers) {
      element.dispose();
    }
    _controllers.clear();
    _timer?.cancel();
    _waves.clear();
  }


  /// 监听应用状态，
  /// 生命周期变化时回调
  /// resumed:    应用可见并可响应用户操作
  /// inactive:   用户可见，但不可响应用户操作
  /// paused:     已经暂停了，用户不可见、不可操作
  /// suspending: 应用被挂起，此状态IOS永远不会回调
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.paused) {
      // 应用退至后台，销毁波纹动画
      _disposeWaveAnimation();
    } else if (state == AppLifecycleState.resumed) {
      // 应用回到前台，重新启动动画
      _startAnimation();
    }
  }
}
