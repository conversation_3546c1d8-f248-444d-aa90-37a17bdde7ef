import 'package:flutter/material.dart';

/// 平滑按钮
class GradualButtom extends StatelessWidget {
  /// 背景颜色
  Color? color;

  /// 圆角
  double radius = 0;

  /// 装饰器 (默认优先使用 decoration)
  BoxDecoration? decoration;

  /// 点击事件
  Function()? onTap;

  /// 子元素（子元素不允许包含 color 属性，不然 Ink 点击颜色不起作用）
  final Widget child;

  /// 构造器
  GradualButtom({
    Key? key,
    this.color,
    this.radius = 0,
    this.decoration,
    this.onTap,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    /// 初始化装饰器
    if (decoration == null) {
      decoration = BoxDecoration(
        color: color ?? Colors.purple,
        borderRadius: BorderRadius.all(Radius.circular(radius)),
      );
    }

    return Material(
      color: Colors.transparent,
      child: Ink(
        // 设置背景，如果子元素设置了背景，则点击颜失效
        decoration: decoration,
        child: InkResponse(
          borderRadius: decoration!.borderRadius as BorderRadius,
          // 点击或者toch控件高亮时显示的控件在控件上层,水波纹下层
          // highlightColor: Colors.purple[800],
          // 点击或者toch控件高亮的shape形状
          highlightShape: BoxShape.rectangle,
          // .InkResponse内部的radius这个需要注意的是，我们需要半径大于控件的宽，如果radius过小，显示的水波纹就是一个很小的圆，
          // 水波纹的半径
          radius: 0.0,
          // 水波纹的颜色 设置了highlightColor属性后 splashColor将不起效果
          // splashColor: Colors.red,
          // true表示要剪裁水波纹响应的界面 false不剪裁 如果控件是圆角不剪裁的话水波纹是矩形
          containedInkWell: true,
          onTap: onTap,
          child: child,
        ),
      ),
    );
  }
}