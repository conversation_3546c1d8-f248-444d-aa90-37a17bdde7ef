import 'package:flutter/material.dart';

/// 水波纹按钮
class RippleButton extends StatelessWidget {
  /// 背景颜色
  Color? color;

  /// 圆角
  double radius = 0;

  /// 装饰器 (默认优先使用 decoration)
  BoxDecoration? decoration;

  /// 点击事件
  Function()? onTap;

  /// 子元素（子元素不允许包含 color 属性，不然 Ink 点击颜色不起作用）
  final Widget child;

  /// 构造器
  RippleButton({
    Key? key,
    this.color,
    this.radius = 0,
    this.decoration,
    this.onTap,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    /// 初始化装饰器
    if (decoration == null) {
      decoration = BoxDecoration(
        color: color ?? Colors.purple,
        borderRadius: BorderRadius.all(Radius.circular(radius)),
      );
    }
    return Material(
      color: Colors.transparent,
      // INK可以实现装饰容器
      child: Ink(
        decoration: decoration,
        child: InkWell(
          // 圆角设置,给水波纹也设置同样的圆角
          // 如果这里不设置就会出现矩形的水波纹效果
          borderRadius: decoration!.borderRadius as BorderRadius,
          // 设置点击事件回调
          onTap: onTap,
          child: child,
        ),
      ),
    );
  }
}