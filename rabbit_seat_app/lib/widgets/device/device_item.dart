import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/utils/utils.dart';

class DeviceItem extends StatefulWidget {
  const DeviceItem(
      {Key? key,
        this.onTap,
        this.top,
        required this.title,
        this.icon,
        this.subTitle,
        this.type = 0})
      : super(key: key);

  final void Function()? onTap;
  final String? icon;
  final double? top;
  final String title;
  final String? subTitle;

  /// 0-默认 1-编辑
  final num type;

  @override
  State<StatefulWidget> createState() => _DeviceItemState();
}

class _DeviceItemState extends State<DeviceItem> {
  @override
  Widget build(BuildContext context) {

    Widget _iconWidget = Image.asset('assets/images/meal/4.png');//Container();
    // if (widget.icon != null) {
    //   _iconWidget = Image.network(widget.icon!, fit: BoxFit.cover,);
    // }
    return InkWell(
      onTap: () {
        if (widget.onTap != null) {
          widget.onTap!();
        }
      },
      child: Container(
        margin: EdgeInsets.only(top: widget.top != null ? widget.top! : 0.0, left: 12, right: 12),
        // width: 330.w,
        height: 60.h,
        child: Container(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: EdgeInsets.only(left: 15.w),
                child: Row(
                  children: [
                    Padding(padding: EdgeInsets.only(right: 18.w), child: ClipOval(child: Container(width: 40, height: 40, color: Color(0xFFE9E9E9), child: _iconWidget,),),),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(widget.title),
                        Util.getText(widget.subTitle,
                            color: const Color(0xFF666666))
                      ],
                    )
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.only(right: 20.w),
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Row(
                    children: [
                      Image(
                          image: const AssetImage('assets/images/my/1.png'),
                          width: 8.w)
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1), // 阴影的颜色
              offset: const Offset(10, 20), // 阴影与容器的距离
              blurRadius: 60.0, // 高斯的标准偏差与盒子的形状卷积。
              spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
            ),
          ],
        ),
      ),
    );
  }
}
