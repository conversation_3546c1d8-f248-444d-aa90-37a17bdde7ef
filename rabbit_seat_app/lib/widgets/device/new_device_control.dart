import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/device/device_model.dart';

class NewDeviceControl extends StatefulWidget {
  final DeviceModel? device;
  Function onControl;
  NewDeviceControl({Key? key, required this.device, required this.onControl})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _NewDeviceControlState();
}

class _NewDeviceControlState extends State<NewDeviceControl> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 14),
      child: Column(
        children: [
          Container(
              height: 112,
              padding: EdgeInsets.fromLTRB(50, 6, 50, 6),
              decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  borderRadius: BorderRadius.circular(79),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x14000000), // 阴影的颜色
                      offset: Offset(0, 0), // 阴影与容器的距离
                      blurRadius: 90.0, // 高斯的标准偏差与盒子的形状卷积。
                      spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
                    )
                  ]),
              child: Column(
                children: [
                  // rotate_command xifa
                  InkWell(
                      onTap: () {
                        widget.onControl("rotate_command", 3);
                      },
                      child: Container(
                        child: Image.asset(
                          "assets/images/device/up.png",
                          width: 32,
                        ),
                      )),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                          onTap: () {
                            widget.onControl("rotate_command", 2);
                          },
                          child: Container(
                            child: Image.asset("assets/images/device/left.png",
                                width: 32),
                          )),
                      InkWell(
                          onTap: () {
                            widget.onControl("rotate_command", 4);
                          },
                          child: Container(
                            child: Image.asset("assets/images/device/right.png",
                                width: 32),
                          )),
                    ],
                  ),
                  InkWell(
                      onTap: () {
                        widget.onControl("rotate_command", 1);
                      },
                      child: Container(
                        child: Image.asset("assets/images/device/down.png",
                            width: 32),
                      )),
                ],
              )),
          Container(
              margin: EdgeInsets.only(top: 14),
              height: 64,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  borderRadius: BorderRadius.circular(42),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x14000000), // 阴影的颜色
                      offset: Offset(0, 0), // 阴影与容器的距离
                      blurRadius: 90.0, // 高斯的标准偏差与盒子的形状卷积。
                      spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
                    )
                  ]),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                      child: Row(
                    children: [
                      Image.asset("assets/images/device/auto.png", width: 32),
                      Container(
                        margin: EdgeInsets.only(left: 10),
                        child: Column(
                          children: [
                            const Text(
                              "自动旋转",
                              style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold),
                            ),
                            Container(
                              // width: 36,
                              margin: EdgeInsets.only(top: 2),
                              padding: EdgeInsets.fromLTRB(5, 2, 5, 2),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: Color(widget.device!.autoRotate
                                    ? 0xff72C34B
                                    : 0xffFAAD14), // #FAAD14
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                widget.device!.autoRotate ? '已就绪' : "未就绪",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 11),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  )),
                  Container(
                    child: Switch(
                        value: widget.device?.autoRotate ?? false,
                        // activeColor: Business.mainColor, // 激活状态下滑块的颜色
                        activeTrackColor: Business.mainColor,
                        inactiveThumbColor: Colors.white, // 非激活状态下滑块的颜色
                        inactiveTrackColor: Color(0xffD8D8D8),
                        trackOutlineWidth:
                            WidgetStateProperty.resolveWith((states) {
                          if (!states.contains(WidgetState.selected)) {
                            return 0.0; // 关闭时边框宽度为 0（无边框）
                          }
                          return 2.0; // 开启时边框宽度为 2
                        }),
                        // 可选：设置轨道边框颜色
                        trackOutlineColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (!states.contains(WidgetState.selected)) {
                            return Colors.transparent; // 关闭时边框透明
                          }
                          return Colors.transparent; // 开启时边框颜色
                        }),
                        thumbIcon: WidgetStateProperty.resolveWith((states) {
                          return const Icon(Icons.circle,
                              color: Colors.white, size: 24.0); // 统一大小的圆点图标
                        }),
                        onChanged: (v) {
                          widget.onControl("auto_rotate", v);
                        }),
                  )
                ],
              )),
          Container(
              margin: EdgeInsets.only(top: 14),
              height: 64,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                  color: const Color(0xFFFFFFFF),
                  borderRadius: BorderRadius.circular(42),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x14000000), // 阴影的颜色
                      offset: Offset(0, 0), // 阴影与容器的距离
                      blurRadius: 90.0, // 高斯的标准偏差与盒子的形状卷积。
                      spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
                    )
                  ]),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                      child: Row(
                    children: [
                      Image.asset("assets/images/device/sleep.png", width: 32),
                      Container(
                          margin: EdgeInsets.only(left: 10),
                          child: const Text(
                            "低功耗待机",
                            style: TextStyle(
                                fontSize: 12,
                                color: Colors.black,
                                fontWeight: FontWeight.bold),
                          )),
                    ],
                  )),
                  Container(
                    child: Switch(
                        value: widget.device?.lptimOnoff ?? false,
                        // activeColor: Business.mainColor, // 激活状态下滑块的颜色
                        activeTrackColor: Business.mainColor,
                        inactiveThumbColor: Colors.white, // 非激活状态下滑块的颜色
                        inactiveTrackColor: Color(0xffD8D8D8),
                        trackOutlineWidth:
                            WidgetStateProperty.resolveWith((states) {
                          if (!states.contains(WidgetState.selected)) {
                            return 0.0; // 关闭时边框宽度为 0（无边框）
                          }
                          return 2.0; // 开启时边框宽度为 2
                        }),
                        // 可选：设置轨道边框颜色
                        trackOutlineColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (!states.contains(WidgetState.selected)) {
                            return Colors.transparent; // 关闭时边框透明
                          }
                          return Colors.transparent; // 开启时边框颜色
                        }),
                        thumbIcon: WidgetStateProperty.resolveWith((states) {
                          return const Icon(Icons.circle,
                              color: Colors.white, size: 24.0); // 统一大小的圆点图标
                        }),
                        onChanged: (v) {
                          widget.onControl("lptime_onoff", v);
                        }),
                  )
                ],
              )),

          Container(
            margin: EdgeInsets.only(top: 14),
            child: Row(
              children: [
                Expanded(
                    child: InkWell(
                  onTap: () {
                    widget.onControl(
                        "assist_rotate", !widget.device!.assistRotate);
                  },
                  child: Container(
                      height: 84,
                      padding: const EdgeInsets.fromLTRB(15, 12, 15, 12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.circular(42),
                        boxShadow: const [
                          BoxShadow(
                            color: Color(0x14000000), // 阴影的颜色
                            offset: Offset(0, 0), // 阴影与容器的距离
                            blurRadius: 90.0, // 高斯的标准偏差与盒子的形状卷积。
                            spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
                          )
                        ],
                      ),
                      child: Container(
                        padding: const EdgeInsets.only(top: 5, bottom: 5),
                        decoration: BoxDecoration(
                          color: widget.device!.assistRotate
                              ? Business.mainColor
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(42),
                          boxShadow: [
                            BoxShadow(
                              color: widget.device!.assistRotate
                                  ? Business.mainColor
                                  : Colors.transparent, // 阴影的颜色
                              offset: Offset(0, 5), // x, y 偏移
                              blurRadius: 10, // 模糊半径
                              spreadRadius: 0, // 扩散半径
                            )
                          ],
                        ),
                        child: Column(
                          children: [
                            Container(
                              child: Image.asset(
                                "assets/images/device/${widget.device!.assistRotate ? "help_select" : "help"}.png",
                                width: 26,
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 5),
                              child: Text("助力旋转",
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: widget.device!.assistRotate
                                          ? Colors.white
                                          : Colors.black,
                                      fontWeight: FontWeight.bold)),
                            )
                          ],
                        ),
                      )),
                )),
                const SizedBox(width: 11), // 添加间距
                Expanded(
                    child: InkWell(
                  onTap: () {
                    widget.onControl(
                        "mute_mode_switch", !widget.device!.muteModeSwitch);
                  },
                  child: Container(
                      height: 84,
                      padding: const EdgeInsets.fromLTRB(15, 12, 15, 12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.circular(42),
                        boxShadow: const [
                          BoxShadow(
                            color: Color(0x14000000), // 阴影的颜色
                            offset: Offset(0, 0), // 阴影与容器的距离
                            blurRadius: 90.0, // 高斯的标准偏差与盒子的形状卷积。
                            spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
                          )
                        ],
                      ),
                      child: Container(
                        padding: const EdgeInsets.only(top: 5, bottom: 5),
                        decoration: BoxDecoration(
                          color: widget.device!.muteModeSwitch
                              ? Business.mainColor
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(42),
                          boxShadow: [
                            BoxShadow(
                              color: widget.device!.muteModeSwitch
                                  ? Business.mainColor
                                  : Colors.transparent, // 阴影的颜色
                              offset: const Offset(0, 5), // x, y 偏移
                              blurRadius: 10, // 模糊半径
                              spreadRadius: 0, // 扩散半径
                            )
                          ],
                        ),
                        child: Column(
                          children: [
                            Container(
                              child: Image.asset(
                                "assets/images/device/${widget.device!.muteModeSwitch ? "mute_select" : "mute"}.png",
                                width: 26,
                              ),
                            ),
                            Container(
                              margin: const EdgeInsets.only(top: 5),
                              child: Text("静音模式",
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: widget.device!.muteModeSwitch
                                          ? Colors.white
                                          : Colors.black,
                                      fontWeight: FontWeight.bold)),
                            )
                          ],
                        ),
                      )),
                )),
              ],
            ),
          ),

          // 自动通风温度阈值
          InkWell(
            onTap: () async {
              final result = await _showTemperaturePicker(context,
                  widget.device?.autoFanTemp ?? 24, [24, 30], "自动通风温度阈值");
              if (result != null) {
                widget.onControl("auto_fan_temp", result);
              }
            },
            child: Container(
                margin: const EdgeInsets.only(top: 14),
                height: 64,
                padding: const EdgeInsets.fromLTRB(12, 12, 18, 12),
                decoration: BoxDecoration(
                    color: const Color(0xFFFFFFFF),
                    borderRadius: BorderRadius.circular(42),
                    boxShadow: const [
                      BoxShadow(
                        color: Color(0x14000000), // 阴影的颜色
                        offset: Offset(0, 0), // 阴影与容器的距离
                        blurRadius: 90.0, // 高斯的标准偏差与盒子的形状卷积。
                        spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
                      )
                    ]),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                        child: Row(
                      children: [
                        Image.asset("assets/images/device/wind.png", width: 32),
                        Container(
                          margin: const EdgeInsets.only(left: 10),
                          child: const Text(
                            "自动通风温度阈值",
                            style: TextStyle(
                                fontSize: 12,
                                color: Colors.black,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    )),
                    Container(
                      child: Row(
                        children: [
                          Text(
                            "${widget.device?.autoFanTemp ?? 26}°C",
                            style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xffA9A9A9),
                                fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(width: 6),
                          const Icon(Icons.arrow_forward_ios,
                              size: 16, color: Color(0xffA9A9A9)),
                        ],
                      ),
                    )
                  ],
                )),
          ),

          // 自动通风温度阈值
          InkWell(
              onTap: () async {
                final result = await _showTemperaturePicker(context,
                    widget.device?.autoHeatTemp ?? 12, [12, 18], "自动加热温度阈值");
                if (result != null) {
                  widget.onControl("auto_heat_temp", result);
                }
              },
              child: Container(
                  margin: const EdgeInsets.only(top: 14),
                  height: 64,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                      color: const Color(0xFFFFFFFF),
                      borderRadius: BorderRadius.circular(42),
                      boxShadow: const [
                        BoxShadow(
                          color: Color(0x14000000), // 阴影的颜色
                          offset: Offset(0, 0), // 阴影与容器的距离
                          blurRadius: 90.0, // 高斯的标准偏差与盒子的形状卷积。
                          spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
                        )
                      ]),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                          child: Row(
                        children: [
                          Image.asset("assets/images/device/hot.png",
                              width: 32),
                          Container(
                            margin: const EdgeInsets.only(left: 10),
                            child: const Text(
                              "自动加热温度阈值",
                              style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                        ],
                      )),
                      Container(
                        child: Row(
                          children: [
                            Text(
                              "${widget.device?.autoHeatTemp ?? 26}°C",
                              style: const TextStyle(
                                  fontSize: 14,
                                  color: Color(0xffA9A9A9),
                                  fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(width: 6),
                            const Icon(Icons.arrow_forward_ios,
                                size: 16, color: Color(0xffA9A9A9)),
                          ],
                        ),
                      )
                    ],
                  ))),
        ],
      ),
    );
  }

  // 显示底部温度选择弹窗的方法
  Future<int?> _showTemperaturePicker(
      BuildContext context, initTemp, List<int> tempLimit, String title) {
    // 弹窗内的临时温度状态，仅在确认后更新主页面
    int tempSelectedTemperature = initTemp.toInt();

    return showModalBottomSheet<int>(
      context: context,
      backgroundColor: Colors.white,
      // 设置圆角
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
      ),
      builder: (BuildContext context) {
        // 使用 StatefulBuilder 来管理弹窗内部的状态，避免刷新整个页面
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter modalSetState) {
            return SizedBox(
              height: 350, // 调整弹窗的整体高度
              child: Column(
                children: [
                  // 1. 标题
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 20.0),
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 18.0,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),

                  // 2. 温度选择器
                  Expanded(
                    child: CupertinoPicker(
                      // 初始选中的项目
                      scrollController: FixedExtentScrollController(
                        initialItem: tempSelectedTemperature - tempLimit[0],
                      ),
                      itemExtent: 48.0, // 每个项目的高度
                      onSelectedItemChanged: (int index) {
                        // 当选择项改变时，更新弹窗内的临时温度
                        modalSetState(() {
                          tempSelectedTemperature = tempLimit[0] + index;
                        });
                      },
                      // 生成温度列表
                      children: List<Widget>.generate(
                          tempLimit[1] - tempLimit[0] + 1, (int index) {
                        final int currentTemp = tempLimit[0] + index;
                        final bool isSelected =
                            currentTemp == tempSelectedTemperature;
                        return Center(
                          child: Text(
                            isSelected ? '$currentTemp℃' : '$currentTemp',
                            style: TextStyle(
                              fontSize: isSelected ? 26 : 24,
                              color: isSelected
                                  ? Business.mainColor
                                  : Color(0xffA9A9A9),
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                        );
                      }),
                    ),
                  ),

                  // 3. 底部按钮
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        // --- MODIFICATION START ---
                        // 取消按钮
                        Expanded(
                          child: InkWell(
                            // 设置与Container一致的圆角，使点击效果也被裁剪
                            borderRadius: BorderRadius.circular(25),
                            onTap: () {
                              Navigator.pop(context); // 关闭弹窗
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: const Color(0xFFF5F5F5), // 浅灰色背景
                                borderRadius: BorderRadius.circular(25),
                              ),
                              child: const Text(
                                '取消',
                                style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        // 确定按钮
                        Expanded(
                          child: InkWell(
                            borderRadius: BorderRadius.circular(25),
                            onTap: () {
                              Navigator.pop(
                                  context, tempSelectedTemperature); // 关闭弹窗
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: Business.mainColor, // 橙色背景
                                borderRadius: BorderRadius.circular(25),
                              ),
                              child: const Text(
                                '确定',
                                style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          ),
                        ),
                        // --- MODIFICATION END ---
                      ],
                    ),
                  )
                ],
              ),
            );
          },
        );
      },
    );
  }
}
