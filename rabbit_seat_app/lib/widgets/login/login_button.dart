import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';

// 登录按钮
class LoginButton extends StatelessWidget {
  Function()? onTap;
  final String text;

  LoginButton({
    Key? key,
    required this.text,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 55.h,
        decoration: BoxDecoration(
          color: Business.mainColor,
          borderRadius: BorderRadius.all(Radius.circular(55.h / 2)),
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
              fontSize: 18.sp, fontWeight: FontWeight.bold, color: Colors.white),
        ),
      ),
    );
  }
}
