import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluwx/fluwx.dart';
import 'package:rabbit_seat_app/models/login/login_model.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/views/login/index.dart';
import 'package:tuya/tuya.dart';

import '../../utils/global.dart';

// 其他登录模式
class LoginModeWidget extends StatelessWidget {
  LoginModeWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _lineWidget(),
              Padding(
                padding: EdgeInsets.only(left: 8.5.w, right: 8.5.w),
                child: Text(
                  "其他登录方式",
                  style: TextStyle(
                      fontSize: 11.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white),
                ),
              ),
              _lineWidget(),
            ],
          ),
          SizedBox(
            height: 19.5.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _itemWidget(
                asset: "assets/images/l_wx.png",
                onTap: () {
                  _weChatLoginAuth(context);
                },
              ),
              // SizedBox(
              //   width: 23,
              // ),
              // _itemWidget(
              //   asset: "assets/images/l_qq.png",
              //   onTap: () {
              //     _qqLoginAuth(context);
              //   },
              // ),
            ],
          )
        ],
      ),
    );
  }

  Widget _itemWidget({required String asset, required Function() onTap}) {
    return InkWell(
      onTap: onTap,
      child: SizedBox(
        width: 36.w,
        height: 36.w,
        child: Image.asset(
          asset,
          fit: BoxFit.fill,
        ),
      ),
    );
  }

  Widget _lineWidget() {
    return SizedBox(
      width: 11.w,
      child: Divider(
        height: 1,
        color: Colors.white,
      ),
    );
  }

  bool _repeat = false;

  void handleWxResponse(WeChatAuthResponse event, BuildContext context) {
    /// 去重复
    if (_repeat == true) return;
    _repeat = true;
    Future.delayed(Duration(milliseconds: 1500), () => _repeat = false);

    print(">>> event:${event.toString()}");
    int? errCode = event.errCode;
    print(">>> 微信登录返回值：ErrCode:$errCode   code:${event.code}");
    if (errCode == 0) {
      String? code = event.code;

      print(">>> 微信登录--用户同意授权成功");
      print("**********");
      // 调用token，跳转页面，绑定手机号，
      if (context != null) {
        wxLoginAction(context, code!);
      }
    } else if (errCode == -4) {
      print(">>> 微信登录--用户拒绝授权");
    } else if (errCode == -2) {
      print(">>> 微信登录--用户取消授权");
    }
  }

  /// 微信登录授权
  void _weChatLoginAuth(BuildContext context) {
    var fluwx = Fluwx();

    /// 注册微信
    fluwx.registerApi(
      appId: 'wx21996e7b57190e41',
      universalLink: 'https://api.buddybuzzy.com/seat/',
    );

    listener(response) {
      if (response is WeChatAuthResponse) {
        handleWxResponse(response, context);
      }
    }
    fluwx.addSubscriber(listener); // 订阅消息

    // 微信登录
    fluwx
        .authBy(
            which: NormalAuth(
                scope: 'snsapi_userinfo', state: 'wechat_sdk_rabbit'))
        .then((value) {
      print(">>> auth:$value");
      if (!value) {
        EasyLoading.showToast('没有安装微信，请安装微信后使用！');
      }
    });
  }

  /// QQ登录授权
  void _qqLoginAuth(BuildContext context) {
    // Navigator.push(context,
    //     MaterialPageRoute(builder: (_) => BindPhoneView()));
  }

  /// 微信登录
  void wxLoginAction(BuildContext context, String code) async {
    try {
      EasyLoading.show();
      final data = await Http.post('/api/token/wechatLogin/$code');
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.success == true) {
        // 登录信息
        LoginModel _model =
            LoginModel.fromJson(result.data! as Map<String, dynamic>);

        /// 登录 Tuya
        final res = await Tuya.login(_model.user!.telephone!, 'Passw0rDSeat!1');
        if (res != null) {
          EasyLoading.dismiss();

          Global.profile.token = _model.token;
          Global.profile.user = _model.user;
          Global.profile.user?.id = _model.uid;
          Global.saveProfile();

          // 页面跳转
          Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
        } else {
          print(">>> 登录失败！");
          EasyLoading.showToast('登录失败！');
        }
      } else if (result.success == false) {
        if (result.data != null && result.data is String) {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (_) => BindPhoneView(
                        openId: result.data as String,
                      )));
        }
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}
