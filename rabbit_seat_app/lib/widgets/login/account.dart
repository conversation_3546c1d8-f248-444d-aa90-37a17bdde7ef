import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 账户
class AccountWidget extends StatefulWidget {
  String? account;
  Color? textColor;
  Color? hintTextColor;
  Function(String)? onChanged;

  AccountWidget({
    Key? key,
    this.account,
    this.textColor,
    this.hintTextColor,
    this.onChanged,
  }) : super(key: key);

  @override
  State<AccountWidget> createState() => _AccountWidgetState();
}

class _AccountWidgetState extends State<AccountWidget> {
  late TextEditingController controller;

  @override
  void initState() {
    super.initState();

    controller = TextEditingController(text: widget.account);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 29.5.w, right: 29.5.w),
      height: 55.h,
      decoration: BoxDecoration(
        color: Color(0x0D000000),
        borderRadius: BorderRadius.all(Radius.circular(55.h / 2)),
      ),
      alignment: Alignment.center,
      child: Row(
        // mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "+86",
            style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: widget.textColor != null ? widget.textColor : Colors.white,
                  ),
          ),
          SizedBox(
            width: 26.w,
          ),
          Expanded(
            child: TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              onChanged: (value) {
                if (widget.onChanged != null) widget.onChanged!(value);
              },
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: widget.textColor != null ? widget.textColor : Colors.white,
              ),
              cursorColor: widget.textColor != null ? widget.textColor : Colors.white,
              textAlignVertical: TextAlignVertical.center,
              decoration: InputDecoration(
                hintText: "请输入手机号码",
                hintStyle: TextStyle(fontSize: 15.sp, color: widget.hintTextColor != null ? widget.hintTextColor : Color(0xB3FFFFFF)),
                contentPadding: EdgeInsets.only(top: 0, bottom: 0),
                border: OutlineInputBorder(borderSide: BorderSide.none),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
