import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/views/login/index.dart';

// 协议
class AgreementWidget extends StatefulWidget {
  Function(bool)? onTickChange;

  AgreementWidget({Key? key, this.onTickChange}) : super(key: key);

  @override
  _AgreementWidgetState createState() => _AgreementWidgetState();
}

class _AgreementWidgetState extends State<AgreementWidget> {
  bool isTick = false;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        GestureDetector(
            onTap: () {
              setState(() {
                isTick = !isTick;
              });
              if (widget.onTickChange != null) widget.onTickChange!(isTick);
            },
            child: Container(
              padding: EdgeInsets.only(top: 3.h, right: 6.5.w),
              alignment: Alignment.center,
              child: Image.asset(
                isTick ? "assets/images/l_ck.png" : "assets/images/l_unck.png",
                width: 14,
                height: 14,
              ),
            )),
        Expanded(
          child: RichText(
            softWrap: true,
            maxLines: 2,
            text: TextSpan(
                text: "您已同意",
                style: TextStyle(fontSize: 12, color: Color(0xFFA2A2A2)),
                children: [
                  TextSpan(
                      text: "《两只兔子用户服务协议》",
                      style:
                          TextStyle(fontSize: 12.sp, color: Color(0xFF4F4F4F)),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          print("协议");
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => AgreementView()));
                        }),
                  // TextSpan(
                  //   text: "和",
                  //   style: TextStyle(fontSize: 12, color: Color(0xFFA2A2A2)),
                  // ),
                  // TextSpan(
                  //   text: "《两只兔子隐私权政策》",
                  //   style: TextStyle(fontSize: 12, color: Color(0xFF4F4F4F)),
                  //   recognizer: TapGestureRecognizer()
                  //     ..onTap = () {
                  //       print("政策");
                  //       Navigator.push(
                  //           context,
                  //           MaterialPageRoute(
                  //               builder: (context) => PolicyView()));
                  //     },
                  // ),
                ]),
          ),
        ),
      ],
    );
  }
}
