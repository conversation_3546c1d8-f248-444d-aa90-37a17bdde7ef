import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/login/sms_code_model.dart';
import 'package:rabbit_seat_app/utils/utils.dart';

import '../../../services/token_service.dart';

// 验证码
class VerifyCodeWidget extends StatefulWidget {
  String? phone;
  Color? textColor;
  Color? hintTextColor;
  Color? itemTextColor;

  Function(String)? onChanged;

  VerifyCodeWidget({
    Key? key,
    this.phone,
    this.textColor,
    this.hintTextColor,
    this.itemTextColor,
    this.onChanged,
  }) : super(key: key);

  @override
  State<VerifyCodeWidget> createState() => _VerifyCodeWidgetState();
}

class _VerifyCodeWidgetState extends State<VerifyCodeWidget> {
  // 秒
  var seconds = 0;

  // 计时器
  Timer? timer;

  String _itemText = "获取验证码";

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 29.5.w, right: 29.5.w),
      height: 55.h,
      decoration: BoxDecoration(
        color: Color(0x0D000000),
        borderRadius: BorderRadius.all(Radius.circular(55.h / 2)),
      ),
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: TextField(
              keyboardType: TextInputType.number,
              onChanged: (value) {
                if (widget.onChanged != null) widget.onChanged!(value);
              },
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: widget.textColor != null ? widget.textColor : Colors.white,
              ),
              cursorColor: widget.textColor != null ? widget.textColor : Colors.white,
              textAlignVertical: TextAlignVertical.center,
              decoration: InputDecoration(
                hintText: "输入验证码",
                hintStyle: TextStyle(fontSize: 15.sp, color: widget.hintTextColor != null ? widget.hintTextColor : Color(0xB3FFFFFF)),
                contentPadding: EdgeInsets.only(top: 0, bottom: 0),
                border: OutlineInputBorder(borderSide: BorderSide.none),
              ),
            ),
          ),
          SizedBox(
            width: 15.w,
          ),
          GestureDetector(
            onTap: () async {
              print("发送断行验证码");
              // 移除软键盘
              Util.removePrimaryFocus(context);
              // 发送短信
              _sendVerifyCode();
            },
            child: Container(
              height: 45.h,
              width: 95.w,
              alignment: Alignment.centerRight,
              child: Text(
                _itemText,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  color: widget.itemTextColor != null ? widget.itemTextColor : Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 计时器
  void createTimer() {
    // 销毁计时器
    disposeTimer();
    // 计时器
    if (timer == null) {
      seconds = 60;
      timer = Timer.periodic(Duration(seconds: 1), (timer) {
        // print(">>> send code timer seconds:$seconds");
        setState(() {
          if (seconds > 0) {
            seconds--;
            _itemText = "$seconds秒后重新发送";
          } else {
            _itemText = "获取验证码";
            // 销毁计时器
            disposeTimer();
          }
        });
      });
    }
  }

  /// 销毁计时器
  void disposeTimer() {
    if (timer != null && timer!.isActive) {
      timer!.cancel();
      timer = null;
    }
  }


  /// 发送短信验证码
  void _sendVerifyCode() async {
    if (widget.phone == null || widget.phone!.isEmpty) {
      EasyLoading.showToast("请输入手机号码！");
      return;
    }
    print(">>> -phone:${widget.phone}");
    EasyLoading.show();
    TokenService.sendSMSCode(phone: widget.phone!, success: (data) {
      EasyLoading.dismiss();
      // 发送成功
      createTimer();
    }, failure: (msg) {
      EasyLoading.dismiss();
      EasyLoading.showToast(msg);
    });
  }

  @override
  void dispose() {
    super.dispose();

    disposeTimer();
  }
}
