import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../utils/utils.dart';


class MyBoxEdit extends StatefulWidget {
  const MyBoxEdit(
      {Key? key,
      this.onTap,
      this.top,
      required this.title,
      this.subTitle,
      this.type = 0,
      this.headerImage,
      this.headerIcon})
      : super(key: key);

  final void Function()? onTap;
  final double? top;
  final String title;
  final String? subTitle;
  final String? headerImage;
  final Widget? headerIcon;

  /// 0-默认 1-编辑
  final num type;

  @override
  State<StatefulWidget> createState() => _MyBoxEditState();
}

class _MyBoxEditState extends State<MyBoxEdit> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (widget.onTap != null) {
          widget.onTap!();
        }
      },
      child: Container(
        margin: EdgeInsets.only(top: widget.top != null ? widget.top! : 0.0),
        width: 330.w,
        height: 55.h,
        child: Container(
          margin: EdgeInsets.only(left: 20.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(children: [
                widget.headerImage != null
                    ? Container(
                        padding: EdgeInsets.only(right: 12.w),
                        child: Image(
                            image: AssetImage(widget.headerImage!),
                            width: 34.w),
                      )
                    : Container(),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Util.getText(widget.title,
                            fontSize: 14, fontWeight: FontWeight.w600),
                        widget.headerIcon ?? Container()
                      ],
                    ),
                    Util.getText(widget.subTitle,
                        color: const Color(0xFF666666))
                  ],
                ),
              ]),
              Container(
                padding: EdgeInsets.only(right: 20.w),
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Image(
                      image: const AssetImage('assets/images/my/edit.png'),
                      width: 16.w),
                ),
              )
            ],
          ),
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1), // 阴影的颜色
              offset: const Offset(10, 20), // 阴影与容器的距离
              blurRadius: 60.0, // 高斯的标准偏差与盒子的形状卷积。
              spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
            ),
          ],
        ),
      ),
    );
  }
}
