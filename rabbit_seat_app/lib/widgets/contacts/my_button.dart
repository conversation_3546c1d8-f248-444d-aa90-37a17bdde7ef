import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MyButton extends StatefulWidget {
  const MyButton(
      {Key? key,
      required this.onTap,
      required this.text,
      this.backround = const Color.fromARGB(255, 255, 119, 0),
      this.color = Colors.white,
      required this.width,
      required this.height,
      this.icon})
      : super(key: key);

  final Function() onTap;
  final double width;
  final double height;
  final Color backround;
  final Color color;
  final String text;
  final Icon? icon;

  @override
  _MyButtonState createState() => _MyButtonState();
}

class _MyButtonState extends State<MyButton> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _buildBody();
  }

  Widget _buildBody() {
    return InkWell(
        onTap: () {
          widget.onTap();
        },
        child: Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.backround,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
              child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              widget.icon != null ? widget.icon! : Container(),
              Text(widget.text,
                  style: TextStyle(fontSize: 18.sp, color: widget.color))
            ],
          )),
        ));
  }
}
