import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluwx/fluwx.dart';
import 'package:url_launcher/url_launcher.dart';

class UITest extends StatefulWidget {
  const UITest({Key? key}) : super(key: key);

  @override
  State<UITest> createState() => _UITestState();
}

class _UITestState extends State<UITest> with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕

    return Scaffold(
        appBar: AppBar(
          title: Text("UITest"),
        ),
        body: Column(
          children: [
            ElevatedButton(
                onPressed: () {
                  _weChatLoginAuth(context);
                },
                child: Text("微信登录")),
          ],
        ));
  }

  bool _repeat = false;

  void handleWxResponse(WeChatAuthResponse event, BuildContext context) {
    /// 去重复
    if (_repeat == true) return;
    _repeat = true;
    Future.delayed(Duration(milliseconds: 1500), () => _repeat = false);

    print(">>> event:${event.toString()}");
    int? errCode = event.errCode;
    print(">>> 微信登录返回值：ErrCode:$errCode   code:${event.code}");
    if (errCode == 0) {
      String? code = event.code;

      print(">>> 微信登录--用户同意授权成功");
      print("**********");
      // 调用token，跳转页面，绑定手机号，
      if (context != null) {
        wxLoginAction(context, code!);
      }
    } else if (errCode == -4) {
      print(">>> 微信登录--用户拒绝授权");
    } else if (errCode == -2) {
      print(">>> 微信登录--用户取消授权");
    }
  }

  /// 微信登录授权
  void _weChatLoginAuth(BuildContext context) async {
    var fluwx = Fluwx();
    bool res = await fluwx.isWeChatInstalled;
    if (res) {
      print(">>> 安装了微信");
    } else {
      print(">>> 没有安装微信");
    }

    /// 注册微信
    fluwx.registerApi(
      appId: 'wx21996e7b57190e41',
      universalLink: 'https://api.buddybuzzy.com/seat/',
    );

    var listener = (response) {
      if (response is WeChatAuthResponse) {
        handleWxResponse(response, context);
      }
    };
    fluwx.addSubscriber(listener); // 订阅消息

    // 微信登录
    fluwx
        .authBy(
            which: NormalAuth(
                scope: 'snsapi_userinfo', state: 'wechat_sdk_rabbit'))
        .then((value) {
      print(">>> auth:$value");
      if (!value) {
        EasyLoading.showToast('没有安装微信，请安装微信后使用！');

        _aaaaaaa();
      }
    });
  }

  /// 微信登录
  void wxLoginAction(BuildContext context, String code) async {
    print(">>> 获取到code 请求服务器地址，实现登录！");
  }

  void _aaaaaaa() async {
    var appId = 'wx21996e7b57190e41';
    //这个是授权同意后的回调地址，自己可以建一个空白页面，将链接放上去，在空白页面中获得code
    // https://api.buddybuzzy.com/seat/
    var redirectUrl = Uri.encodeFull(
        'https://api.buddybuzzy.com/seat/'); //urlEncode(text: UrlKeys.REDIRECT_URL);
    String requestUrl =
        "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" +
            appId +
            "&redirect_uri=" +
            'null' +
            "&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect";

    // https://open.weixin.qq.com/connect/qrconnect?appid=wx21996e7b57190e41&redirect_uri=&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect

    // https://open.weixin.qq.com/connect/qrconnect?appid=wx21996e7b57190e41&redirect_uri=&response_type=code&scope=snsapi_login&state=1#wechat_redirect

    if (await canLaunch(requestUrl) != null) {
      await launch(requestUrl);
    } else {
      throw "Could not open link (" + requestUrl + ").";
    }
  }

  @override
  bool get wantKeepAlive => true;
}
