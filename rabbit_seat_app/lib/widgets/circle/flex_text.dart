import 'dart:math';
import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:rabbit_seat_app/business.dart';

/// 在文本超出指定行数之后，会显示 展开/收起 按钮的文本展示组件
///
/// 用例1: 常规使用
/// TextLimitDisplay(
///   text: '一大段文字...',
///   minLines: 2,
///   textStyle: TextStyle(color: Colors.blue),
/// )
///
/// 用例2：特性化使用-比如 一段文字结尾显示的是 编辑，而点击编辑按钮之后要做一些事, 且显示样式不变
/// TextLimitDisplay(
///   text: '一大段文字...',
///   minLines: 2, // 这两个长度也要设置一致
///   maxLines: 2,
///   isAlwaysDisplay: true, // 按钮始终显示
///   textStyle: TextStyle(color: Colors.blue),
///   shrinkText: '编辑', // 这两个文本文字要设置成一样的
///   expandText: '编辑',
///   onShrink: onEdit, // 两个点击事件也要传递同样的事件
///   onExpand: onEdit,
/// )
class FlexText extends StatefulWidget {
  /// 显示文本
  String text;

  /// 最少展示几行-在收起的状态下展示几行
  final int minLines;

  /// 最多展示几行-在展开的状态下展示几行，
  /// 默认最多支持显示99行，要显示更多的行数需要手动传入
  final int maxLines;

  /// 文字整体样式
  final TextStyle? textStyle;

  /// 收起状态下按钮文字
  final String shrinkText;

  /// 收起状态下按钮文字
  final TextStyle? shrinkStyle;

  ///  展开状态下的按钮文字
  final String expandText;

  ///  展开状态下的按钮文字
  final TextStyle? expandStyle;

  /// 当点击展开的时候 当点击收起的时候
  final Function(bool)? onTap;

  FlexText({
    Key? key,
    required this.text,
    this.textStyle,
    this.minLines = 3,
    this.maxLines = 99,
    this.shrinkText = '展开',
    this.shrinkStyle,
    this.expandText = '收起',
    this.expandStyle,
    this.onTap,
  }) : super(key: key);

  @override
  _FlexTextState createState() => _FlexTextState();
}

class _FlexTextState extends State<FlexText> {
  final GlobalKey _key = GlobalKey();

  bool _isExpand = false;

  bool _isOver = false;

  late String _shrinkText;

  late String _expandText;

  @override
  void initState() {
    init();
    super.initState();
  }

  @override
  void dispose() {
    _key.currentState?.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(FlexText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.text.compareTo(oldWidget.text) != 0) {
      init();
    }
  }

  void init() {
    _shrinkText = widget.text;
    _expandText = widget.text;
    if (widget.minLines != null && widget.minLines > 0) {
      initValue();
    }
  }

  /// 控制按钮文本
  String get _activeText => _isExpand ? _expandBtnText : _shrinkBtnText;

  /// 显示文本内容
  String get _showText => _isExpand ? _expandText : _shrinkText;

  /// 最小行数
  int get _minLines => _isExpand ? _maxLines : widget.minLines;

  /// 最大行数
  int get _maxLines => widget.maxLines;

  bool _isOverflow(List metrics, int len) => metrics.length > len;

  /// 收起文本
  String get _shrinkBtnText => ' ${widget.shrinkText}';

  /// 展开文本
  String get _expandBtnText => ' ${widget.expandText}';

  /// 文本样式
  TextStyle get _textStyle => TextStyle(fontSize: 15, color: Color(0xFF000000));

  // Icon? _icon(bool isExpand) =>
  //     isExpand ? widget.shrinkIcon : widget.expandIcon;

  void initValue() {
    // TODO: 暂时还剩一个问题是这种延时处理的方式无法保证文本组件一定渲染完成了
    Future.delayed(Duration(), () {
      // 如果没有渲染完成那么这里就会报错，无法获取到size
      List<LineMetrics?> lineMetrics = [];
      try {
        lineMetrics = _paintText(widget.text);
      } catch (_) {
        lineMetrics = List.generate(
          max(99, widget.maxLines),
              (index) => null,
        );
      }
      setState(() {
        _isOver = _isOverflow(lineMetrics, widget.minLines);
        if (_isOver) {
          _shrinkText =
              _calcShrinkText(lineMetrics, widget.minLines, _shrinkBtnText);
          _expandText = _calcShrinkText(
            lineMetrics,
            _maxLines,
            _expandBtnText,
            isExpand: true,
          );
        }
      });
    });
  }

  List<LineMetrics> _paintText(String text, {double? maxWidth}) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      text: TextSpan(text: text, style: _textStyle),
    );
    if (maxWidth != null) {
      textPainter.layout(minWidth: maxWidth, maxWidth: maxWidth);
    } else {
      final textWidth = _key.currentContext?.size?.width;
      textPainter.layout(minWidth: textWidth ?? 0, maxWidth: textWidth ?? 0);
    }

    return textPainter.computeLineMetrics();
  }

  bool _lenEquals(String text, int len) => _paintText(text).length == len;

  void _tapRecognizer() {
    setState(() => _isExpand = !_isExpand);

    if (widget.onTap != null) widget.onTap!(_isExpand);
  }

  String _getOverflowedText(
      String shrinkText, {
        String trailingText = '',
        int limitLen = 0,
      }) {
    int textLen = widget.text.length;
    int step = 1;
    int count = 0;
    while (!_lenEquals('$shrinkText$trailingText', limitLen + 1)) {
      if (shrinkText.length + step > textLen) {
        count++;
        if (count > 10) {
          // 死循环检测处理
          break;
        }
        if (step < 1) {
          break;
        } else {
          step = step ~/ 2;
        }
      } else {
        shrinkText = widget.text.substring(0, shrinkText.length + step);
        step += 4;
      }
    }
    return shrinkText;
  }

  String _calcShrinkText(
      List<LineMetrics?> lineMetrics,
      int len,
      String trailingBtnText, {
        bool isExpand = false,
      }) {
    // 如果限制的最大行数超过了实际渲染出来的最大行数，说明根据不会出现溢出的情况
    if (len > lineMetrics.length) return widget.text;

    final String spacerText = _isOverflow(lineMetrics, len) ? '...' : '';

    String trailingText;

    trailingText = '$spacerText$trailingBtnText';

    // 计算出n行文本大致的字数，根据字数从原文本截取相应的字符串
    // double nTextLength = min(len, lineMetrics.length) *
    //     _key.currentContext.size.width /
    //     _textStyle.fontSize;
    // String shrinkText = widget.text.substring(0, nTextLength.toInt());
    // 暂时不能从这里计算长度，当文字带有换行的时候会出问题
    String shrinkText = '';
    // 下面加上结尾字符不一定会换行，因为字体不是等宽字体
    // 所以需要先确定溢出才开始倒算
    shrinkText = _getOverflowedText(
      shrinkText,
      trailingText: trailingText,
      limitLen: len,
    );
    // 这个时候这些文字加上结尾附加文字一定是会换行的
    // 所以一直判断如果文字换行的行数大于传入的指定行数，那么就从文字末尾减去一个字符
    // 再继续判断，直到文本行数等于传入的指定行数为止
    // 使用runes计算，避免表情在某些位置报错
    final shrinkTextRunnes = shrinkText.runes.toList();
    int offset = 0;
    while (!_lenEquals('$shrinkText$trailingText', len)) {
      if (shrinkTextRunnes.length - offset > 0) {
        // shrinkText = shrinkText.substring(0, shrinkText.length - 1);
        offset++;
        shrinkText = String.fromCharCodes(
            shrinkTextRunnes.sublist(0, shrinkTextRunnes.length - offset));
      } else {
        break;
      }
    }
    return '$shrinkText$spacerText';
  }

  InlineSpan _trailingBtn() {
    return TextSpan(
      text: _activeText,
      style: _textStyle.copyWith(color: Business.mainColor),
      recognizer: TapGestureRecognizer()..onTap = _tapRecognizer,
    );
  }

  Widget _handleText() {
    return Stack(
      children: [
        Text.rich(
          TextSpan(
            text: _showText,
            children: [
              if (_isOver) _trailingBtn(),
            ],
          ),
          key: _key,
          maxLines: _minLines,
          softWrap: true,
          overflow: TextOverflow.ellipsis,
          style: _textStyle,
        ),
      ],
    );
  }

  Widget _dispatch() {
    if (widget.minLines != null && widget.minLines > 0) {
      return _handleText();
    }
    return Text(
      widget.text,
      maxLines: widget.minLines,
      style: _textStyle,
      textAlign: TextAlign.left,
    );
  }

  @override
  Widget build(BuildContext context) {
    return _dispatch();
  }
}