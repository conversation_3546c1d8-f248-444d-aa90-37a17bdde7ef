import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';

/**
 * 评论控制栏
 */
class RemarkBar extends StatelessWidget {
  /// 是否点赞
  bool isLike = false;

  /// 点赞数量
  int likeNumber = 0;

  /// 评论点击
  Function()? onRemarkAction;

  /// 点赞点击
  Function()? onThumbUpAction;

  /// 发送消息
  Function(String?)? onSendAction;

  RemarkBar({Key? key, this.isLike = false, this.likeNumber = 0, this.onRemarkAction, this.onThumbUpAction}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    


    String _thumbImage = isLike ? "assets/images/circle/3.png" : "assets/images/circle/6.png";
    Color _thumbTextColor = isLike ? Business.mainColor : Color(0xFF595959);

    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
      child: Column(
        children: [
          // 分隔线条
          Divider(
            height: 1,
            color: Color(0xFFEEEEEE),
          ),
          // 控制栏
          Container(
            padding: EdgeInsets.only(left: 12.w, right: 13.w),
            height: 56.h,
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: onRemarkAction,
                    child: Container(
                      margin: EdgeInsets.only(right: 15.w),
                      padding: EdgeInsets.only(left: 28.w),
                      height: 35.h,
                      decoration: BoxDecoration(
                        color: Color(0xFFFAFAFA),
                        borderRadius:
                        BorderRadius.all(Radius.circular(35.h / 2)),
                      ),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        "写评论...",
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: Color(0xFF595959),
                        ),
                      ),
                    ),
                  ),
                ),
                _actionItemWidget(
                  asset: "assets/images/circle/5.png",
                  text: "评论",
                  textColor: Color(0xFF595959),
                  onTap: onRemarkAction,
                ),
                _actionItemWidget(
                  asset: _thumbImage,
                  text: "$likeNumber",
                  textColor: _thumbTextColor,
                  onTap: onThumbUpAction,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 事件按钮部件
  Widget _actionItemWidget({
    required String asset,
    required String text,
    required Color textColor,
    Function()? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 35.h,
        padding: EdgeInsets.only(left: 13.w, right: 13.w),
        alignment: Alignment.center,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 17.w,
              height: 17.w,
              child: Image.asset(asset),
            ),
            Padding(
              padding: EdgeInsets.only(left: 8.w),
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 11.sp,
                  color: textColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}