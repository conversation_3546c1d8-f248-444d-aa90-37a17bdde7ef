import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';

import '../../models/circle/commentModel.dart';
import '../../utils/date_format.dart';
import '../../utils/global.dart';
import '../../utils/http.dart';

/// 评论记录
class RemarkCell extends StatefulWidget {
  /// 评论记录
  CommentModel data;

  /// 显示追加数据
  bool addToEnable = true;

  /// 点击事件
  Function(CommentModel)? onTap;

  /// 文章主人，长按删除评论
  Function(GlobalKey, CommentModel)? onLongPress;

  /// 评论记录添加用户点击'删除'删除
  Function(CommentModel)? onCommentsDelTap;

  /// 追加评论点击
  Function(CommentModel, CommentModel)? addToOnTap;

  Function(CommentModel) onShowAll;

  Key? key;

  RemarkCell({
    this.key,
    required this.data,
    this.addToEnable = true,
    this.onTap,
    this.onLongPress,
    this.onCommentsDelTap,
    this.addToOnTap,
    required this.onShowAll,
  }) : super(key: key);

  @override
  State<RemarkCell> createState() => _RemarkCellState();
}

class _RemarkCellState extends State<RemarkCell> {
  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    /// 显示评论追加数据
    Widget addToWidget = Container();
    var data = widget.data;
    if (data.children != null && data.children!.length > 0) {
      addToWidget = _addToWidget(context);
    }

    /// 头像url
    String? _portrait;
    if (data.user != null && data.user!.avatar != null) {
      _portrait = kReleaseBaseUrl + data.user!.avatar!;
    }
    return GestureDetector(
      onTap: () {
        print(">>>> remark --- 评论记录");
        if (widget.onTap != null) widget.onTap!(data);
      },
      onLongPress: () {
        print(">>>> remark --- 长按删除评论");
        if (widget.onLongPress != null)
          widget.onLongPress!(widget.key! as GlobalKey, data);
      },
      child: Container(
        padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 20.h),
        color: Colors.white,
        child: Column(
          children: [
            // 数据
            Container(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 头像
                  _portraitWidget(context: context, url: _portrait),
                  // 数据
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.only(left: 10.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 用户信息
                          _userInfoWidget(),
                          // 文本数据
                          _textContentWidget(data.content),
                          // 追加数据
                          addToWidget,
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 分割线
            _divider(),
          ],
        ),
      ),
    );
  }

  /// 头像
  Widget _portraitWidget(
      {required BuildContext context, String? url, Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        child: ClipOval(
          child: SizedBox(
            width: 38.w,
            height: 38.w,
            child: url != null
                ? Image.network(
                    url,
                    fit: BoxFit.cover,
                  )
                : Container(
                    color: Colors.grey,
                  ),
          ),
        ),
      ),
    );
  }

  /// 用户信息
  Widget _userInfoWidget() {
    // 用户名
    String _userName = widget.data.user!.nickName ?? '';
    String _timeText = widget.data != null
        ? "${DateFormatter.formatByMilliseconds(widget.data.createOn.toInt())}"
        : '';
    bool _isLike = false;
    int _likeNumber = 0;
    bool _isOwner = false;
    if (widget.data.user!.id == Global.profile.user!.id) {
      _isOwner = true;
    }

    return SizedBox(
      height: 38.w,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _userName,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF595959),
                  ),
                ),
                Expanded(child: Container()),
                Text(
                  _timeText,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: Color(0xFFB5B5B9),
                  ),
                ),
              ],
            ),
          ),
          // 点赞 | 删除
          Visibility(
            visible: widget.data.createBy == Global.profile.user?.id,
            child: Padding(
              padding: EdgeInsets.only(left: 10.w),
              child: GestureDetector(
                onTap: () {
                  if (widget.onCommentsDelTap != null)
                    widget.onCommentsDelTap!(widget.data);
                },
                child: Container(
                  padding: EdgeInsets.all(5.w),
                  child: Text(
                    "删除",
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: const Color(
                        (0xFF595959),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  /// 文本内容
  Widget _textContentWidget(String? text) {
    return Container(
      padding: EdgeInsets.only(top: 13.h),
      child: Text(
        text ?? '',
        style: TextStyle(fontSize: 14.sp, color: const Color(0xFF212121)),
      ),
    );
  }

  /// 追加数据列表部件
  Widget _addToWidget(context) {
    // 条数
    int _count = widget.data.children!.length;
    // 查看全部回复
    Widget _lookAllWidget = GestureDetector(
      onTap: () {
        widget.onShowAll(widget.data);
        // Navigator.push(context, MaterialPageRoute(builder: (ctx) => ReplyRemarkPage()));
      },
      child: Container(
        margin: EdgeInsets.only(top: 14.h),
        alignment: Alignment.centerLeft,
        child: Text(
          "查看全部回复 $_count条 >",
          style: TextStyle(
            fontSize: 13.sp,
            color: Business.mainColor,
          ),
        ),
      ),
    );

    return Container(
      margin: EdgeInsets.only(top: 13.h),
      padding: EdgeInsets.only(
        left: 12.5.w,
        top: 5.h,
        right: 12.5.w,
        bottom: 13.5.h,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.all(
          Radius.circular(8.w),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 数据列表
          ListView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: !widget.data.showAll
                ? (widget.data.children!.length > 2
                    ? 2
                    : widget.data.children!.length)
                : widget.data.children!.length,
            itemBuilder: (context, index) {
              CommentModel _model = widget.data.children![index];
              return _addToItemBuilder(
                  '${_model.user!.nickName}：${_model.content}', onTap: () {
                if (widget.addToOnTap != null)
                  widget.addToOnTap!(widget.data, _model);
              });
            },
          ),
          // 查看全部
          _count > 2 && !widget.data.showAll ? _lookAllWidget : Container(),
        ],
      ),
    );
  }

  /// 追加数据列表元素部件
  Widget _addToItemBuilder(String? text, {Function()? onTap}) {
    return GestureDetector(
      onTap: () {
        if (onTap != null) onTap();
      },
      child: Container(
        padding: EdgeInsets.only(top: 13.h),
        child: Text(
          text ?? '',
          maxLines: 50,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(fontSize: 13.sp, color: const Color(0xFF595959)),
        ),
      ),
    );
  }

  /// 分割线
  Widget _divider() {
    return Padding(
      padding: EdgeInsets.only(top: 22.h),
      child: const Divider(
        height: 1,
        color: Color(0xFFF5F5F5),
      ),
    );
  }
}
