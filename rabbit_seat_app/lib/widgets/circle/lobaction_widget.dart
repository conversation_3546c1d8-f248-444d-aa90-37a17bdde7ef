import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 位置数据
class LocationWidget extends StatelessWidget {
  String? name;
  String? address;
  Function()? onTap;
  LocationWidget({Key? key, this.name, this.address}) : super(key: key);

  @override
  Widget build(BuildContext context) {



    return InkWell(onTap: onTap, child: Container(
      child: Container(
        height: 40.h,
        alignment: Alignment.centerLeft,
        child: Container(
          padding: EdgeInsets.only(left: 5.w, right: 8.5.h),
          height: 22,
          decoration: BoxDecoration(
            color: Color(0xFFF1F1F1),
            borderRadius: BorderRadius.all(Radius.circular(11.h)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(width: 15.w, height: 15.w, child: Image.asset('assets/images/circle/2.png', fit: BoxFit.fill,),),
              SizedBox(width: 3.5.w),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 0.45.sw),
                child: Text(
                  name ?? '',
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(fontSize: 10.sp, color: Color(0xFF595959)),
                ),
              ),
              SizedBox(
                width: 6.w,
              ),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 0.3.sw),
                child: Text(
                  address ?? "",
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(fontSize: 10.sp, color: Color(0xFFA5A5A5)),
                ),
              ),
            ],
          ),
        ),
      ),
    ),);
  }
}