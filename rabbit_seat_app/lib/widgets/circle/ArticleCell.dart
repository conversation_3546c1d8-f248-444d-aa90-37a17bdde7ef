import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/utils/date_format.dart';
import 'package:video_player/video_player.dart';

import '../../models/circle/circleModel.dart';
import '../../models/circle/fileModel.dart';
import '../../models/circle/holderModel.dart';
import '../../utils/http.dart';
import '../../views/circle/ImgPreview.dart';
import '../../views/common/video_play_view.dart';
import 'flex_text.dart';
import 'lobaction_widget.dart';

/**
 * 文章列表数据
 */
class ArticleCell extends StatelessWidget {
  /// 数据模型
  final CircleModel data;

  /// 点击
  final Function()? onTap;

  /// 头像点击
  final Function()? onPortraitTap;

  /// 点赞
  final Function()? onThumbUpTap;

  ArticleCell({
    Key? key,
    required this.data,
    this.onTap,
    this.onPortraitTap,
    this.onThumbUpTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {


    Widget _pixWidget = Container();
    List<FileModel> _pixDatas = data.getPixDatas();
    if (_pixDatas.length > 0) {
      if (_pixDatas.length == 1) {
        _pixWidget = _bigImageWidget(context, _pixDatas.first);
      } else {
        _pixWidget = _mediaWidget(context, _pixDatas);
      }
    }

    // 显示定位
    Widget _locationWidget = Container();
    if (data.locationInfo != null) {
      _locationWidget = LocationWidget(
        name: data.locationInfo,
      );
    }

    return Container(
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.only(left: 15, top: 15, right: 15),
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户信息
          _userInfoWidget(context,
              info: data.createBy,
              time: data.createOn,
              portraitTap: onPortraitTap),
          SizedBox(height: 16),
          // 文本内容
          data.content != null ? FlexText(text: "${data.content}") : SizedBox(),
          SizedBox(height: 15),
          // 图片
          _pixWidget,
          // 定位
          _locationWidget,
          SizedBox(height: 3),
          // 点赞控制
          _bottomControlWidget(context),
        ],
      ),
    );
  }

  // 更多按钮key
  final GlobalKey _moreItemKey = GlobalKey();

  /// 用户信息
  Widget _userInfoWidget(
    BuildContext context, {
    HolderModel? info,
    num? time,
    Function()? portraitTap,
    Function(GlobalKey)? moreTap,
  }) {
    // 头像
    Widget _portrait = Container(color: Colors.grey);
    // 姓名
    String _name = '';
    // 时间
    String _time = '';

    // 校验数据
    if (info != null) {
      /// 放开注释
      if (info.avatar != null) {
        if (info.avatar!.startsWith('http')) {
          _portrait = Image.network(
            '${info.avatar}',
            fit: BoxFit.cover,
          );
        } else {
          _portrait = Image.network(
            '$kReleaseBaseUrl${info.avatar}',
            fit: BoxFit.cover,
          );
        }
      }
      // 姓名
      _name = info.nickName ?? '';
    }
    if (time != null) {
      // 时间
      _time = DateFormatter.formatByMilliseconds(time.toInt());
    }

    // 返回UI
    return Row(
      children: [
        Expanded(
          child: Container(
            child: Row(
              children: [
                // 头像
                Container(
                  width: 38,
                  child: GestureDetector(
                    onTap: portraitTap,
                    child: SizedBox(
                      width: 38,
                      height: 38,
                      child: ClipOval(
                        child: _portrait,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: 10,
                  height: 1,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 用户名
                      Text(
                        _name,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF212121),
                        ),
                      ),
                      SizedBox(height: 5),
                      // 创建时间
                      Text(
                        _time,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 11,
                          color: Color(0xFFB5B5B9),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        moreTap != null
            ? InkWell(
                onTap: () {
                  moreTap(_moreItemKey);
                },
                child: SizedBox(
                  key: _moreItemKey,
                  width: 30,
                  height: 30,
                  child: Icon(
                    Icons.more_horiz,
                    color: Color(0xFFB5B5B9),
                  ),
                ),
              )
            : SizedBox(width: 1, height: 1),
      ],
    );
  }

  /// 单张大图片
  Widget _bigImageWidget(BuildContext context, FileModel model) {
    return AspectRatio(
      aspectRatio: 1.778,
      child: GestureDetector(
        onTap: () {
          print("大张图片点击");
          Navigator.push(context,
              MaterialPageRoute(builder: (_) => ImgPreview(items: [model])));
        },
        child: Container(
          decoration: BoxDecoration(
            color: Color(0x4F000000),
            border: Border.all(color: Color(0xFFF5F5F5), width: 1),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            child: model.thumbnailUrl != null
                ? CachedNetworkImage(
                    fit: BoxFit.cover,
                    imageUrl: model.thumbnailUrl!,
                  )
                : Container(color: Colors.grey),
          ),
        ),
      ),
    );
  }

  /// 媒体
  Widget _mediaWidget(BuildContext context, List<FileModel> datas) {
    // 余量
    int _count = datas.length - 3;
    return AspectRatio(
      aspectRatio: 3.15,
      child: Stack(
        children: [
          // 图片影像
          Container(
            child: GridView.builder(
                shrinkWrap: true,
                itemCount: datas.length > 3 ? 3 : datas.length,
                scrollDirection: Axis.horizontal,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 1,
                  mainAxisSpacing: 10.0,
                  crossAxisSpacing: 10.0,
                ),
                itemBuilder: (context, index) {
                  FileModel model = datas[index];
                  if (model.thumbnailUrl == null) {
                    return Container(
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                    );
                  }
                  return GestureDetector(
                    onTap: () {
                      print(">>> 媒体点击   ----------- ");
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) {
                          return model.type == 1
                              ? VideoPlayView(url: model.fileUrl)
                              : ImgPreview(
                            items:
                            data.photoFiles ?? data.photos ?? [],
                            index: index,
                          );
                        }),
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black, //Color(0x4F000000),
                        border:
                        Border.all(color: Color(0xFFF5F5F5), width: 1),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        child: Stack(
                          children: [
                            CachedNetworkImage(
                              fit: BoxFit.cover,
                              imageUrl: model.thumbnailUrl!,
                            ),
                            // 播放按钮
                            model.type == 1
                                ? Align(
                              child: Icon(
                                Icons.play_circle_outline,
                                color: Colors.white,
                                size: 40,
                              ),
                            )
                                : SizedBox(),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
          ),
          // 容量
          _count > 0
              ? Positioned(
                  bottom: 11,
                  right: 6,
                  child: GestureDetector(
                    onTap: () {
                      print("更多图片");
                    },
                    child: Container(
                      height: 20,
                      decoration: BoxDecoration(
                        color: Color(0x80000000),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        border: Border.all(color: Color(0xFFF5F5F5), width: 1),
                      ),
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(left: 7, right: 7),
                      child: Text(
                        "+$_count",
                        style: TextStyle(fontSize: 14, color: Colors.white),
                      ),
                    ),
                  ),
                )
              : Container(),
        ],
      ),
    );
    // return
  }

  /// 底部控制栏
  Widget _bottomControlWidget(BuildContext context) {
    return Container(
      height: 51,
      padding: EdgeInsets.only(bottom: 3),
      child: Row(
        children: [
          // 阅览数
          Expanded(
            child: _buttonWidget(
              icon: Image.asset(
                'assets/images/circle/4.png',
                fit: BoxFit.fill,
              ),
              text: "${data.viewCount}",
              alignment: MainAxisAlignment.start,
            ),
          ),
          SizedBox(
            width: 5,
          ),
          // 评论
          Expanded(
            child: _buttonWidget(
              icon: Image.asset(
                'assets/images/circle/5.png',
                fit: BoxFit.fill,
              ),
              text: "${data.commentCount}",
              alignment: MainAxisAlignment.center,
            ),
          ),
          SizedBox(
            width: 5,
          ),
          // 点赞
          Expanded(
            child: _buttonWidget(
              icon: Image.asset(
                data.isUserLike
                    ? 'assets/images/circle/3.png'
                    : 'assets/images/circle/6.png',
                fit: BoxFit.fill,
              ),
              text: "${data.likeCount}",
              alignment: MainAxisAlignment.end,
              onTap: onThumbUpTap,
            ),
          ),
        ],
      ),
    );
  }

  /// 点赞控制栏按钮组件
  Widget _buttonWidget({
    required Widget icon,
    required String text,
    Function()? onTap,
    Color? color,
    TextStyle? textStyle,
    MainAxisAlignment alignment = MainAxisAlignment.start,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        child: Row(
          mainAxisAlignment: alignment,
          children: [
            SizedBox(
              width: 22,
              height: 22,
              child: icon,
            ),
            SizedBox(
              width: 3,
            ),
            Container(
              constraints: BoxConstraints(maxWidth: 0.22.sw),
              child: DefaultTextStyle(
                style: TextStyle(
                  fontSize: 11,
                  color: Color(0xFF595959),
                ),
                child: Text(
                  text,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: textStyle,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
