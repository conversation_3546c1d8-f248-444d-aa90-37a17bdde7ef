import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/circle/fileModel.dart';
import 'package:rabbit_seat_app/models/circle/index.dart';
import 'package:rabbit_seat_app/models/help/pixItem.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/views/circle/ImgPreview.dart';
import 'package:rabbit_seat_app/views/circle/review_view.dart';
import 'package:rabbit_seat_app/views/common/video_play_view.dart';
import 'package:rabbit_seat_app/views/my/user_articles_view.dart';
import 'package:rabbit_seat_app/widgets/intercat/header.dart';
import 'package:video_player/video_player.dart';

import '../../models/circle/circleModel.dart';
import '../../models/circle/photoModel.dart';
import '../../utils/http.dart';
import '../../utils/signal.dart';
import 'flex_text.dart';
import 'lobaction_widget.dart';

class CircleCell extends StatelessWidget {
  /// 数据模型
  CircleModel data;

  /// 显示控制
  bool showControl = true;

  /// 头像点击
  Function()? onPortraitTap;

  CircleCell(
      {Key? key,
        required this.data,
        this.showControl = true,
        this.onPortraitTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {


    return _buildWidget(context);
  }

  /// 构建UI
  Widget _buildWidget(BuildContext context) {
    Widget _pixWidget = Container();
    List<FileModel> _pixDatas = data.getPixDatas();
    if (_pixDatas.length > 0) {
      if (_pixDatas.length == 1) {
        _pixWidget = _bigImageWidget(context, _pixDatas.first);
      } else {
        _pixWidget = _mediaWidget(context, _pixDatas);
      }
    }

    // 显示定位
    Widget _locationWidget = Container();
    if (data.locationInfo != null) {
      _locationWidget = LocationWidget(
        name: data.locationInfo,
      );
    }

    return Container(
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.only(left: 15, top: 15, right: 15),
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户信息
          CHeaderWidget(
            data: data.createBy,
            createTime: data.createOn.toInt(),
            onHeaderTap: onPortraitTap,
          ),
          // UserWidget(
          //   data: data.createBy,
          // ),
          SizedBox(height: 16),
          // 文本内容
          data.content != null ? FlexText(text: "${data.content}") : SizedBox(),
          SizedBox(height: 15),
          // 图片
          _pixWidget,
          // 定位
          _locationWidget,
          SizedBox(height: 3),
          // 点赞控制
          // 点赞控制
          showControl ? _bottomControlWidget(context) : Container(),
        ],
      ),
    );
  }

  /// 单张大图片
  Widget _bigImageWidget(BuildContext context, FileModel model) {
    return AspectRatio(
      aspectRatio: 1.778,
      child: GestureDetector(
        onTap: () {
          print("大张图片点击");
          if (model.type == 0) {
            Navigator.push(context,
                MaterialPageRoute(builder: (_) => ImgPreview(items: [model])));
          } else {
            Navigator.push(context, MaterialPageRoute(builder: (_) => VideoPlayView(url: model.fileUrl)));
          }
        },
        child: Container(
          decoration: BoxDecoration(
            color: Color(0x4F000000),
            border: Border.all(color: Color(0xFFF5F5F5), width: 1),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            child: model.thumbnailUrl != null
                ? Stack(
              children: [
                Positioned.fill(child: CachedNetworkImage(
                  fit: BoxFit.cover,
                  imageUrl: model.thumbnailUrl!,
                ),),
                // 播放按钮
                model.type == 1
                    ? Align(
                  child: Icon(
                    Icons.play_circle_outline,
                    color: Colors.white,
                    size: 40,
                  ),
                )
                    : SizedBox(),
              ],
            )
                : Container(color: Colors.grey),
          ),
        ),
      ),
    );
  }

  /// 媒体
  Widget _mediaWidget(BuildContext context, List<FileModel> datas) {
    // 余量
    int _count = datas.length - 3;
    return AspectRatio(
      aspectRatio: 3.15,
      child: Stack(
        children: [
          // 图片影像
          Container(
            child: GridView.builder(
                shrinkWrap: true,
                itemCount: datas.length > 3 ? 3 : datas.length,
                scrollDirection: Axis.horizontal,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 1,
                  mainAxisSpacing: 10.0,
                  crossAxisSpacing: 10.0,
                ),
                itemBuilder: (context, index) {
                  FileModel model = datas[index];
                  if (model.thumbnailUrl == null) {
                    return Container(
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                    );
                  }
                  return GestureDetector(
                    onTap: () {
                      print(">>> 媒体点击   ----------- ");
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) {
                          return model.type == 1
                              ? VideoPlayView(url: model.fileUrl)
                              : ImgPreview(
                            items:
                            data.photoFiles ?? data.photos ?? [],
                            index: index,
                          );
                        }),
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black, //Color(0x4F000000),
                        border:
                        Border.all(color: Color(0xFFF5F5F5), width: 1),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        child: Stack(
                          children: [
                            Positioned.fill(child: CachedNetworkImage(
                              fit: BoxFit.cover,
                              imageUrl: model.thumbnailUrl!,
                            ),),
                            // 播放按钮
                            model.type == 1
                                ? Align(
                              child: Icon(
                                Icons.play_circle_outline,
                                color: Colors.white,
                                size: 40,
                              ),
                            )
                                : SizedBox(),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
          ),
          // 容量
          _count > 0
              ? Positioned(
            bottom: 11,
            right: 6,
            child: GestureDetector(
              onTap: () {
                print("更多图片");
              },
              child: Container(
                height: 20,
                decoration: BoxDecoration(
                  color: Color(0x80000000),
                  borderRadius: BorderRadius.all(Radius.circular(10)),
                  border: Border.all(color: Color(0xFFF5F5F5), width: 1),
                ),
                alignment: Alignment.center,
                padding: EdgeInsets.only(left: 7, right: 7),
                child: Text(
                  "+$_count",
                  style: TextStyle(fontSize: 14, color: Colors.white),
                ),
              ),
            ),
          )
              : Container(),
        ],
      ),
    );
    // return
  }

  /// 底部控制栏
  Widget _bottomControlWidget(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {



      return Container(
        height: 51,
        padding: EdgeInsets.only(bottom: 3),
        child: Row(
          children: [
            // 阅览数
            Expanded(
              child: _buttonWidget(
                icon: Image.asset(
                  'assets/images/circle/4.png',
                  fit: BoxFit.fill,
                ),
                text: "${data.viewCount}",
                alignment: MainAxisAlignment.start,
              ),
            ),
            SizedBox(
              width: 5,
            ),
            // 评论
            Expanded(
              child: _buttonWidget(
                icon: Image.asset(
                  'assets/images/circle/5.png',
                  fit: BoxFit.fill,
                ),
                text: "${data.commentCount}",
                alignment: MainAxisAlignment.center,
              ),
            ),
            SizedBox(
              width: 5,
            ),
            // 点赞
            Expanded(
              child: _buttonWidget(
                icon: Image.asset(
                  data.isUserLike
                      ? 'assets/images/circle/3.png'
                      : 'assets/images/circle/6.png',
                  fit: BoxFit.fill,
                ),
                text: "${data.likeCount}",
                alignment: MainAxisAlignment.end,
                onTap: () {
                  print("点赞");
                  if (data.isUserLike == true) {
                    _cancelThumbUp(data, context, setState);
                  } else {
                    _submitThumbsUp(data, context, setState);
                  }
                },
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 点赞控制栏按钮组件
  Widget _buttonWidget({
    required Widget icon,
    required String text,
    Function()? onTap,
    Color? color,
    TextStyle? textStyle,
    MainAxisAlignment alignment = MainAxisAlignment.start,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        child: Row(
          mainAxisAlignment: alignment,
          children: [
            SizedBox(
              width: 22,
              height: 22,
              child: icon,
            ),
            SizedBox(
              width: 3,
            ),
            Container(
              constraints: BoxConstraints(maxWidth: 0.22.sw),
              child: DefaultTextStyle(
                style: TextStyle(
                  fontSize: 11,
                  color: Color(0xFF595959),
                ),
                child: Text(
                  text,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: textStyle,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 点赞
  void _submitThumbsUp(
      CircleModel model, BuildContext context, setState) async {
    try {
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/like/${model.id}');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        // model.likeCount += 1;
        // model.isUserLike = true;
        setState(() {
          model.likeCount += 1;
          model.isUserLike = true;
        });
        // KitSignal().send('ThumbUp', {"id": model.id, "state": true});
        // context.read<CircleProvider>().notifyListeners();
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
    }
  }

  /// 取消点赞
  void _cancelThumbUp(CircleModel model, BuildContext context, setState) async {
    try {
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/unlike/${model.id}');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        setState(() {
          model.likeCount -= 1;
          model.isUserLike = false;
        });
        // model.likeCount -= 1;
        // model.isUserLike = false;
        // KitSignal().send('ThumbUp', {"id": model.id, "state": false});
        // context.read<CircleProvider>().notifyListeners();
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
    }
  }
}
