import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_swiper_tv/flutter_swiper.dart';
import 'package:rabbit_seat_app/models/circle/banner.dart';
import 'package:rabbit_seat_app/models/error.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../utils/http.dart';

/**
 * 横幅控件
 */
class BannerWidget extends StatelessWidget {
  final List<BannerModel> datas;
  Color backgroundColor;
  EdgeInsetsGeometry? padding;

  BannerWidget(
      {Key? key,
      required this.datas,
      this.backgroundColor = Colors.white,
      this.padding})
      : super(key: key);

//
//   @override
//   Widget build(BuildContext context) {
//     return Container();
//   }
// }

// class BannerWidget extends StatefulWidget {
//   final List<BannerModel> datas;
//
//   const BannerWidget({Key? key, required this.datas}) : super(key: key);
//
//   @override
//   State<BannerWidget> createState() => _BannerWidgetState();
// }
//
// class _BannerWidgetState extends State<BannerWidget> {
//
//
//   @override
//   void initState() {
//     super.initState();
//
//     // 加载横幅数据
//     _loadBannerDatas();
//   }

  @override
  Widget build(BuildContext context) {
    Widget _swiper = Container(
      decoration: BoxDecoration(
          color: Colors.grey,
          borderRadius: BorderRadius.all(Radius.circular(12))),
    );
    if (datas.length > 0) {
      _swiper = Swiper(
        itemBuilder: (BuildContext context, int index) {
          BannerModel _model = datas[index];
          String? _url;
          if (_model.file != null) {
            if (_model.file!.fileUrl != null) {
              if (_model.file!.fileUrl!.startsWith('https://') ||
                  _model.file!.fileUrl!.startsWith('http://')) {
                _url = _model.file!.fileUrl!;
              } else {
                _url = kReleaseBaseUrl + _model.file!.fileUrl!;
              }
            }
          } else if (_model.fileUrl != null) {
            if (_model.fileUrl!.startsWith('https://') ||
                _model.fileUrl!.startsWith('http://')) {
              _url = _model.fileUrl!;
            } else {
              _url = kReleaseBaseUrl + _model.fileUrl!;
            }
          }

          // String _url = kReleaseBaseUrl + _model.file!.fileUrl!;
          return InkWell(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _url != null
                    ? Image.network(
                        _url, //"http://via.placeholder.com/350x150",
                        fit: BoxFit.fill,
                      )
                    : Container(
                        color: Colors.grey,
                      ),
              ),
              onTap: () async {
                if (_model.linkUrl != null) {
                  await launchUrl(Uri.parse(_model.linkUrl!));
                }
              });
        },
        itemCount: datas.length,
        pagination: SwiperPagination(),
        autoplay: true,
        autoplayDelay: 5000,
      );
    }
    return Container(
      color: backgroundColor,
      padding: padding == null
          ? EdgeInsets.only(top: 10, left: 15, right: 15, bottom: 15)
          : padding,
      child: AspectRatio(
        aspectRatio: 1.926,
        child: _swiper,
      ),
    );
  }
}
