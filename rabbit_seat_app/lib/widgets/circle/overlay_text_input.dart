import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';


/**
 * 叠加文本输入
 */
class OverlayTextInput {
  /// 叠加入口
  static OverlayEntry? _overlayEntry;

  /// 显示输入框
  static void show(BuildContext context,
      {Function(String)? onSubmitted, String? hintText}) {
    // 关系
    close();
    // 初始化
    _overlayEntry = OverlayEntry(
      builder: (context) {
        return _TextInputWidget(onSubmitted: onSubmitted, hintText: hintText,);
      },
    );
    // 插入层
    Overlay.of(context)?.insert(_overlayEntry!);
  }

  /// 关闭输入框
  static void close() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
  }
}


/**
 * 输入框部件
 */
class _TextInputWidget extends StatefulWidget {
  /// 回调方法
  final Function(String)? onSubmitted;
  /// 提示文本
  final String? hintText;

  _TextInputWidget({Key? key, this.onSubmitted, this.hintText}) : super(key: key);

  @override
  State<_TextInputWidget> createState() => _TextInputWidgetState();
}

class _TextInputWidgetState extends State<_TextInputWidget> {
  /// 输入控制器
  final TextEditingController _controller = TextEditingController();

  /// 焦点
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance?.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    return Stack(
      children: [
        /// 背景点击退出
        Positioned.fill(
          child: GestureDetector(
            onTap: () {
              OverlayTextInput.close();
            },
            child: Container(
              color: Colors.black.withOpacity(0.3),
            ),
          ),
        ),

        /// 输入框
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: AnimatedPadding(
            padding: EdgeInsets.only(
              // 下面这一行是重点
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            duration: Duration.zero,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: OverlayTextInput.close,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    height: 60.h,
                    padding:
                    EdgeInsets.only(left: 12.w, top: 12.h, bottom: 12.h, right: 15.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(18.h),
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: Row(
                        children: [
                          /// 文本输入
                          _textFieldWidget(),

                          /// 发送按钮
                          GestureDetector(
                            onTap: _onSubmitted,
                            child: Container(
                              height: 35.h,
                              alignment: Alignment.center,
                              margin: EdgeInsets.only(left: 10.w),
                              padding: EdgeInsets.only(left: 15.w, right: 15.w),
                              decoration: BoxDecoration(
                                color: Color(0xFFFFF1E5),
                                borderRadius: BorderRadius.all(
                                  Radius.circular(35.h / 2),
                                ),
                              ),
                              child: Text(
                                "发送",
                                style: TextStyle(
                                  fontSize: 15.sp,
                                  fontWeight: FontWeight.w500,
                                  color: Business.mainColor,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 软键盘 发送 点击
  Future<void> _onSubmitted() async {
    final text = _controller.text;
    if (widget.onSubmitted != null) widget.onSubmitted!(text);
    OverlayTextInput.close();
  }

  /// 文本输入框
  Widget _textFieldWidget() {
    return Expanded(
      child: Container(
        height: 35,
        alignment: Alignment.center,
        padding: EdgeInsets.only(left: 19, right: 19),
        decoration: BoxDecoration(
          color: Color(0xFFFAFAFA),
          borderRadius: BorderRadius.all(
            Radius.circular(35 / 2),
          ),
        ),
        child: TextField(
          focusNode: _focusNode,
          controller: _controller,
          cursorColor: Business.mainColor,
          cursorWidth: 1,
          autofocus: true,
          style: TextStyle(
            fontSize: 15.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
          maxLines: 1,
          textInputAction: TextInputAction.send,
          onSubmitted: (value) {
            _onSubmitted();
          },
          decoration: InputDecoration(
            isDense: true,
            hintText: widget.hintText??"请输入",
            hintStyle: TextStyle(
              fontSize: 15.sp,
              color: Color(0xFFA5A5A5),
            ),
            border: InputBorder.none,
          ),
        ),
      ),
    );
  }
}
