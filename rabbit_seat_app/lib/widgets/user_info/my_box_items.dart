import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/utils/http.dart';

import '../../utils/utils.dart';


class MyBoxItem {
  String title;
  String? subTitle;
  String? subImage;
  bool isEnd;
  void Function()? onTap;
  MyBoxItem(this.title, this.subTitle, this.onTap,
      {this.isEnd = false, this.subImage = ""});
}

class MyBoxItems extends StatefulWidget {
  MyBoxItems({Key? key, required this.items, this.type = 0}) : super(key: key);

  List<MyBoxItem> items = [];

  /// 0-默认 1-编辑
  final num type;

  @override
  State<StatefulWidget> createState() => _MyBoxItemsState();
}

class _MyBoxItemsState extends State<MyBoxItems> {
  @override
  Widget build(BuildContext context) {
    return Container(
        width: 330.w,
        margin: EdgeInsets.only(top: 12.h),
        padding: EdgeInsets.only(
          bottom: 15.h,
          left: 20.w,
          right: 20.w,
        ),
        child: Column(
          children: widget.items.map((e) {
            return InkWell(
              onTap: () {
                if (e.onTap != null) {
                  e.onTap!();
                }
              },
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.only(
                        left: 20.w, top: 15.h, bottom: e.isEnd ? 0 : 15.h),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          child: Text(e.title),
                        ),
                        Container(
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.only(right: 10.w),
                                  child: Center(
                                      child: e.subTitle != null
                                          ? Util.getText(e.subTitle,
                                              color: const Color(0xFF666666))
                                          : SizedBox(
                                              width: 46.w,
                                              height: 46.w,
                                              child: ClipOval(
                                                child: (e.subImage != null &&
                                                        e.subImage != ""
                                                    ? Image.network(
                                                        kReleaseBaseUrl + e.subImage!,
                                                        fit: BoxFit.fill,
                                                      )
                                                    : const Image(
                                                        image: AssetImage(
                                                            'assets/images/logo.png'),
                                                        fit: BoxFit.fill,
                                                      )),
                                              ),
                                            )),
                                ),
                                Image(
                                    image: const AssetImage(
                                        'assets/images/my/1.png'),
                                    width: 8.w)
                              ],
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  e.isEnd
                      ? const SizedBox(
                          height: 0,
                        )
                      : Divider(
                          height: 1.h,
                          color: Color.fromARGB(255, 217, 215, 215),
                        ),
                ],
              ),
            );
          }).toList(),
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1), // 阴影的颜色
              offset: const Offset(10, 20), // 阴影与容器的距离
              blurRadius: 60.0, // 高斯的标准偏差与盒子的形状卷积。
              spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
            ),
          ],
        ));
  }
}
