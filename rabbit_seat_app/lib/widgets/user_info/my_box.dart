import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../utils/utils.dart';


class MyBox extends StatefulWidget {
  MyBox(
      {Key? key,
      this.onTap,
      this.top,
      required this.title,
      this.subTitle,
      this.type = 0})
      : super(key: key);

  final void Function()? onTap;
  final double? top;
  final String title;
  String? subTitle;

  /// 0-默认 1-编辑
  final num type;

  @override
  State<StatefulWidget> createState() => _MyBoxState();
}

class _MyBoxState extends State<MyBox> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (widget.onTap != null) {
          widget.onTap!();
        }
      },
      child: Container(
        margin: EdgeInsets.only(top: widget.top != null ? widget.top! : 0.0),
        width: 330.w,
        height: 46.h,
        child: Container(
          margin: EdgeInsets.only(left: 20.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: EdgeInsets.only(left: 17.w),
                child: Text(widget.title),
              ),
              Container(
                padding: EdgeInsets.only(right: 20.w),
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.only(right: 10.w),
                        child: Center(
                          child: Util.getText(widget.subTitle,
                              color: const Color(0xFF666666)),
                        ),
                      ),
                      Image(
                          image: const AssetImage('assets/images/my/1.png'),
                          width: 8.w)
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1), // 阴影的颜色
              offset: const Offset(10, 20), // 阴影与容器的距离
              blurRadius: 60.0, // 高斯的标准偏差与盒子的形状卷积。
              spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
            ),
          ],
        ),
      ),
    );
  }
}
