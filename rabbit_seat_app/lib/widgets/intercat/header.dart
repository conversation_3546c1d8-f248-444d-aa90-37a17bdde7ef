import 'package:flutter/material.dart';
import 'package:rabbit_seat_app/models/circle/holderModel.dart';
import 'package:rabbit_seat_app/utils/global.dart';
import 'package:rabbit_seat_app/widgets/intercat/cut_widget.dart';

import '../../utils/date_format.dart';
import '../../utils/http.dart';
import '../../views/my/user_articles_view.dart';

/**
 * 文章记录，头部用户信息
 */
class CHeaderWidget extends StatelessWidget {
  final Key? tapKey;
  final HolderModel? data;
  late int createTime;
  final Function(GlobalKey?)? onMoreTap;
  final Function()? onHeaderTap;

  CHeaderWidget(
      {Key? key,
        this.tapKey,
        this.data,
        this.createTime = 0,
        this.onMoreTap,
        this.onHeaderTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 头像
    Widget _portrait = Container(color: Colors.grey);

    /// 放开注释
    if (data != null && data!.avatar != null) {
      // print(">>>>> 头像：$kReleaseBaseUrl${data!.avatar}");
      _portrait = Image.network(
        '$kReleaseBaseUrl${data!.avatar}',
        fit: BoxFit.cover,
      );
      // _portrait = CachedNetworkImage(
      //   fit: BoxFit.cover,
      //   imageUrl: '$kReleaseBaseUrl${data!.avatar}',
      //   placeholder: (context, url) =>
      //       CircularProgressIndicator(),
      //   errorWidget: (context, url, error) => Icon(Icons.error),
      // );
    }
    // 姓名
    // String _name = data != null ? "${data!.nickName}" : '';

    String _nickName = '两只兔子';
    if (data != null) {
      if (data!.nickName != null && data!.nickName!.isNotEmpty) {
        _nickName = data!.nickName!;
      } else {
        if (data!.id != null &&
            data!.id!.isNotEmpty &&
            data!.id!.length >= 6) {
          _nickName = '两只兔子' + data!.id!.substring((data!.id!.length - 6));
        }
      }
    }

    // 时间
    String _time =
    data != null ? "${DateFormatter.formatByMilliseconds(createTime)}" : '';

    if (onMoreTap != null && data!.id == Global.profile.user!.id) {
      return Row(
        children: [
          Expanded(
              child: CutContainer(
                leading: GestureDetector(
                  onTap: onHeaderTap,
                  // onTap: () {
                  //   print(">>>>>>>>>");
                  //   if (data != null && data!.id != null) {
                  //     Navigator.push(context, MaterialPageRoute(builder: (_) =>
                  //           UserArticlesView(userId: data!.id!)));
                  //   }
                  // },
                  child: SizedBox(
                    width: 38,
                    height: 38,
                    child: ClipOval(
                      child: _portrait,
                    ),
                  ),
                ),
                width: 38,
                space: 10,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _nickName,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF212121),
                      ),
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    Text(
                      _time,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 11,
                        color: Color(0xFFB5B5B9),
                      ),
                    ),
                  ],
                ),
              )),
          InkWell(
            onTap: () {
              onMoreTap!(tapKey as GlobalKey);
            },
            child: SizedBox(
                key: tapKey,
                width: 30,
                height: 30,
                child: Icon(
                  Icons.more_horiz,
                  color: Color(0xFFB5B5B9),
                )),
          ),
        ],
      );
    }
    else {
      return CutContainer(
        leading: GestureDetector(
          onTap: onHeaderTap,
          // onTap: () {
          //   print(">>>>>>>>>");
          //   if (data != null && data!.id != null) {
          //     Navigator.push(
          //         context,
          //         MaterialPageRoute(
          //             builder: (_) => UserArticlesView(userId: data!.id!)));
          //   }
          // },
          child: SizedBox(
            width: 38,
            height: 38,
            child: ClipOval(
              child: _portrait,
            ),
          ),
        ),
        width: 38,
        space: 10,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _nickName,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: Color(0xFF212121),
              ),
            ),
            SizedBox(
              height: 5,
            ),
            Text(
              _time,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 11,
                color: Color(0xFFB5B5B9),
              ),
            ),
          ],
        ),
      );
    }
  }
}
