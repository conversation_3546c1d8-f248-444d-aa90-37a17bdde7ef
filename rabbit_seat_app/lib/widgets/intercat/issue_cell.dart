// import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/circle/fileModel.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/utils/ui.dart';
import 'package:rabbit_seat_app/views/circle/ImgPreview.dart';
import 'package:rabbit_seat_app/views/common/video_play_view.dart';
import 'package:rabbit_seat_app/widgets/dialog/overlay_utils.dart';
import 'package:rabbit_seat_app/widgets/intercat/header.dart';

import '../../models/circle/circleModel.dart';
import '../../models/circle/photoModel.dart';
import '../../utils/http.dart';
import '../circle/flex_text.dart';
import '../circle/lobaction_widget.dart';

class IssueCell extends StatelessWidget {
  /// 数据模型
  CircleModel data;

  /// 显示控制
  bool showControl = true;

  /// 删除评论
  Function()? onDeleteArticles;

  /// 开启或不关闭评论
  Function()? onOnOrOffComments;

  /// 点赞
  Function()? onThumbUp;

  IssueCell({
    Key? key,
    required this.data,
    this.showControl = true,
    this.onDeleteArticles,
    this.onOnOrOffComments,
    this.onThumbUp,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {


    Widget _pixWidget = Container();
    List<FileModel> _pixDatas = data.getPixDatas();
    if (_pixDatas.length > 0) {
      if (_pixDatas.length == 1) {
        _pixWidget = UI.bigImageWidget(context, _pixDatas.first);
      } else {
        _pixWidget = _mediaWidget(context, _pixDatas);
      }
    }
    // if (index == 0) {
    //   _pixWidget = _bigImageWidget();
    // }
    // else if (index == 1) {
    //   _pixWidget = _videoWidget();
    // } else if (index == 2) {
    //   _pixWidget = _imagesWidget();
    // }

    // 显示定位
    Widget _locationWidget = Container();
    if (data.locationInfo != null) {
      _locationWidget = LocationWidget(
        name: data.locationInfo,
      );
    }

    return Container(
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.only(left: 15, top: 15, right: 15),
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户信息
          CHeaderWidget(
            data: data.createBy,
            createTime: data.createOn,
            tapKey: GlobalKey(),
            onMoreTap: (key) {
              OverlayUtils.showMenuView(
                  context: context,
                  key: key!,
                  size: Size(90, 81),
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () {
                          OverlayUtils.close();
                          if (onDeleteArticles != null) onDeleteArticles!();
                        },
                        child: Container(
                          height: 40,
                          alignment: Alignment.center,
                          child: Text(
                            "删除",
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF000000),
                            ),
                          ),
                        ),
                      ),
                      Divider(
                        height: 1,
                        color: Color(0xFFEEEEEE),
                      ),
                      InkWell(
                        onTap: () {
                          OverlayUtils.close();
                          print("关闭平路");
                          if (onOnOrOffComments != null) onOnOrOffComments!();
                          // _openOrCloseComments();
                        },
                        child: Container(
                          height: 40,
                          alignment: Alignment.center,
                          child: Text(
                            data.canComment == true ? '关闭评论' : "开启评论",
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF000000),
                            ),
                          ),
                        ),
                      )
                    ],
                  ));
            },
          ),
          // UserWidget(
          //   data: data.createBy,
          // ),
          SizedBox(height: 16),
          // 文本内容
          FlexText(text: "${data.content}"),
          SizedBox(height: 15),
          // 图片
          _pixWidget,
          // 定位
          _locationWidget,
          SizedBox(height: 3),
          // 点赞控制
          // 点赞控制
          showControl ? _bottomControlWidget(context) : Container(),
        ],
      ),
    );
  }

  /// 媒体
  Widget _mediaWidget(BuildContext context, List<FileModel> datas) {
    // 余量
    int _count = datas.length - 3;
    return AspectRatio(
      aspectRatio: 3.15,
      child: Stack(
        children: [
          // 图片影像
          Container(
            child: GridView.builder(
                shrinkWrap: true,
                itemCount: datas.length > 3 ? 3 : datas.length,
                scrollDirection: Axis.horizontal,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 1,
                  mainAxisSpacing: 10.0,
                  crossAxisSpacing: 10.0,
                ),
                itemBuilder: (context, index) {
                  FileModel model = datas[index];
                  if (model.thumbnailUrl == null) {
                    return Container(
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                    );
                  }
                  return GestureDetector(
                    onTap: () {
                      print(">>> 媒体点击   ----------- ");
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) {
                          return model.type == 1
                              ? VideoPlayView(url: model.fileUrl)
                              : ImgPreview(
                                  items: data.photoFiles ?? data.photos ?? [],
                                  index: index,
                                );
                        }),
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black, //Color(0x4F000000),
                        border: Border.all(color: Color(0xFFF5F5F5), width: 1),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        child: Stack(
                          children: [
                            CachedNetworkImage(
                              fit: BoxFit.cover,
                              imageUrl: model.thumbnailUrl!,
                            ),
                            // 播放按钮
                            model.type == 1
                                ? Align(
                                    child: Icon(
                                      Icons.play_circle_outline,
                                      color: Colors.white,
                                      size: 40,
                                    ),
                                  )
                                : SizedBox(),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
          ),
          // 容量
          _count > 0
              ? Positioned(
                  bottom: 11,
                  right: 6,
                  child: GestureDetector(
                    onTap: () {
                      print("更多图片");
                    },
                    child: Container(
                      height: 20,
                      decoration: BoxDecoration(
                        color: Color(0x80000000),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        border: Border.all(color: Color(0xFFF5F5F5), width: 1),
                      ),
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(left: 7, right: 7),
                      child: Text(
                        "+$_count",
                        style: TextStyle(fontSize: 14, color: Colors.white),
                      ),
                    ),
                  ),
                )
              : Container(),
        ],
      ),
    );
    // return
  }

  /// 单张大图片
  Widget _bigImageWidget(BuildContext context, FileModel model) {
    return AspectRatio(
      aspectRatio: 1.778,
      child: GestureDetector(
        onTap: () {
          print("大张图片点击");
          Navigator.push(context,
              MaterialPageRoute(builder: (_) => ImgPreview(items: [model])));
        },
        child: Container(
          decoration: BoxDecoration(
            color: Color(0x4F000000),
            border: Border.all(color: Color(0xFFF5F5F5), width: 1),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            child: model.thumbnailUrl != null
                ? CachedNetworkImage(
                    fit: BoxFit.cover,
                    imageUrl: model.thumbnailUrl!,
                  )
                : Container(color: Colors.grey),
          ),
        ),
      ),
    );
  }

  /// 视屏
  Widget _videoWidget(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.778,
      child: GestureDetector(
        onTap: () {
          print("播放视图");
        },
        child: Container(
          decoration: BoxDecoration(
            color: Color(0x4F000000),
            border: Border.all(color: Color(0xFFF5F5F5), width: 1),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
        ),
      ),
    );
  }

  /// 多张图片
  // Widget _imagesWidget(BuildContext context, List<PhotoModel> images) {
  Widget _imagesWidget(BuildContext context, List<FileModel> images) {
    // 余量
    int _count = images.length - 3;
    return AspectRatio(
      aspectRatio: 3.15,
      child: Stack(
        children: [
          // 图片
          Container(
            child: GridView.builder(
                shrinkWrap: true,
                itemCount: images.length > 3 ? 3 : images.length,
                scrollDirection: Axis.horizontal,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 1,
                  mainAxisSpacing: 10.0,
                  crossAxisSpacing: 10.0,
                ),
                itemBuilder: (context, index) {
                  // PhotoModel model = images[index];
                  FileModel model = images[index];
                  return GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) {
                          return model.type == 1
                              ? VideoPlayView(url: model.fileUrl)
                              : ImgPreview(
                                  items: data.photoFiles ?? data.photos ?? [],
                                  index: index,
                                );
                        }),
                      );
                      // http://www.buddybuzzy.com/seat/api/upload/24f69bcd-f4f4-4048-a31d-b43557251d35.jpg
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Color(0x4F000000),
                        border: Border.all(color: Color(0xFFF5F5F5), width: 1),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        // child: Container(
                        //   color: Colors.grey,
                        // ),
                        child: CachedNetworkImage(
                          fit: BoxFit.cover,
                          imageUrl: model.fileUrl!.startsWith('http') == true
                              ? model.fileUrl!
                              : '$kReleaseBaseUrl${model.fileUrl}',
                          placeholder: (context, url) =>
                              CircularProgressIndicator(),
                          errorWidget: (context, url, error) =>
                              Icon(Icons.error),
                        ),
                      ),
                    ),
                  );
                }),
          ),
          // 容量
          _count > 0
              ? Positioned(
                  bottom: 11,
                  right: 6,
                  child: GestureDetector(
                    onTap: () {
                      print("更多图片");
                    },
                    child: Container(
                      height: 20,
                      decoration: BoxDecoration(
                        color: Color(0x80000000),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        border: Border.all(color: Color(0xFFF5F5F5), width: 1),
                      ),
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(left: 7, right: 7),
                      child: Text(
                        "+$_count",
                        style: TextStyle(fontSize: 14, color: Colors.white),
                      ),
                    ),
                  ),
                )
              : Container(),
        ],
      ),
    );
    // return
  }

  /// 底部控制栏
  Widget _bottomControlWidget(BuildContext context) {
    return Container(
      height: 51,
      padding: EdgeInsets.only(bottom: 3),
      child: Row(
        children: [
          // 阅览数
          Expanded(
            child: _buttonWidget(
              icon: Image.asset(
                'assets/images/circle/4.png',
                fit: BoxFit.fill,
              ),
              text: "${data.viewCount}",
              alignment: MainAxisAlignment.start,
            ),
          ),
          SizedBox(
            width: 5,
          ),
          // 评论
          Expanded(
            child: _buttonWidget(
              icon: Image.asset(
                'assets/images/circle/5.png',
                fit: BoxFit.fill,
              ),
              text: "${data.comments != null ? data.comments!.length : 0}",
              alignment: MainAxisAlignment.center,
            ),
          ),
          SizedBox(
            width: 5,
          ),
          // 点赞
          Expanded(
            child: _buttonWidget(
              icon: Image.asset(
                data.isUserLike
                    ? 'assets/images/circle/3.png'
                    : 'assets/images/circle/6.png',
                fit: BoxFit.fill,
              ),
              text: "${data.likeCount}",
              alignment: MainAxisAlignment.end,
              onTap: () {
                print("点赞");
                if (onThumbUp != null) onThumbUp!();
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 点赞控制栏按钮组件
  Widget _buttonWidget({
    required Widget icon,
    required String text,
    Function()? onTap,
    Color? color,
    TextStyle? textStyle,
    MainAxisAlignment alignment = MainAxisAlignment.start,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        child: Row(
          mainAxisAlignment: alignment,
          children: [
            SizedBox(
              width: 22,
              height: 22,
              child: icon,
            ),
            SizedBox(
              width: 3,
            ),
            Container(
              constraints: BoxConstraints(maxWidth: 0.22.sw),
              child: DefaultTextStyle(
                style: TextStyle(
                  fontSize: 11,
                  color: Color(0xFF595959),
                ),
                child: Text(
                  text,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: textStyle,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
