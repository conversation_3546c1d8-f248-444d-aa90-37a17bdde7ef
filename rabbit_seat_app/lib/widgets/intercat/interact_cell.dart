import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/utils/http.dart';

import '../../models/interact/interact_model.dart';
import '../../utils/date_format.dart';
import 'cut_widget.dart';

class InteractCell extends StatelessWidget {
  /// 评论记录
  InteractModel data;

  // /// 显示追加数据
  // bool addToEnable = true;
  //
  // /// 点击事件
  // Function(dynamic)? onTap;
  //
  // /// 文章主人，长按删除评论
  // Function(GlobalKey, dynamic)? onLongPress;
  //
  // /// 评论记录添加用户点击'删除'删除
  // Function(CommentModel)? onCommentsDelTap;
  //
  // /// 追加评论点击
  // Function(CommentModel, CommentModel)? addToOnTap;

  Key? key;

  InteractCell({this.key, required this.data}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    /// 显示评论追加数据
    Widget addToWidget = Container();
    // if (data.children != null && data.children!.length > 0) {
    //   addToWidget = _addToWidget(context);
    // }

    /// 头像url
    String? _portrait;
    // if (data.user != null && data.user!.avatar != null) {
    //   _portrait = kReleaseBaseUrl + data.user!.avatar!;
    // }
    return GestureDetector(
      onTap: () {
        print(">>>> remark --- 评论记录");
      },
      onLongPress: () {
        print(">>>> remark --- 长按删除评论");
      },
      child: Container(
        padding: EdgeInsets.only(right: 15.w, top: 20.h),
        color: Colors.white,
        child: Column(
          children: [
            // 头部
            _headerWidget(),
            // 数据
            _contentWidget(),
          ],
        ),
      ),
    );
  }

  // 头部信息
  Widget _headerWidget() {
    String? url = null;
    if (data.userAvatar != null && data.userAvatar!.isNotEmpty) {
      url = kReleaseBaseUrl + data.userAvatar!;
    }
    String _userName = data.userName ?? '';
    // like 点赞，Comment-评论
    String _msgType = '';

    if (data.messageType != null) {
      if (data.messageType == 'Like') {
        _msgType = '赞了你的圈子';
      } else if (data.messageType == 'Comment') {
        if (data.parentComment != null) {
          _msgType = '回复了你的评论';
        } else {
          _msgType = '评论了你的圈子';
        }
      }
    }
    String _timeText = '';
    if (data.createOn > 0) {
      _timeText = DateFormatter.formatByMilliseconds(data.createOn.toInt());
    }
    return Container(
      child: Row(
        children: [
          Container(
            width: 63,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(left: 15),
            child: ClipOval(
              child: SizedBox(
                width: 38.w,
                height: 38.w,
                child: url != null
                    ? Image.network(
                        url,
                        fit: BoxFit.cover,
                      )
                    : Container(
                        color: Colors.grey,
                      ),
              ),
            ),
          ),
          Expanded(
            child: Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        _userName,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF212121),
                        ),
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Expanded(
                          child: Text(
                        _msgType,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF595959),
                        ),
                      )),
                    ],
                  ),
                  SizedBox(
                    height: 5,
                  ),
                  Text(
                    _timeText,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 11,
                      color: Color(0xFFB5B5B9),
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  // 数据
  Widget _contentWidget() {
    Widget _commentsWidget = Container(
      height: 13,
    );
    // if (data.toUser != null || data.comment != null) {
    //   String? text = data.comment;
    //   if (data.toUser != null && data.toUser!.isNotEmpty) {
    //     text = data.toUser! + ":" + (text??'');
    //   }
    //   _commentsWidget = Padding(
    //     padding: EdgeInsets.only(top: 18, bottom: 20),
    //     child: Text(
    //       text??'',
    //       style: TextStyle(
    //         fontSize: 13,
    //         fontWeight: FontWeight.bold,
    //         color: Color(0xFF212121),
    //       ),
    //     ),
    //   );
    // }
    if (data.comment != null) {
      String? text = data.comment;
      _commentsWidget = Padding(
        padding: EdgeInsets.only(top: 18, bottom: 20),
        child: Text(
          text ?? '',
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.bold,
            color: Color(0xFF212121),
          ),
        ),
      );
    }
    Widget _appendWidget = _addToWidget();
    return Container(
      child: Row(
        children: [
          Container(width: 63),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 文本
                _commentsWidget,
                // 追加评论
                _appendWidget,
                // 分割线
                _divider(),
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 追加数据列表部件
  Widget _addToWidget() {
    // 文章内容
    String? url;
    if (data.articlePhoto != null && data.articlePhoto!.isNotEmpty) {
      url = kReleaseBaseUrl + data.articlePhoto!;
    }
    String articleContent = data.articleContent ?? '';
    Widget _article = Container();
    if (data.articlePhoto != null || data.articleContent != null) {
      List<Widget> _articleChildren = [];
      if (data.articlePhoto != null) {
        _articleChildren.add(ClipRRect(
          borderRadius: BorderRadius.all(Radius.circular(4)),
          child: SizedBox(
            width: 40.w,
            height: 40.w,
            child: url != null
                ? Image.network(
                    url,
                    fit: BoxFit.cover,
                  )
                : Container(
                    color: Colors.grey,
                  ),
          ),
        ));
      }
      if (data.articleContent != null && data.articleContent!.isNotEmpty) {
        final _textWidget = Padding(
          padding: EdgeInsets.only(left: 10),
          child: Text(
            articleContent,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Color(0xFF595959),
            ),
          ),
        );
        _articleChildren.add(Expanded(child: _textWidget));
      }
      _article = Container(
        padding: EdgeInsets.only(top: 5, left: 5, bottom: 5, right: 10),
        constraints: BoxConstraints(minHeight: 50),
        decoration: BoxDecoration(
          color: Color(0xFFF2F2F2),
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Row(
          children: _articleChildren,
        ),
      );
    }
    if (data.parentComment == null && data.parentCommentUserName == null) {
      return _article;
    }
    // 上级评论
    String _parentText = '${data.parentCommentUserName}:${data.parentComment}';
    Widget _comment = Container(
      padding: EdgeInsets.only(top: 14, left: 12, bottom: 8, right: 10),
      decoration: BoxDecoration(
        color: Color(0xFFFAFAFA),
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _parentText,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Color(0xFF595959),
            ),
          ),
          SizedBox(
            height: 10,
          ),
          _article,
        ],
      ),
    );
    return _comment;
  }

  /// 分割线
  Widget _divider() {
    return Padding(
      padding: EdgeInsets.only(top: 22.h),
      child: Divider(
        height: 1,
        color: Color(0xFFF5F5F5),
        // color: Colors.red,
      ),
    );
  }
}
