import 'package:flutter/material.dart';
/**
 * 切割集装箱
 */
class CutContainer extends StatelessWidget {
  final Widget? leading;
  final double width; // leading width
  final double space; // leading 与 child 的间距
  final Widget child;

  const CutContainer({Key? key, this.leading, this.width = 0, this.space = 0, required this.child})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (leading == null) {
      return Container(child: child);
    }
    else {
      return Container(
        child: Row(
          children: [
            width == 0 ? leading! : Container(width: width, child: leading!),
            SizedBox(width: space, height: 1,),
            Expanded(child: child),
          ],
        ),
      );
    }
  }
}

