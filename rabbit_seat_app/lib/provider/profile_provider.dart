
import 'package:flutter/cupertino.dart';
import 'package:rabbit_seat_app/models/device/device_model.dart';
import 'package:rabbit_seat_app/models/home/<USER>';
import 'package:rabbit_seat_app/models/profile.dart';
import 'package:rabbit_seat_app/utils/global.dart';

class ProfileProvider extends ChangeNotifier {
  Profile get profile => Global.profile;

  List<HomeDeviceModel>? get devices => profile.devices;
  void setDevice(HomeDeviceModel model) {

  }

  @override
  void notifyListeners() {
    //保存Profile变更
    Global.saveProfile();
    //通知依赖的Widget更新
    super.notifyListeners();
  }
}
