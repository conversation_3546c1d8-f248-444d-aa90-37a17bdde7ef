import 'dart:convert';

import 'package:date_format/date_format.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/models/device/device_model.dart';
import 'package:rabbit_seat_app/models/device/device_sim_model.dart';

import '../models/error.dart';
import '../models/result/result_model.dart';
import '../utils/http.dart';

class DeviceProvider extends ChangeNotifier {
  /// -1加载失败、 0未加载、1加载成功，2没有设备
  int state = 0;

  /// 我的设备列表
  List<DeviceModel> _mdevices = [];

  /// 我的设备列表
  List<DeviceModel> get mdevices => _mdevices;

  /// 加载数据
  void loadMyDevices({Function()? completed}) async {
    try {
      final data = await Http.get("/api/device/appList");
      final result = ResultModel<List<DeviceModel>?>.fromJson(data, (json) {
        if (json != null && (json is List<dynamic>)) {
          return (json)
              .map((e) => DeviceModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        List<DeviceModel>? _devs = result.data;
        if (_devs != null && _devs.isNotEmpty) {
          state = 1;
          _mdevices = _devs;
          if (displayDevice == null) _displayDevice = _mdevices.first;
        } else {
          // 没有数据
          state = 2;
        }
      } else {
        state = -1;
        EasyLoading.showToast(result.message);
      }
      notifyListeners();
      if (completed != null) completed();
    } catch (e) {
      print('>>> Error:$e');
      state = -1;
      notifyListeners();
      if (completed != null) completed();
      EasyLoading.showToast(e.toString());
    }
  }

  /// 显示设备
  DeviceModel? _displayDevice;

  DeviceModel? get displayDevice => _displayDevice;

  void setDisplayDevice(DeviceModel model) {
    _displayDevice = model;
    notifyListeners();
  }

  /// 编辑设备
  DeviceModel? _editorDevice;

  DeviceModel? get editorDevice => _editorDevice;

  void setEditorDevice(DeviceModel model) {
    _editorDevice = model;
    notifyListeners();
  }

  /// 删除设备
  void deleteDevice({Function(bool)? completed}) async {
    try {
      EasyLoading.show();
      final data = await Http.post(
          '/api/device/unbundling/${_editorDevice!.id}',
          options: Options(contentType: 'application/x-www-form-urlencoded'));
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        EasyLoading.showToast("删除成功！");
        // 从列表中删除
        var _dev;
        _mdevices.forEach((element) {
          if (element.id == _editorDevice!.id) _dev = element;
        });
        _mdevices.remove(_dev);
        // 判断是否是当前显示设备
        if (_editorDevice == displayDevice ||
            _editorDevice!.id == displayDevice!.id) {
          if (_mdevices.length > 0) {
            _displayDevice = _mdevices.first;
          } else {
            _displayDevice = null;
            state = 2;
          }
          print(">>>>. 删除设备");
        }
        _editorDevice = null;
        notifyListeners();
        if (completed != null) completed(true);
      } else {
        EasyLoading.showToast(result.message);
        if (completed != null) completed(false);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
      if (completed != null) completed(false);
    }
  }

  /// 修改名称
  void modifyDeviceName(String name, {Function(bool)? completed}) async {
    try {
      EasyLoading.show();
      Map<String, dynamic> params = {
        "id": _editorDevice!.id,
        "name": name,
      };
      final data = await Http.put('/api/device/config', data: params);
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        EasyLoading.showToast("保存成功！");
        _editorDevice!.name = name;
        notifyListeners();
        if (completed != null) completed(true);
      } else {
        EasyLoading.showToast(result.message);
        if (completed != null) completed(false);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
      if (completed != null) completed(false);
    }
  }

  Future<DeviceSimModel?> getDeviceSimInfo(String iccid) async {
    try {
      final data = await Http.get('/api/device/sim/' + iccid);
      final result = ResultModel<DeviceSimModel>.fromJson(data,
          (json) => DeviceSimModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        return result.data;
      } else {
        return null;
      }
    } on MyError catch (e) {
    } catch (e) {}
    return null;
  }

  void clear() {
    print('>>>> 清除设备数据   ----------- ');
    state = 0;
    _mdevices = [];
    _displayDevice = null;
    _editorDevice = null;
    deviceCount = 0;
    expireTime = null;
  }

  //// 设备显示
  int deviceCount = 0;
  String? minExpiredTime;
  void setDeviceCount(int count) {
    deviceCount = count;
    notifyListeners();
  }

  void setMinExpiredTime(List<DeviceModel> devices) {
    try {
      var list = [];
      devices.forEach((element) {
        if (element.expiredTime != null) {
          list.add(element.expiredTime);
        }
      });
      list.sort();

      minExpiredTime = list.first;
    } catch (_) {}
    notifyListeners();
  }

  //// 当前设备到期时间
  String? expireTime;
  void setCurrentDeviceExpireTime(num val) async {
    if (val == 0) {
      expireTime = null;
    } else {
      final _date = DateTime.fromMillisecondsSinceEpoch(val.toInt());
      DateTime endDate = DateTime(_date.year + 2, _date.month, _date.day);
      expireTime = formatDate(endDate, [yyyy, '-', mm, '-', dd]);
    }
    notifyListeners();
  }
}
