import 'package:flutter/material.dart';

import '../utils/global.dart';
import 'profile_provider.dart';

class ThemeProvider extends ProfileProvider {
  // 获取当前主题，如果为设置主题，则默认使用蓝色主题
  MaterialColor get theme => Global.themes
      .firstWhere((e) => e.value == profile.theme, orElse: () => Colors.orange);

  // 主题改变后，通知其依赖项，新主题会立即生效
  set theme(MaterialColor color) {
    if (color != theme) {
      profile.theme = color[500]?.value as num;
      notifyListeners();
    }
  }
}
