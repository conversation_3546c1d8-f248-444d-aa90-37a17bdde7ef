
import 'dart:core';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/models/user/index.dart';
import '../models/result/result_model.dart';
import '../utils/global.dart';
import '../utils/http.dart';
import 'profile_provider.dart';

class UserProvider extends ProfileProvider {

  UserModel? get user => profile.user;

  BabyModel? _editBaby;

  /// APP是否登录(如果有用户信息，则证明登录过)
  bool get isLogin => user != null;

  /// 用户信息发生变化，更新用户信息并通知依赖它的子孙Widgets更新
  set user(UserModel? user) {
    profile.user = user;
    notifyListeners();
  }

  void setName(String name) {
    profile.user?.name = name;
    notifyListeners();
  }

  void setNickName(String nickName) {
    profile.user?.nickName = nickName;
  }

  void setAvatar(String avatar) {
    profile.user?.avatar = avatar;
    notifyListeners();
  }

  void setSex(int sex) {
    profile.user?.sex = sex;
    notifyListeners();
  }

  void setCity(String city) {
    profile.user?.city = city;
    notifyListeners();
  }


  void setTelephone(String telephone) {
    profile.user?.telephone = telephone;
    notifyListeners();
  }

  void addBaby(BabyModel model) {
    profile.user?.babyInfos?.add(model);
    notifyListeners();
  }

  void setBabyInfos(List<BabyModel> babys) {
    profile.user?.babyInfos = babys;
    notifyListeners();
  }

  void addContacts(ContactsModel model) {
    profile.user?.emergencyContacts?.add(model);
    notifyListeners();
  }

  void setContacts(List<ContactsModel> contacts) {
    profile.user?.emergencyContacts = contacts;
    notifyListeners();
  }

  // 获取到编辑的baby
  BabyModel? get editBaby => _editBaby;
  void setEditBaby(BabyModel model) {
    _editBaby = model;
    notifyListeners();
  }
  // 跟新数据
  void updateBabyInfo(String id, String name, String sex, String birthday) {
    if (user != null && user!.babyInfos != null) {
      List<BabyModel> _babys = user!.babyInfos!;
      for (BabyModel tmp in _babys) {
        if (tmp.id == id) {
          tmp.name = name;
          tmp.sex = sex;
          tmp.birthday = birthday;
          user!.babyInfos = _babys;
          break;
        }
      }
      notifyListeners();
    }
  }

  void removeBabyInfo(BabyModel model) {
    List<BabyModel> _babys = user!.babyInfos!;
    _babys.remove(model);
    user!.babyInfos = _babys;
    notifyListeners();
  }


  /// 加载用户数据
  void loadUserDatas({Function(bool)? completed}) async {
    print("加载数据");
    try {
      String id = Global.profile.user!.id!;
      final data = await Http.get('/api/appuser/user/$id');
      final result = ResultModel<UserModel>.fromJson(data, (json) => UserModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        Global.profile.user = result.data;
        // 刷新页面
        notifyListeners();
        if (completed != null) completed(true);
      } else {
        EasyLoading.showToast(result.message);
        if (completed != null) completed(false);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
      if (completed != null) completed(false);
    }
  }

  /// 添加紧急联系人
  void addEmergencyContact() async {

  }

  /// 修改紧急联系人信息
  void modifyEmergencyContact() async {

  }
}