import 'dart:math';


import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:image_picker/image_picker.dart';
import 'package:media_asset_utils/media_asset_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rabbit_seat_app/utils/PermissionUtils.dart';
import 'package:rabbit_seat_app/utils/utils.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';

import 'dart:io';
import 'dart:ui' as ui show Codec;
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

import 'package:permission_handler/permission_handler.dart';
import 'package:app_settings/app_settings.dart';
import 'package:rabbit_seat_app/widgets/dialog/dialog.dart';

class TempView extends StatefulWidget {
  const TempView({Key? key}) : super(key: key);

  @override
  State<TempView> createState() => _TempViewState();
}

class _TempViewState extends State<TempView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefAppBar(context: context, title: "TempView"),
      body: Column(
        children: [
          ElevatedButton(
              onPressed: () {
                Navigator.push(
                    context, MaterialPageRoute(builder: (_) => TView()));
              },
              child: Text('跳转'))
        ],
      ),
    );
  }
}

/// ======================================================================
/// ======================================================================
/// ======================================================================

class TView extends StatefulWidget {
  const TView({Key? key}) : super(key: key);

  @override
  State<TView> createState() => _TViewState();
}

class _TViewState extends State<TView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Plugin example app'),
      ),
      body: Column(
        children: [
          ElevatedButton(
              onPressed: () async {
                // /// 定位权限状态
                PermissionStatus
                loc_status; // = await Permission.location.status;
                // if (loc_status == PermissionStatus.granted) {
                //   // 后续操作
                //   print(">>> 后续操作 ------- ");
                // }
                // else {
                // 未授权
                loc_status = await Permission.location.request();
                // 状态判断
                if (loc_status == PermissionStatus.denied) {
                  // 用户拒绝访问请求
                  print(">>> 用户拒绝访问请求 -------");
                  EasyLoading.showToast('用户拒绝访问');
                } else if (loc_status == PermissionStatus.granted) {
                  // 用户授予对请
                  print(">>> 后续操作 ------- ");
                  EasyLoading.showToast('用户授予请求');
                } else if (loc_status == PermissionStatus.restricted) {
                  // 操作系统拒绝访问请求，仅支持iOS
                  print(">>> 操作系统拒绝访问请求 ------- ");
                  EasyLoading.showToast('操作系统拒绝访问');
                } else if (loc_status == PermissionStatus.limited) {
                  // 有限访问。仅支持iOS (iOS14+).*
                  print(">>> 有限访问。 ------- ");
                  EasyLoading.showToast('有限访问');
                } else if (loc_status == PermissionStatus.permanentlyDenied) {
                  // 权限被永久拒绝, 仅支持Android
                  print(">>> 权限被永久拒绝。 ------- ");
                  EasyLoading.showToast('权限被永久拒绝');
                  showDialog(
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          title: Text("定位权限未开启"),
                          content:
                          Text('请在iPhone的"设置->两只兔子"选项中，允许两只兔子访问你的定位权限。'),
                          actions: [
                            ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context, false);
                                },
                                child: Text("知道了")),
                            ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context, true);
                                },
                                child: Text("前往设置")),
                          ],
                        );
                      }).then((value) async {
                    if (value != null && value is bool && value == true) {
                      print(">>> 打开手机设置页面 openAppSettings");
                      await AppSettings.openAppSettings();
                    }
                  });
                }
                // }
              },
              child: Text("定位")),
          ElevatedButton(
              onPressed: () async {
                _checkBlePermission();
              },
              child: Text("蓝牙")),
          ElevatedButton(onPressed: () {}, child: Text("按钮3")),
          ElevatedButton(onPressed: () {}, child: Text("按钮4")),
          ElevatedButton(onPressed: () {}, child: Text("按钮5")),
          ElevatedButton(onPressed: () {}, child: Text("按钮6")),
          ElevatedButton(onPressed: () {}, child: Text("按钮7")),
          ElevatedButton(onPressed: () {}, child: Text("按钮8")),
        ],
      ),
    );
  }

  /// 权限检测
  // void checkPermissions
  /// 显示权限状态提示
  void _showPermissionStatusDialog(
      BuildContext context, String type, PermissionStatus status) {
    // 状态判断
    if (status == PermissionStatus.granted) {
      // 用户授予对请

    } else {
      // 其他 denied-未授权；restricted-操作系统拒绝；limited-有限访问；permanentlyDenied-权限被永久拒绝
      DefDialog.showDialog1(
          context: context,
          title: '定位权限未开启',
          message: '请在iPhone的"设置->两字兔子"选项中，允许两只兔子访问你的定位权限。',
          cancelText: '知道了',
          confirmText: '前往设置',
          cancel: () {},
          confirm: () async {
            await AppSettings.openAppSettings();
          });
      return;
    }
  }

  /// 检测蓝牙权限
  void _checkBlePermission() async {
    // 请求蓝牙权限
    PermissionStatus _bleStatus = await Permission.bluetooth.request();
    // 状态判断
    if (_bleStatus != PermissionStatus.granted) {
      // 其他 denied-未授权；restricted-操作系统拒绝；limited-有限访问；permanentlyDenied-权限被永久拒绝
      if (Platform.isIOS) {
        DefDialog.showDialog1(
            context: context,
            title: '蓝牙权限未开启',
            message: '请在iPhone的"设置->两字兔子"选项中，允许两只兔子访问你的蓝牙权限。',
            cancelText: '知道了',
            confirmText: '前往设置',
            cancel: () {},
            confirm: () async {
              await AppSettings.openAppSettings();
            });
      }
      else {
        DefDialog.showDialog1(
            context: context,
            title: '蓝牙权限未开启',
            message: '请在手机的"设置->应用->权限管理"选项中，允许两只兔子访问你的蓝牙权限。',
            cancelText: '知道了',
            confirmText: '前往设置',
            cancel: () {},
            confirm: () async {
              await AppSettings.openAppSettings(type: AppSettingsType.bluetooth);
            });
      }

      return;
    }

    // 用户授予对请

    if (Platform.isAndroid) {
      // 蓝牙连接权限
      PermissionStatus _bleConnectStatus = await Permission.bluetoothConnect.request();
      if (_bleStatus != PermissionStatus.granted) {
        EasyLoading.showToast('status');
        return;
      }
      // 蓝牙扫描权限
      PermissionStatus _bleScanStatus = await Permission.bluetoothScan.request();
      if (_bleScanStatus != PermissionStatus.granted) {
        EasyLoading.showToast('status');
        return;
      }
    }

    EasyLoading.showToast('后续操作！');
  }

  /// 检测定位权限
  void _checkLocalPermission() async {
    // 请求蓝牙权限
    PermissionStatus _bleStatus = await Permission.location.request();
    // 状态判断
    if (_bleStatus != PermissionStatus.granted) {
      // 其他 denied-未授权；restricted-操作系统拒绝；limited-有限访问；permanentlyDenied-权限被永久拒绝
      if (Platform.isIOS) {
        DefDialog.showDialog1(
            context: context,
            title: '蓝牙权限未开启',
            message: '请在iPhone的"设置->两字兔子"选项中，允许两只兔子访问你的蓝牙权限。',
            cancelText: '知道了',
            confirmText: '前往设置',
            cancel: () {},
            confirm: () async {
              await AppSettings.openAppSettings();
            });
      }
      else {
        DefDialog.showDialog1(
            context: context,
            title: '蓝牙权限未开启',
            message: '请在手机的"设置->应用->权限管理"选项中，允许两只兔子访问你的蓝牙权限。',
            cancelText: '知道了',
            confirmText: '前往设置',
            cancel: () {},
            confirm: () async {
              await AppSettings.openAppSettings(type: AppSettingsType.bluetooth);
            });
      }

      return;
    }

    EasyLoading.showToast('后续操作！');
  }

  /// 检测相机权限
  void _checkCameraPermission() async {
    // 请求相机权限
    PermissionStatus _bleStatus = await Permission.camera.request();
    // 状态判断
    if (_bleStatus != PermissionStatus.granted) {
      // 其他 denied-未授权；restricted-操作系统拒绝；limited-有限访问；permanentlyDenied-权限被永久拒绝
      if (Platform.isIOS) {
        DefDialog.showDialog1(
            context: context,
            title: '相机权限未开启',
            message: '请在iPhone的"设置->两字兔子"选项中，允许两只兔子访问你的相机权限。',
            cancelText: '知道了',
            confirmText: '前往设置',
            cancel: () {},
            confirm: () async {
              await AppSettings.openAppSettings();
            });
      }
      else {
        DefDialog.showDialog1(
            context: context,
            title: '相机权限未开启',
            message: '请在手机的"设置->应用->权限管理"选项中，允许两只兔子访问你的相机权限。',
            cancelText: '知道了',
            confirmText: '前往设置',
            cancel: () {},
            confirm: () async {
              await AppSettings.openAppSettings();
            });
      }

      return;
    }

    EasyLoading.showToast('后续操作！');
  }
}
