import 'package:rabbit_seat_app/models/circle/banner.dart';
import 'package:rabbit_seat_app/models/circle/circleModel.dart';

class ContentsState {
  // 默认 -1空白， 0-默认，1-最热，2-最新
  int style = 0;

  // 数据
  Map<String, CircleModel> datas = {};

  List<CircleModel> get items => datas.values.toList();
  List<BannerModel>? banners;

  // 页码
  int pageIndex = 0;
  int pageCount = 0;

  ContentsState init({Map<String, dynamic>? arguments}) {
    ContentsState state = ContentsState()..style = -1;
    if (arguments != null) {
      if (arguments.containsKey('style')) {
        state.style = arguments['style'] as int;
      }
    }
    return state;
  }

  ContentsState clone() {
    return ContentsState()
      ..style = style
      ..datas = datas
      ..banners = banners
      ..pageIndex = pageIndex
      ..pageCount = pageCount;
  }
}
