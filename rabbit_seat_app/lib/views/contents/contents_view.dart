import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:rabbit_seat_app/views/article/article_view.dart';
import 'package:rabbit_seat_app/views/issue/issue_view.dart';
import 'package:rabbit_seat_app/widgets/circle/ContentsCell.dart';

import '../../models/circle/circleModel.dart';
import '../../utils/rx_article.dart';
import '../../widgets/circle/banner_widget.dart';
import '../../widgets/refresh/index.dart';
import '../circle/circle_details_view.dart';
import '../my/user_articles_view.dart';
import 'contents_cubit.dart';
import 'contents_state.dart';

/**
 * 文章目录（圈子，最热，最新）
 */
class ContentsPage extends StatefulWidget {
  final Map<String, dynamic>? arguments;

  const ContentsPage({Key? key, this.arguments}) : super(key: key);

  @override
  State<ContentsPage> createState() => _ContentsPageState();
}

class _ContentsPageState extends State<ContentsPage>
    with AutomaticKeepAliveClientMixin {
  /// 刷新控制
  late EasyRefreshController _refreshController;
  late ScrollController _scrollController;

  /// 最大滚动区域
  double _maxScrollExtent = 0;
  double _offset = 0;

  @override
  void initState() {
    super.initState();

    _refreshController = EasyRefreshController();
    _scrollController = ScrollController();

    /// 滚动监听
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();

    super.dispose();
  }

  /// 滚动监听
  void _scrollListener() {
    _offset = _scrollController.offset;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) =>
          ContentsCubit(arguments: widget.arguments),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<ContentsCubit>(context);

    if (cubit.state.style == -1)
      return Center(
        child: Text('参数异常！'),
      );

    return BlocBuilder<ContentsCubit, ContentsState>(builder: (context, state) {
      return Container(
        color: Color(0xFFF9F9F9),
        child: NotificationListener<OverscrollNotification>(
          onNotification: (notification) {
            _maxScrollExtent = notification.metrics.maxScrollExtent;
            // 执行回弹
            Future.delayed(Duration(milliseconds: 500), () {
              if (_offset > _maxScrollExtent - 60) {
                _scrollController.animateTo(_maxScrollExtent - 60,
                    duration: Duration(milliseconds: 300),
                    curve: Curves.easeIn);
              }
            });
            return false;
          },
          child: EasyRefresh.custom(
            firstRefresh: true,
            controller: _refreshController,
            scrollController: _scrollController,
            header: CustomRefreshHeader(),
            footer: CustomRefreshFooter(),
            slivers: [
              // 包装表头
              SliverToBoxAdapter(
                child: BannerWidget(datas: cubit.state.banners ?? []),
              ),
              // 数据列表
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  _itemBuilder,
                  childCount: cubit.state.items.length,
                ),
              ),
            ],
            shrinkWrap: true,
            onRefresh: () async {
              print("refresh");

              await cubit.loadBanners();
              await cubit.loadDatas(0);

              // 重置加载没有更多数据
              _refreshController.resetLoadState();
              // 刷新结束
              _refreshController.finishRefresh();
            },
            onLoad: () async {
              print("load");
              if (cubit.state.pageIndex < cubit.state.pageCount) {
                await cubit.loadDatas(cubit.state.pageIndex + 1);
              }
              _refreshController.finishLoad(
                  noMore: cubit.state.pageIndex >= cubit.state.pageCount);
            },
          ),
        ),
      );
    });
  }

  /// 列表元素
  Widget _itemBuilder(BuildContext context, int index) {
    final cubit = BlocProvider.of<ContentsCubit>(context);

    CircleModel model = cubit.state.items[index];

    return ContentsCell(
      data: model,
      onTap: () {
        // 跳转文章详情页面
        Navigator.push(context, MaterialPageRoute(
          builder: (context) {
            return ArticlePage(arguments: {"id": model.id});
            // return CircleDetailsView(arguments: {"id": model.id});
          },
        ));
      },
      ownerTap: () {
        // 跳转用户信息页面
        if (model.createBy != null && model.createBy!.id != null) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) =>
                  IssuePage(arguments: {"userId": model.createBy!.id!}),
            ),
          );
        }
      },
      moreTap: () {
        // 删除控制
      },
      thumbsTap: () {
        // 点赞控制
        if (model.isUserLike == true) {
          cubit.cancelThumbUp(model.id!);
        } else {
          cubit.submitThumbsUp(model.id!);
        }
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
