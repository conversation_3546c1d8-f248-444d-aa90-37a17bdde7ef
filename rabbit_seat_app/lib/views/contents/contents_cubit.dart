import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/models/circle/circleModel.dart';
import 'package:rabbit_seat_app/utils/rx_article.dart';

import '../../models/circle/banner.dart';
import '../../models/error.dart';
import '../../models/list/list_model.dart';
import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import 'contents_state.dart';



class ContentsCubit extends Cubit<ContentsState> {
  ContentsCubit({Map<String, dynamic>? arguments})
      : super(ContentsState().init(arguments: arguments)) {
    _init();
  }

  late StreamSubscription _thumbsUpStream;
  // late StreamSubscription _reportStream;
  late StreamSubscription _commentsStream;
  late StreamSubscription _deleteStream;
  late StreamSubscription _refreshStream;

  void _init() {
    // 点赞
    _thumbsUpStream = RxArticle.articleThumbsUpSubject.listen((value) {
      if (value is Map<String, dynamic> && value.containsKey('id') && value.containsKey("val")) {
        String id = value['id'] as String;
        bool val = value['val'] as bool;
        thumbsUpStateChangeById(id, val);
      }
    });

    // 举报
    // _reportStream = RxArticle.articleReportSubject.listen((value) {
    //   if (value is Map<String, dynamic> && value.containsKey('id')) {
    //     String id = value['id'] as String;
    //     reportStateChangeById(id);
    //   }
    // });

    // 开放评论
    _commentsStream = RxArticle.articleCommentsStateSubject.listen((value) {
      if (value is Map<String, dynamic> && value.containsKey('id') && value.containsKey("val")) {
        String id = value['id'] as String;
        bool val = value['val'] as bool;
        remarkStateChangeById(id, val);
      }
    });

    // 删除
    _deleteStream = RxArticle.articleDeleteSubject.where((event) => event is Map<String, dynamic>).listen((value) {
      if (value.containsKey('id')) {
        String id = value['id'] as String;
        deleteArticlesById(id);
      }
    });

    // 刷新
    _refreshStream = RxArticle.articleRefreshSubject.listen((value) {
      loadDatas(0);
    });
  }

  /// 加载列表数据
  Future<void> loadDatas(int page) async {
    try {
      Map<String, dynamic> params = {
        "pageNo": page,
        "pageSize": 10,
        "orderType": state.style, // 0-默认，1-最热，2-最新
      };
      final data = await Http.get("/api/apparticle/app/list", data: params);
      final result = ResultModel<ListModel<CircleModel>?>.fromJson(data, (json) {
        return ListModel<CircleModel>.fromJson(json as Map<String, dynamic>,
            (centent) {
          return CircleModel.fromJson(centent as Map<String, dynamic>);
        });
      });
      if (result.code == 200) {
        ListModel<CircleModel>? listModel =
            result.data as ListModel<CircleModel>?;
        if (listModel != null) {
          Map<String, CircleModel> _datas = {};
          listModel.content?.forEach((element) {
            _datas.addAll({element.id! : element});
          });
          if (page == 0) {
            state.datas = _datas;
          } else {
            state.datas.addAll(_datas);
          }
          state.pageIndex = page;
          state.pageCount = listModel.totalPages.toInt();
        }
        emit(state.clone());
      }
      else {
        EasyLoading.showToast(result.message);
      }
      return;
    } on MyError catch (e) {
      print('>>> Error:$e');
      EasyLoading.showToast(e.message ?? "数据解析失败！");
      return;
    } catch (e) {
      print('>>> Error:$e');
      EasyLoading.showToast(e.toString());
      return;
    }
  }

  /// 加载横幅数据
  Future<void> loadBanners() async {
    try {
      final data = await Http.get("/api/banner/applist");
      final result = ResultModel<List<BannerModel>?>.fromJson(data, (json) {
        if (json is List) {
          return (json as List<dynamic>?)
              ?.map((e) => BannerModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        state.banners = result.data as List<BannerModel>;
        emit(state.clone());
      } else {
        EasyLoading.showToast(result.message);
      }
      return;
    } on MyError catch (e) {
      EasyLoading.showToast(e.message ?? "网络请求失败！");
      return;
    } catch (e) {
      EasyLoading.showToast(e.toString());
      return;
    }
  }

  /// 点赞
  Future<void> submitThumbsUp(String id) async {
    try {
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/like/$id');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        // thumbsUpStateChangeById(id, true);
        RxArticle.articleThumbsUpSubject.add({'id' : id, 'val' : true});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
      return ;
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
      return ;
    }
  }

  /// 取消点赞
  Future<void> cancelThumbUp(String id) async {
    try {
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/unlike/$id');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        // thumbsUpStateChangeById(id, false);
        RxArticle.articleThumbsUpSubject.add({'id' : id, 'val' : false});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
      return;
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
      return;
    }
  }

  /// 点赞状态变化
  void thumbsUpStateChangeById(String id, bool val) {
    CircleModel? model = state.datas[id];
    if (model != null) {
      if (val == true) {
        model.likeCount += 1;
        model.isUserLike = true;
      } else {
        model.likeCount -= 1;
        model.isUserLike = false;
      }
      // 判断页面是否在显示状态
      emit(state.clone());
    }
  }

  /// 举报
  void reportStateChangeById(String id) {
    CircleModel? model = state.datas[id];
    if (model != null) {
      model.postStatus = 'Reported';
      // 判断页面是否在显示状态
      emit(state.clone());
    }
  }

  /// 开发评论
  void remarkStateChangeById(String id, bool val) {
    CircleModel? model = state.datas[id];
    if (model != null) {
      model.canComment = val;
      // 判断页面是否在显示状态
      emit(state.clone());
    }
  }

  /// 删除文章
  void deleteArticlesById(String id) {
    if (state.datas.containsKey(id)) {
      state.datas.remove(id);
      emit(state.clone());
    }
  }

  @override
  Future<void> close() {

    _thumbsUpStream.cancel();
    // _reportStream.cancel();
    _commentsStream.cancel();
    _deleteStream.cancel();
    _refreshStream.cancel();

    return super.close();
  }
}
