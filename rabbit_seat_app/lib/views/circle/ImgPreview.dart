import 'dart:io';

import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';

import '../../models/circle/fileModel.dart';
import '../../models/help/pixItem.dart';

/**
 * 图片预览
 */
class ImgPreview extends StatefulWidget {
  final List<FileModel> items;
  final int index;

  const ImgPreview({Key? key, required, required this.items, this.index = 0})
      : super(key: key);

  @override
  State<ImgPreview> createState() => _ImgPreviewState();
}

class _ImgPreviewState extends State<ImgPreview> {
  List<FileModel> _datas = [];
  int _index = 0;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();

    _datas = widget.items;
    _index = widget.index;
    _pageController = PageController(initialPage: _index);
  }

  @override
  void dispose() {

    imageCache?.clear();

    print('>>> 清除缓存');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: DefAppBar(context: context, title: "${_index + 1}/${_datas.length}"),
      body: Container(
        color: Colors.black,
          child: PhotoViewGallery.builder(
        scrollPhysics: const BouncingScrollPhysics(),
        builder: (BuildContext context, int index) {
          FileModel _item = _datas[index];
          return PhotoViewGalleryPageOptions(
            // imageProvider: FileImage(File((_item.path!))),
            imageProvider: NetworkImage(_item.fileUrl!.startsWith('http') ? _item.fileUrl! : (kReleaseBaseUrl + _item.fileUrl!)),
            initialScale: PhotoViewComputedScale.contained * 1,
            heroAttributes:
                PhotoViewHeroAttributes(tag: _item.id ?? '$_index'),
          );
        },
        itemCount: _datas.length,
        loadingBuilder: (context, event) => Center(
          child: Container(
            width: 20.0,
            height: 20.0,
            child: CircularProgressIndicator(
              value: event == null
                  ? 0
                  : event.cumulativeBytesLoaded / event.expectedTotalBytes!,
            ),
          ),
        ),
        backgroundDecoration: BoxDecoration(
          color: Colors.black
        ),
        pageController: _pageController,
        onPageChanged: (val) {
          setState(() {
            _index = val;
          });
        },
      )),
    );
  }
}
