import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/help/pixItem.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/widgets/preview/image_scale_widget.dart';



class ImagePreview extends StatefulWidget {
  final PixItem data;
  const ImagePreview({Key? key, required this.data}) : super(key: key);

  @override
  State<ImagePreview> createState() => _ImagePreviewState();
}

class _ImagePreviewState extends State<ImagePreview> {
  @override
  Widget build(BuildContext context) {

    Widget _imageWidget = Container();
    if (widget.data.fileUrl != null) {
      String? _url;
      if (widget.data.fileUrl!.startsWith('https://') ||
          widget.data.fileUrl!.startsWith('http://')) {
        _url = widget.data.fileUrl;
      } else {
        _url = '$kReleaseBaseUrl${widget.data.fileUrl}';
      }
      _imageWidget = ImageScaleWidget.network(url: _url);
    } else if (widget.data.path != null) {
      _imageWidget = ImageScaleWidget.file(file: File(widget.data.path!),);
    }
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          _imageWidget,
          Container(
            margin: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
            padding: EdgeInsets.only(left: 15),
            height: 56,
            child: IconButton(onPressed: () {
              Navigator.pop(context);
            }, icon: Icon(Icons.arrow_back_ios, color: Colors.white, size: 24,)),
          ),
        ],
      ),
    );
  }
}


