import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/circle/commentModel.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/utils/signal.dart';
import 'package:rabbit_seat_app/views/report/report_view.dart';
import 'package:rabbit_seat_app/widgets/circle/ArticleInfoWidget.dart';
import 'package:rabbit_seat_app/widgets/dialog/dialog.dart';

import '../../models/circle/circleModel.dart';
import '../../utils/global.dart';
import '../../utils/http.dart';
// import '../../widgets/circle/circle_cell.dart';
import '../../widgets/circle/overlay_text_input.dart';
import '../../widgets/circle/remark_bar.dart';
import '../../widgets/circle/remark_cell.dart';
import '../../widgets/dialog/overlay_utils.dart';

/**
 * 朋友圈文章详情
 */
class CircleDetailsView extends StatefulWidget {
  /// 参数
  final Map<String, dynamic>? arguments;

  const CircleDetailsView({Key? key, this.arguments}) : super(key: key);

  @override
  State<CircleDetailsView> createState() => _CircleDetailsViewState();
}

class _CircleDetailsViewState extends State<CircleDetailsView> {
  /// 菜单控制
  GlobalKey _menuKey = GlobalKey();

  // 数据源
  CircleModel? _data;

  // 请求数据id
  String? _id;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    if (widget.arguments != null && widget.arguments!.containsKey("id")) {
      _id = widget.arguments!["id"];
      // 加载数据
      _loadDatas();
    }
  }

  @override
  void dispose() {
    imageCache?.clear();
    print(">>>>> 清除图片缓存 details");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    


    Widget _body = Container();

    /// 判断是否违规
    bool _isViolation = _data?.postStatus == 'Reported' ? true : false;

    if (_isViolation) {
      _body = Column(
        children: [
          _violationWidget(context),
          Expanded(child: Stack(
            children: [
              Positioned.fill(child: _bodyWidget(context)),
              Positioned.fill(child: Container(color: Colors.white38,)),
            ],
          )),
        ],
      );
    } else {
      _body = _bodyWidget(context);
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: Icon(
            Icons.arrow_back_ios,
            color: Colors.black,
            size: 18,
          ),
        ),
        backgroundColor: Colors.white,
        actions: [
          IconButton(
              key: _menuKey,
              onPressed: _isViolation == false ? () {
                _showMenuDialog(context);
              } : null,
              icon: Icon(
                Icons.more_horiz_rounded,
                color: Colors.black,
              )),
        ],
        elevation: 1,
        title: Text(
          "圈子详情",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF000000),
          ),
        ),
        centerTitle: true,
      ),
      resizeToAvoidBottomInset: false, // 底部弹出输入框，阻止界面resize
      body: _body,
    );
  }

  /// 违规下架
  Widget _violationWidget(BuildContext context) {
    return Container(
      color: Color(0xFFEEEEEE),
      padding: EdgeInsets.only(left: 19, top: 18, right: 9, bottom: 19),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '违规下架',
            style: TextStyle(
              fontSize: 15.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 5,),
          Text(
            '内容含色情暴力，如有疑问请查看《两只兔子用户服务协议》或联 系客服',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: Color(0xFF595959),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示菜单
  void _showMenuDialog(context) {
    if (_data == null) return;

    Size _size = Size(0, 0);
    Widget _childWidget = Container();
    // 举报
    if (_data!.createBy!.id == Global.profile.user!.id) {
      // 自己提交的内容，可以删除
      _childWidget = Column(
        children: [
          InkWell(
            onTap: () {
              OverlayUtils.close();
              DefDialog.showDialog1(
                  context: context,
                  message: '确定删除此条圈子吗?',
                  confirm: () {
                    _deleteArticlesOrComments(_data!.id!, isArticles: true);
                  });
            },
            child: Container(
              height: 40,
              alignment: Alignment.center,
              child: Text(
                "删除",
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF000000),
                ),
              ),
            ),
          ),
          Divider(
            height: 1,
            color: Color(0xFFEEEEEE),
          ),
          InkWell(
            onTap: () {
              OverlayUtils.close();
              _openOrCloseComments();
            },
            child: Container(
              height: 40,
              alignment: Alignment.center,
              child: Text(
                _data!.canComment == true ? '关闭评论' : "开启评论",
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF000000),
                ),
              ),
            ),
          )
        ],
      );
      _size = Size(90, 81);
    } else {
      // 游客
      _childWidget = InkWell(
        onTap: () {
          OverlayUtils.close();
          print(">>> 举报当前文章");

          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (_) => ReportPage(
                    articleId: _data!.id!,
                  )));
        },
        child: Container(
          child: Text(
            "举报",
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: Color(0xFF000000),
            ),
          ),
        ),
      );
      _size = Size(90, 40);
    }
    // 判断是否是自己
    OverlayUtils.showMenuView(
      context: context,
      key: _menuKey,
      size: _size,
      child: _childWidget,
    );
  }

  /// 主体部件
  Widget _bodyWidget(BuildContext context) {
    if (_data == null) return Container();

    // 是否开放评论
    Widget _remarkWidget = Container();
    // if (_data!.canComment == true) {
    // 是否为空
    if (_data!.comments == null || _data!.comments!.length > 0) {
      _remarkWidget = _remarkListWidget();
    }
    else {
      if (_data!.canComment == true) {
        _remarkWidget = _emptyDataWidget('快来发表第一条评论吧！');
      } else {
        // 未开放评论
        _remarkWidget = _emptyDataWidget('用户未开放评论！');
      }
    }

    return Column(
      children: [
        Expanded(
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Container(
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 记录数据
                      ArticleInfoWidget(data: _data!),
                      // CircleCell(
                      //   data: _data!,
                      //   showControl: false,
                      // ),
                      // 分割线
                      Container(
                        color: Color(0xFFF9F9F9),
                        height: 10.h,
                      ),
                      // 抬头
                      Container(
                        padding: EdgeInsets.only(left: 15, top: 17),
                        color: Colors.white,
                        child: Text(
                          "评论 (${_data!.comments != null ? _data!.comments!
                              .length : 0})",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // 列表数据
              _remarkWidget,
            ],
          ),
        ),
        RemarkBar(
          isLike: _data!.isUserLike,
          likeNumber: _data!.likeCount.toInt(),
          onRemarkAction: () {
            // 判断是否开放评论
            if (_data!.canComment) {
              OverlayTextInput.show(context, hintText: "写评论...",
                  onSubmitted: (text) {
                    _sendComments(id: _data!.id!, content: text);
                  });
            } else {
              EasyLoading.showToast("暂未开放评论！");
            }
          },
          onThumbUpAction: () {
            if (_data!.isUserLike == true) {
              _cancelThumbUp(_data!.id!);
            } else {
              _submitThumbsUp(_data!.id!);
            }
          },
        ),
      ],
    );
  }

  /// 没有数据提示部件
  Widget _emptyDataWidget(String? text) {
    return SliverToBoxAdapter(
      child: Container(
        constraints: BoxConstraints(minHeight: 350),
        padding: EdgeInsets.only(top: 30.h),
        alignment: Alignment.topCenter,
        child: Text(
          text ?? '',
          style: TextStyle(fontSize: 13.sp, color: Color(0xFF595959)),
        ),
      ),
    );
  }

  /// 评论列表数据
  Widget _remarkListWidget() {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
            (context, index) {
          CommentModel model = _data!.comments![index];
          return RemarkCell(
            key: GlobalKey(),
            data: model,
            onShowAll: (comments){
              setState(() {
                model.showAll =  true;
              });
            },
            onTap: (comments) {
              /// 判断用户是否开启评论
              if (_data!.canComment == true) {
                /// 评论评论
                OverlayTextInput.show(context,
                    hintText: "回复：${comments.user!.nickName}",
                    onSubmitted: (text) {
                      _sendComments(
                          id: _data!.id!, content: text, parentId: comments.id!);
                    });
              } else {
                EasyLoading.showToast("暂未开放评论！");
              }
            },
            onLongPress: (key, comments) {
              if (_data!.createBy!.id == Global.profile.user!.id) {
                OverlayUtils.showMenuView(
                  context: context,
                  key: key,
                  size: const Size(100, 50),
                  child: InkWell(
                    onTap: () {
                    },
                    child: Text(
                      "删除",
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.red,
                      ),
                    ),
                  ),
                );
              }
            },
            addToOnTap: (comments, addToModel) {
              /// 判断用户是否开启评论
              if (_data!.canComment == true) {
                /// 追加评论
                OverlayTextInput.show(context,
                    hintText: "回复：${addToModel.user!.nickName}",
                    onSubmitted: (text) {
                      _sendComments(
                          id: _data!.id!, content: text, parentId: comments.id!);
                    });
              } else {
                EasyLoading.showToast("暂未开放评论！");
              }
            },
            onCommentsDelTap: (comments) {
              /// 删除自己添加的评论
              DefDialog.showDialog1(
                  context: context,
                  message: '确定删除此条评论吗？',
                  confirm: () {
                    _deleteArticlesOrComments(comments.id!);
                  });
            },
          );
        },
        childCount: _data!.comments!.length,
      ),
    );
  }

  /// 加载数据
  void _loadDatas() async {
    try {
      EasyLoading.show();
      final data = await Http.get('/api/apparticle/$_id');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) {
        return CircleModel.fromJson(json as Map<String, dynamic>);
      });
      if (res.code == 200) {
        _data = res.data;
        if (mounted) setState(() {});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
    }
  }

  /// 发送评论
  void _sendComments(
      {required String id, required String content, String? parentId}) async {
    try {
      if (content == null || content.isEmpty) {
        EasyLoading.showToast("未编辑评论内容！");
        return;
      }
      Map<String, dynamic> params = {'content': content, 'articleId': id};
      if (parentId != null) {
        params.addAll({'parentId': parentId});
      }
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/comment', data: params);
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        EasyLoading.showToast("评论成功！");
        _loadDatas();
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
    }
  }

  /// 删除文章/评论
  void _deleteArticlesOrComments(String id, {bool isArticles = false}) async {
    try {
      EasyLoading.show();
      var urlAdd = isArticles ? '' : 'comment/';
      final data = await Http.del('/api/apparticle/$urlAdd$id');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        if (isArticles == true) {
          EasyLoading.showToast("文章删除成功!");
          Navigator.pop(context);
        } else {
          EasyLoading.showToast("删除成功！");
          // 刷新或从新加载
          _loadDatas();
        }
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
    }
  }

  /// 开启或关闭评论
  void _openOrCloseComments() async {
    try {
      bool _status = !_data!.canComment;
      Map<String, dynamic> params = {
        "id": _data!.id,
        "canComment": _status,
      };
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/canComment', data: params);
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        if (_status == true) {
          EasyLoading.showToast("已开启评论！");
        } else {
          EasyLoading.showToast("已关闭评论！");
        }
        _data!.canComment = _status;
        if (mounted) setState(() {});
        KitSignal().send('Comments', {"id": _data!.id, "state": _status});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
    }
  }

  /// 点赞
  void _submitThumbsUp(String id) async {
    try {
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/like/$id');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        _data!.isUserLike = true;
        _data!.likeCount += 1;
        // context.read<CircleProvider>().setIsLike(id: id, state: true);
        if (mounted) setState(() {});
        KitSignal().send('ThumbUp', {"id": id, "state": true});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
    }
  }

  /// 取消点赞
  void _cancelThumbUp(String id) async {
    try {
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/unlike/$id');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        _data!.isUserLike = false;
        _data!.likeCount -= 1;
        // context.read<CircleProvider>().setIsLike(id: id, state: false);
        if (mounted) setState(() {});
        KitSignal().send('ThumbUp', {"id": id, "state": false});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
    }
  }
}
