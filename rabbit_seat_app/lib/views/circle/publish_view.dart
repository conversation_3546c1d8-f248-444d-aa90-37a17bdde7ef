import 'dart:io';
  
import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:media_asset_utils/media_asset_utils.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/error.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/services/help_service.dart';
import 'package:rabbit_seat_app/utils/PermissionUtils.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/utils/oss/OSSClient.dart';
import 'package:rabbit_seat_app/utils/oss/ossmodels.dart';
import 'package:rabbit_seat_app/utils/rx_article.dart';
import 'package:rabbit_seat_app/utils/signal.dart';
import 'package:rabbit_seat_app/utils/utils.dart';
import 'package:rabbit_seat_app/views/circle/review_view.dart';
import 'package:rabbit_seat_app/views/common/video_play_view.dart';
import 'package:video_player/video_player.dart';

// import '../../models/help/uploadFile.dart';
import '../../models/help/pixItem.dart';
import '../../widgets/common/appbar.dart';
import '../../widgets/dialog/dialog.dart';
import 'location_view.dart';

import 'dart:convert';

/**
 * 发表文章
 */
class PublishView extends StatefulWidget {
  const PublishView({Key? key}) : super(key: key);

  @override
  State<PublishView> createState() => _PublishViewState();
}

class _PublishViewState extends State<PublishView> {
  // StreamSubscription? subEvents;

  /// 发布按钮启用
  bool _publishEnabled = false;

  /// 纬度
  double? latitude = null;

  /// 经度
  double? longitude = null;

  /// 定位信息
  String? locationInfo = null; // "不显示位置";

  /// 是否开始评论
  bool openComments = true;

  /// 文章内容
  String? content = null;

  /// 图片
  // List<UploadFile> files = [];
  List<PixItem> files = [];

  /// 图片选择器
  ImagePicker imagePicker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于375dp * 812dp的屏幕

    return Scaffold(
      appBar: DefAppBar(context: context, title: '', actions: [
        _publishButton(),
      ]),
      body: _buildBody(context),
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.white,
    );
  }

  /// 构建主体
  Widget _buildBody(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // 移除软键盘
        _removePrimaryFocus();
      },
      child: ListView(
        physics: ClampingScrollPhysics(),
        children: [
          Container(
            padding: EdgeInsets.only(left: 15, top: 17, right: 15, bottom: 5),
            height: 150.h,
            child: TextField(
              onChanged: (text) {
                content = text;
                setPublishState();
              },
              maxLines: 6,
              style: TextStyle(fontSize: 14.sp),
              inputFormatters: [LengthLimitingTextInputFormatter(400)],
              decoration: const InputDecoration(
                  hintText: "分享此刻的想法...", border: InputBorder.none),
            ),
          ),
          Align(
            alignment: Alignment.topRight,
            child: Container(
              padding: EdgeInsets.only(right: 13.w),
              child: Text(
                content != null ? "${content!.length}/400" : '0/400',
                style: TextStyle(color: Color(0xFF595959), fontSize: 11),
              ),
            ),
          ),
          SizedBox(
            height: 26.h,
          ),
          _imagesWidget(),
          Container(
            padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 26.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _locationWidget(),
                _commentsWidget(),
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 拍照
  void _takePhotos() async {
    // 相机权限
    Util.checkCameraPermissionStatus(context, "拍照发布圈子", () async {
      try {
        XFile? _xfile =
            await ImagePicker().pickImage(source: ImageSource.camera);
        if (_xfile != null) {
          // 压缩图片
          final res = await MediaAssetUtils.compressImage(File(_xfile.path));
          if (res != null) {
            // 刷新数据
            files.add(PixItem.easy(path: res.path, type: 0));
            setPublishState();
          }
        }
      } catch (e) {
        print(">>> e:$e");
      }
    });
  }

  /// 相册获取图片
  void _galleryPhotos() async {
    // 相册权限
    Util.checkStoragePermissionStatus(context, "选取照片用户发布圈子", () async {
      try {
        XFile? _xfile =
            await ImagePicker().pickImage(source: ImageSource.gallery);
        if (_xfile != null) {
          // 压缩图片
          final res = await MediaAssetUtils.compressImage(File(_xfile.path));
          if (res != null) {
            // 刷新数据
            files.add(PixItem.easy(path: res.path, type: 0));
            setPublishState();
          }
        }
      } catch (e) {
        print(">>> e:$e");
      }
    });
  }

  /// 录像
  void _takeVideo() async {
    Util.checkMicrophonePermissionStatus(context, "录像发布圈子", () {
      Util.checkCameraPermissionStatus(context, "录像发布圈子", () async {
        try {
          XFile? _xfile =
              await imagePicker.pickVideo(source: ImageSource.camera);
          if (_xfile != null) {
            File? res = await MediaAssetUtils.compressVideo(File(_xfile.path));
            if (res != null) {
              // 刷新数据
              try {
                PixItem item = PixItem.easy(path: res.path, type: 1);
                item.controller = VideoPlayerController.file(File(item.path!));
                item.controller!.initialize().then((value) {
                  setState(() {});
                });
                files.add(item);
                setPublishState();
              } catch (e) {
                EasyLoading.showToast("视频加载失败！");
              }
            }
          }
        } catch (e) {
          print(">>> e:$e");
        }
      });
    });
  }

  /// 相册获取视频
  void _galleryVideo() async {
    // 相册权限
    Util.checkStoragePermissionStatus(context, "选取视频用于发布圈子", () async {
      try {
        XFile? _xfile =
            await ImagePicker().pickVideo(source: ImageSource.gallery);
        if (_xfile != null) {
          // 压缩视频
          File? res = await MediaAssetUtils.compressVideo(File(_xfile.path));
          if (res != null) {
            // 刷新数据
            try {
              PixItem item = PixItem.easy(path: res.path, type: 1);
              item.controller = VideoPlayerController.file(File(item.path!));
              item.controller!.initialize().then((value) {
                setState(() {});
              });
              files.add(item);
              setPublishState();
            } catch (e) {
              EasyLoading.showToast("视频加载失败！");
            }
          }
        }
      } catch (e) {
        print(">>> e:$e");
      }
    });
  }

  // 需要在Info.plist中添加NSLocationAlwaysAndWhenInUseUsageDescription和NSLocationWhenInUseUsageDescription字段。
  /// 定位，位置部件
  Widget _locationWidget() {
    return InkWell(
        onTap: () async {
          // 移除软键盘
          _removePrimaryFocus();

          // 定位状态
          final _localIsOn =
              await Permission.locationWhenInUse.serviceStatus.isEnabled;
          if (_localIsOn == false) {
            DefDialog.showDialog2(
                context: context,
                title: '温馨提示',
                message: '定位功能未开启，请先开启定位功能后使用！',
                itemText: '知道了',
                confirm: () {});
            return;
          }

          // 定位权限访问
          Util.checkLocationPermissionStatus(context, "发布圈子定位", () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (ctx) {
                  return LocationView(arguments: {
                    "lat": latitude,
                    "lon": longitude,
                    'name': locationInfo
                  });
                },
              ),
            ).then((value) {
              print(">>>> 页面返回 - 刷新定位信息 value:$value");
              if (value != null && (value is Map<String, dynamic>)) {
                String name = value["name"];
                if (name == '不显示位置') {
                  locationInfo = null;
                } else {
                  locationInfo = value["name"];
                  latitude = value["lat"];
                  longitude = value["lon"];
                }
              }
            });
          });
          // PermissionStatus _localStatus = await Permission.location.request();
          // if (_localStatus != PermissionStatus.granted) {
          //   DefDialog.showDialog1(
          //       context: context,
          //       title: '定位权限未开启',
          //       message: Platform.isIOS
          //           ? '请在iPhone的"设置->两字兔子"选项中，允许两只兔子访问你的定位权限。'
          //           : '请在手机的"设置->应用->权限管理"选项中，允许两只兔子访问你的定位权限。',
          //       cancelText: '知道了',
          //       confirmText: '前往设置',
          //       cancel: () {},
          //       confirm: () async {
          //         openAppSettings();
          //         // Platform.isIOS
          //         //     ? await AppSettings.openAppSettings()
          //         //     : await AppSettings.openLocationSettings();
          //       });

          //   return;
          // }
        },
        child: Container(
          height: 21.h,
          padding: EdgeInsets.only(left: 5.w, right: 5.w),
          child: Row(
            children: [
              Icon(
                Icons.fmd_good,
                size: 14.sp,
                color: locationInfo != null
                    ? Color.fromARGB(255, 234, 138, 36)
                    : Color(0xFF595959),
              ),
              Container(
                margin: EdgeInsets.only(left: 5.w),
                child: Text(locationInfo ?? '不显示位置',
                    style: TextStyle(
                      fontSize: 11.w,
                      color: Color(0xFF595959),
                    )),
              ),
              Container(
                margin: EdgeInsets.only(left: 5.w),
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: 12.sp,
                  color: Color(0xFF595959),
                ),
              )
            ],
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20.w),
            border: Border.all(color: Color(0xFFF1F1F1), width: 1.w),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1), // 阴影的颜色
                offset: const Offset(10, 20), // 阴影与容器的距离
                blurRadius: 60.0, // 高斯的标准偏差与盒子的形状卷积。
                spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
              ),
            ],
          ),
        ));
  }

  /// 开放，关闭评论部件
  Widget _commentsWidget() {
    return InkWell(
      onTap: () {
        // 移除软键盘
        _removePrimaryFocus();

        setState(() {
          openComments = !openComments;
        });
      },
      child: Container(
        height: 21.h,
        padding: EdgeInsets.only(left: 5.w, right: 5.w),
        child: Row(
          children: [
            Image(
              image: AssetImage(openComments
                  ? 'assets/images/publish/4.png'
                  : 'assets/images/publish/5.png'),
              width: 13.w,
            ),
            Container(
              margin: EdgeInsets.only(left: 5.w, right: 5.w),
              child: Text(openComments ? "开放评论" : "关闭评论",
                  style: TextStyle(
                    fontSize: 11.w,
                    color: const Color(0xFF595959),
                  )),
            ),
          ],
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.w),
          border: Border.all(color: const Color(0xFFF1F1F1), width: 1.w),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1), // 阴影的颜色
              offset: const Offset(10, 20), // 阴影与容器的距离
              blurRadius: 60.0, // 高斯的标准偏差与盒子的形状卷积。
              spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
            ),
          ],
        ),
      ),
    );
  }

  /// 发布按钮
  Widget _publishButton() {
    Color _bgColor = Color(0xFFFAFAFA);
    Color _textColor = Color(0xFF212121);
    if (_publishEnabled) {
      _bgColor = Business.mainColor;
      _textColor = Color(0xFFFFFFFF);
    }
    return TextButton(
      onPressed: _publishEnabled ? _publishedArticles : null,
      child: Container(
        alignment: Alignment.center,
        width: 57,
        height: 29,
        decoration: BoxDecoration(
          color: _bgColor,
          borderRadius: BorderRadius.all(Radius.circular(29)),
        ),
        child: Text(
          "发布",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: _textColor,
          ),
        ),
      ),
    );
  }

  /// 多张图片
  Widget _imagesWidget() {
    /// 图片的最大限制
    return Container(
      padding: EdgeInsets.only(left: 15, right: 15),
      child: GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        scrollDirection: Axis.vertical,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: files.length < 9 ? files.length + 1 : files.length,
        itemBuilder: (context, index) {
          print(">>> item index:$index");
          if (files.length < 9 && index == files.length) {
            // 添加
            return GestureDetector(
              onTap: () {
                print("获取图片");
                // 移除软键盘
                _removePrimaryFocus();
                // 选择图片
                DefDialog.showPixBottomSheet(context, (index) {
                  // 1-拍照；2-相册；3-录像；4-相册
                  if (index == 1) {
                    _takePhotos();
                  } else if (index == 2) {
                    _galleryPhotos();
                  } else if (index == 3) {
                    _takeVideo();
                  } else if (index == 4) {
                    _galleryVideo();
                  }
                }, true);
              },
              child: Container(
                decoration: BoxDecoration(
                  color: Color(0xFFF2F2F2),
                  borderRadius: BorderRadius.all(Radius.circular(6)),
                ),
                child: Center(
                  child: Icon(
                    Icons.add,
                    size: 40.sp,
                    color: const Color.fromARGB(255, 156, 156, 156),
                  ),
                ),
              ),
            );
          } else {
            PixItem _fils = files[index];
            Widget _contentWidget = Container();
            if (_fils.type == 0) {
              _contentWidget = Image.file(
                File(_fils.path!),
                fit: BoxFit.cover,
              );
            } else if (_fils.type == 1) {
              _contentWidget = Stack(
                children: [
                  _fils.controller != null
                      ? VideoPlayer(_fils.controller!)
                      : Container(),
                  // 播放按钮
                  Align(
                    child: Icon(
                      Icons.play_circle_outline,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                ],
              );
            }
            return Stack(
              children: [
                // 显示图片
                GestureDetector(
                  onTap: () {
                    print("图片预览");

                    // 移除软键盘
                    _removePrimaryFocus();

                    if (_fils.type == 0) {
                      print("图片预览");
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (_) => ImagePreview(data: _fils)));
                    } else if (_fils.type == 1) {
                      print("视频预览");
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (_) => VideoPlayView(
                                    file: File(_fils.path!),
                                  )));
                    }
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.all(Radius.circular(6)),
                    child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        color: Color(0xFFF2F2F2),
                        borderRadius: BorderRadius.all(Radius.circular(6)),
                      ),
                      child: _contentWidget,
                    ),
                  ),
                ),
                // 删除按钮
                Positioned(
                  top: 3,
                  right: 5,
                  child: GestureDetector(
                    onTap: () {
                      // 移除软键盘
                      _removePrimaryFocus();

                      setState(() {
                        files.removeAt(index);
                      });
                      setPublishState();
                    },
                    child: Container(
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.remove_circle,
                        color: Colors.grey,
                        size: 22,
                      ),
                    ),
                  ),
                ),
              ],
            );
          }
        },
      ),
    );
  }

  void setPublishState() {
    if ((content == null || content!.isEmpty) && files.isEmpty) {
      setState(() {
        _publishEnabled = false;
      });
    } else {
      setState(() {
        _publishEnabled = true;
      });
    }
  }

  /// 移除软键盘
  void _removePrimaryFocus() {
    // 焦点管理器，通过焦点管理器取消关注焦点，从而关闭软键盘
    FocusScopeNode scopeNode = FocusScope.of(context);
    // 判断当前是否无主要关注焦点及焦点子类不为空
    if (!scopeNode.hasPrimaryFocus && scopeNode.focusedChild != null) {
      // 当前主要焦点取消关注
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  /// 发布
  void _publishedArticles() async {
    try {
      EasyLoading.show();
      // 提交图片
      String? _fstUrl = null;
      List<Map<String, dynamic>> _imageIds = [];
      List<Map<String, dynamic>> _voideIds = [];

      List<Map<String, dynamic>> _imgJson = [];
      List<Map<String, dynamic>> _vodJson = [];

      // for (PixItem flx in files) {
      //   var res = await HelpService.uploadFile(flx.path!);
      //   if (res != null) {
      //     flx.fileUrl = res.fileUrl;
      //     flx.fileId = res.fileId;
      //     if (flx.type == 0) {
      //       _imageIds.add({"id": res.fileId});
      //     } else {
      //       _voideIds.add({"id": res.fileId});
      //     }
      //     if (_fstUrl == null) _fstUrl = res.fileUrl;
      //   } else {
      //     EasyLoading.dismiss();
      //     EasyLoading.showToast("图片上传失败！");
      //     return;
      //   }
      // }

      // 上传文件
      try {
        for (PixItem flx in files) {
          String _uuid = Util.getUuid();
          if (flx.type == 0) {
            final OSSObject obj = await OSSClient().putObject(
                object: OSSVideoObject.fromFile(
                    file: File(flx.path!), uuid: _uuid));
            _imageIds.add({"id": obj.uuid});
            _imgJson.add({'fileUrl': obj.url, 'fileName': obj.name});
          } else {
            final OSSObject obj = await OSSClient().putObject(
                object: OSSImageObject.fromFile(
                    file: File(flx.path!), uuid: _uuid));
            _voideIds.add({"id": obj.uuid});
            _vodJson.add({'fileUrl': obj.url, 'fileName': obj.name});
          }
        }
      } catch (e) {
        print(">>> upload:$e");
      }

      // String? _fstUrl = _imageIds.length > 0
      //     ? (_imageIds.first as Map<String, dynamic>)['fileUrl']
      //     : null;
      Map<String, dynamic> params = {
        "content": content, //文字内容
        "locationInfo": locationInfo, // 定位信息
        "longitude": longitude, // 定位信息
        "latitude": latitude, // 定位信息
        "canComment": openComments, //能否评论
        // "photos": _imageIds, // 图片id
        // "videos": _voideIds, // 视频id
        "photosJson": _imgJson.isNotEmpty ? jsonEncode(_imgJson) : null,
        "videosJson": _vodJson.isNotEmpty ? jsonEncode(_vodJson) : null,
        "articlePhoto": _fstUrl,
      };
      print(">>>> params:${params.toString()}");

      final data = await Http.post('/api/apparticle', data: params);
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        EasyLoading.showToast("发表成功");
        KitSignal().send('publish');
        RxArticle.articleRefreshSubject.add(true);
        Navigator.pop(context, false);
      } else {
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      print(">>> error${e.toString()}");
      EasyLoading.dismiss();
      EasyLoading.showToast(e.message ?? '网络请求失败！');
    } catch (e) {
      print(">>> error:::${e.toString()}");
      print((e as Exception).runtimeType);
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }

  /// 初始化
// void _initOSSClient() async {
//   final object = await OSSClient().putObject(
//     object: OSSObject(),
//     bucket: '',
//     endpoint: '',
//     path: '',
//   );
// }
}
