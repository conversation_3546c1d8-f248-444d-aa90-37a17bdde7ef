import 'dart:collection';

import 'package:amap_flutter_search/amap_flutter_search.dart' as MapSearch;
import 'package:amap_map/amap_map.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:rabbit_seat_app/business.dart';

import 'package:x_amap_base/x_amap_base.dart';

import '../../widgets/common/appbar.dart';

/**
 * 定位
 */
class LocationView extends StatefulWidget {
  final Map<String, dynamic>? arguments;

  const LocationView({Key? key, this.arguments}) : super(key: key);

  @override
  State<LocationView> createState() => _LocationViewState();
}

class _LocationViewState extends State<LocationView>
    with SingleTickerProviderStateMixin {
  /// 放开注释

  /// 显示地图控制器
  AMapController? _mapController;

  ///建议在APP首次启动或者进行弹窗进行隐私声明时，根据用户设置
  ///
  /// [hasContains] 隐私权政策是否包含高德开平隐私权政策
  ///
  ///[hasShow] 隐私权政策是否弹窗展示告知用户
  ///
  ///[hasAgree] 隐私权政策是否已经取得用户同意
  late AMapPrivacyStatement _mapPrivacyStatement;

  /// 搜索周边
  List<MapSearch.AMapPoi> _circum = [];
  List<MapSearch.AMapPoi> _search = [];

  /// 用户位置
  LatLng? _userLatlng;

  /// 搜索定位
  LatLng? _searchLatLng;
  String? _keyword;

  /// 初次加载周边
  bool _isLading = false;

  /// 地图标记
  List<Marker> _markers = <Marker>[];

  /// 选中索引
  MapSearch.AMapPoi? _selectedPoi;
  String? _name;

  /// 文本输入控制器
  TextEditingController _textEditingController = TextEditingController();

  /// 按钮
  bool _confirmEnabled = false;

  @override
  void initState() {
    super.initState();

    /// 初始化地图协议
    _mapPrivacyStatement =
        AMapPrivacyStatement(hasContains: true, hasShow: true, hasAgree: true);

    // 设置 key
    MapSearch.AmapFlutterSearch.setApiKey(
      amapApiKeys.androidKey!,
      amapApiKeys.iosKey!,
    );
    // 同意更新隐私
    MapSearch.AmapFlutterSearch.updatePrivacyAgree(true);
    // 更新隐私显示
    MapSearch.AmapFlutterSearch.updatePrivacyShow(true, true);

    // 初始化加载的数据
    if (widget.arguments != null) {
      if (widget.arguments!.containsKey("lat") &&
          widget.arguments!["lat"] != null &&
          widget.arguments!.containsKey("lon") &&
          widget.arguments!['lon'] != null) {
        _searchLatLng =
            LatLng(widget.arguments!["lat"], widget.arguments!["lon"]);
      }
      if (widget.arguments!.containsKey("name") &&
          widget.arguments!['name'] != null) {
        _name = widget.arguments!['name'];
      }
      if (_searchLatLng != null && _name != null) {
        _confirmEnabled = true;
      }
    }
  }

  @override
  void dispose() {
    super.dispose();

    if (_mapController != null) {
      _mapController!.disponse();
      _mapController = null;
    }
  }

  static const AMapApiKey amapApiKeys = AMapApiKey(
      androidKey: "5d335232ad3253f08e9cd4c78e7906af",
      iosKey: "afaf1f8bfe5fabbd2d4f3bcc981ac8eb");
  static const AMapPrivacyStatement amapPrivacyStatement =
      AMapPrivacyStatement(hasContains: true, hasShow: true, hasAgree: true);

  @override
  Widget build(BuildContext context) {
    AMapInitializer.init(context, apiKey: amapApiKeys);
    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: "所在位置",
        actions: [
          _confirmButton(),
        ],
      ),
      body: _bodyWidget(context),
    );
  }

  /// 放开注释

  /// 构建主体
  Widget _bodyWidget(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: _mapWidget(context),
        ),
        // 位置列表
        Expanded(child: _placeWidget(context)),
      ],
    );
  }

  /// 地图部件
  Widget _mapWidget(BuildContext context) {
    ///使用默认属性创建一个地图
    final AMapWidget map = AMapWidget(
      onMapCreated: _onMapCreated,
      // 点击
      onTap: (latLng) {
        // 移除软键盘
        _removePrimaryFocus(context);
        // 刷新页面
        setState(() {
          _isLading = true;
          // 清除地图上的标记
          _markers = <Marker>[];
          _confirmEnabled = false;
          // 添加一个当前的标记
          _markers.add(
            Marker(
                position: latLng,
                icon: BitmapDescriptor.defaultMarker,
                infoWindowEnable: false),
          );
          _textEditingController.text = "";
        });
        // 移动到中心
        if (_mapController != null) {
          _mapController!.moveCamera(CameraUpdate.newCameraPosition(
              CameraPosition(target: latLng, zoom: 16)));
        }
        _searchLatLng = latLng;
        // 加载周边数据
        _mapSearchAround(latLng);
      },
      // 标记
      markers: Set<Marker>.of(_markers),
      // 初始化时的地图中心
      initialCameraPosition: CameraPosition(target: LatLng(30.12, 120.21)),
      // 定位小蓝点
      myLocationStyleOptions: MyLocationStyleOptions(true,
          circleFillColor: Colors.transparent,
          circleStrokeColor: Colors.transparent,
          circleStrokeWidth: 0),
      // 缩放级别
      minMaxZoomPreference: MinMaxZoomPreference(3, 20),
      // 定位发生变化
      onLocationChanged: _onLocationChanged,
    );
    // 地图
    return Stack(
      children: [
        Container(
          child: map,
        ),
        Positioned(
          right: 15,
          bottom: 23,
          child: GestureDetector(
            onTap: () {
              // 移除软键盘
              _removePrimaryFocus(context);
              // 移动到中心
              if (_mapController != null && _userLatlng != null) {
                _mapController!.moveCamera(CameraUpdate.newCameraPosition(
                    CameraPosition(target: _userLatlng!, zoom: 16)));
              }
              _textEditingController.text = "";
              // 清除标记
              _markers = [];
              _confirmEnabled = false;
              _isLading = true;
              _searchLatLng = _userLatlng;
              _mapSearchAround(_searchLatLng!);
              setState(() {});
            },
            child: SizedBox(
              width: 30,
              height: 30,
              child: Image.asset('assets/images/map/2.png'),
            ),
          ),
        )
      ],
    );
  }

  /// 地点部件
  Widget _placeWidget(BuildContext context) {
    Widget _contentWidget = _emptyWidget();
    if (_isLading) {
      _contentWidget = _loadingWidget();
    } else {
      if (_search.length > 0) {
        _contentWidget = ListView.builder(
          itemCount: _search.length,
          itemBuilder: (context, index) {
            bool _selected = false;
            final item = _search[index];
            if (_selectedPoi == null && _name != null && item.name == _name) {
              _selectedPoi = item;
            }
            if (_selectedPoi == item) {
              _selected = true;
            }
            return MapPoiWidget(
              poi: item,
              selected: _selected,
              onTap: (poi) {
                // 移除软键盘
                _removePrimaryFocus(context);
                _name = poi.name;
                _selectedPoi = poi;
                setState(() {
                  _confirmEnabled = true;
                });
              },
            );
          },
        );
      }
    }
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        _removePrimaryFocus(context);
      },
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(left: 12, top: 17, right: 12, bottom: 12),
              alignment: Alignment.centerLeft,
              height: 38,
              padding: EdgeInsets.only(left: 18, right: 18),
              decoration: BoxDecoration(
                color: Color(0xFFF2F2F2),
                borderRadius: BorderRadius.all(Radius.circular(38 / 2)),
              ),
              child: Row(
                children: [
                  SizedBox(
                    width: 15,
                    height: 15,
                    child: Image.asset('assets/images/map/1.png'),
                  ),
                  SizedBox(
                    width: 7,
                  ),
                  Expanded(
                    child: TextField(
                      controller: _textEditingController,
                      onChanged: (value) {
                        print(">>> value:$value");
                      },
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF666666),
                      ),
                      textInputAction: TextInputAction.search,
                      cursorColor: Colors.black54,
                      onSubmitted: (value) {
                        print("搜索");
                        if (_searchLatLng != null) {
                          setState(() {
                            _confirmEnabled = false;
                            _isLading = true;
                            _mapSearchAround(_searchLatLng!, keyword: value);
                          });
                        }
                      },
                      decoration: InputDecoration(
                        hintText: "搜索地点",
                        hintStyle: TextStyle(
                          fontSize: 14,
                          color: Color(0xFFA5A5A5),
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: _contentWidget,
            ),
          ],
        ),
      ),
    );
  }

  /// 移除软键盘
  void _removePrimaryFocus(context) {
    // 焦点管理器，通过焦点管理器取消关注焦点，从而关闭软键盘
    FocusScopeNode scopeNode = FocusScope.of(context);
    // 判断当前是否无主要关注焦点及焦点子类不为空
    if (!scopeNode.hasPrimaryFocus && scopeNode.focusedChild != null) {
      // 当前主要焦点取消关注
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  /// 空数据部件
  Widget _emptyWidget() {
    return Container(
        child: Center(
      child: Text("没有数据"),
    ));
  }

  /// 初始化加载部件
  Widget _loadingWidget() {
    return Container(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CupertinoActivityIndicator(),
            SizedBox(
              height: 9,
            ),
            Text(
              "正在加载...",
              style: TextStyle(fontSize: 12, color: Color(0xFF595959)),
            ),
          ],
        ),
      ),
    );
  }

  /// 定位发生变化
  void _onLocationChanged(AMapLocation location) async {
    print(">>> location:${location.toString()}");

    if (_userLatlng == null && _searchLatLng == null) {
      setState(() {
        _isLading = true;
      });
      _searchLatLng = location.latLng;
      // 搜索周边
      _mapSearchAround(location.latLng);
      _mapController!.moveCamera(CameraUpdate.newCameraPosition(
          CameraPosition(target: location.latLng, zoom: 16)));
    }
    _userLatlng = location.latLng;
  }

  /// 查询周边
  void _mapSearchAround(LatLng latLng, {String? keyword}) async {
    print(">>> _mapSearchAround");

    
    // 搜索周边
    List<MapSearch.AMapPoi> _list =
        await MapSearch.AmapFlutterSearch.searchAround(
      MapSearch.Location(
        latitude: latLng.latitude,
        longitude: latLng.longitude,
      ),
      keyword: keyword ?? '',
      page: 1,
      pageSize: 40,
    );

    _isLading = false;

    _circum = _list;
    _search.clear();
    var map = HashMap();
    map["name"] = "不显示位置";
    map["address"] = "隐藏地点";
    map["distance"] = 0;
    var noneLocation = MapSearch.AMapPoi.fromJson(map);
    _search.add(noneLocation);
    _search.addAll(_circum);

    if (mounted) setState(() {});
  }

  /// 地图创建回调
  void _onMapCreated(AMapController controller) {
    _mapController = controller;
    if (_searchLatLng != null) {
      setState(() {
        _isLading = true;
        // 加标记
        _markers = [];
        // 添加一个当前的标记
        _markers.add(
          Marker(
              position: _searchLatLng!,
              icon: BitmapDescriptor.defaultMarker,
              infoWindowEnable: false),
        );
      });
      // 搜索周边
      _mapSearchAround(_searchLatLng!);
      _mapController!.moveCamera(CameraUpdate.newCameraPosition(
          CameraPosition(target: _searchLatLng!, zoom: 16)));
    }
    if (mounted) setState(() {});
  }

  /// 发布按钮
  Widget _confirmButton() {
    Color _bgColor = Color(0xFFFAFAFA);
    Color _textColor = Color(0xFF212121);
    if (_confirmEnabled) {
      _bgColor = Business.mainColor;
      _textColor = Color(0xFFFFFFFF);
    }
    return TextButton(
      onPressed: _confirmEnabled
          ? () {
              if (_selectedPoi != null) {
                Navigator.pop(context, {
                  'name': _selectedPoi!.name,
                  "lat": _selectedPoi!.location?.latitude,
                  'lon': _selectedPoi!.location?.longitude,
                });
              }
            }
          : null,
      child: Container(
        alignment: Alignment.center,
        width: 57,
        height: 29,
        decoration: BoxDecoration(
          color: _bgColor,
          borderRadius: BorderRadius.all(Radius.circular(29)),
        ),
        child: Text(
          "确定",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: _textColor,
          ),
        ),
      ),
    );
  }
}

/// 定位内容

class MapPoiWidget extends StatelessWidget {
  bool selected = false;

  final MapSearch.AMapPoi poi;

  final Function(MapSearch.AMapPoi)? onTap;

  MapPoiWidget({Key? key, required this.poi, this.selected = false, this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color _bgColor = selected == true ? Color(0xFFF5F5F5) : Colors.white;
    return Material(
      color: Colors.transparent,
      // INK可以实现装饰容器
      child: Ink(
        decoration: BoxDecoration(color: _bgColor),
        child: InkWell(
          // 设置点击事件回调
          onTap: () {
            if (onTap != null) onTap!(poi);
          },
          child: Container(
            padding: EdgeInsets.only(left: 15, right: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Container(
                  padding: EdgeInsets.only(top: 15),
                  child: Text(
                    poi.name ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF212121),
                    ),
                  ),
                ),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.only(top: 7, bottom: 15),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${poi.distance ?? '?'}m',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF595959),
                        ),
                      ),
                      Text(
                        ' | ',
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF595959),
                        ),
                      ),
                      Expanded(
                          child: Text(
                        poi.address ?? '',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF595959),
                        ),
                      )),
                    ],
                  ),
                ),
                Divider(
                  height: 1,
                  color: Color(0xFFF5F5F5),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 获取两点的测算距离
/*
  double _getMeasuredDistance(LatLng latLng1, LatLng latLng2) {
    // 单位：⽶
    // def: 地球半径
    double def = 6378137.0;

    // 纬度[latitude]换算
    double radLat1 = latLng1.latitude * pi / 180;
    double radLat2 = latLng2.latitude * pi / 180;
    // 经度[longitude]换算
    double radLon1 = latLng1.longitude * pi / 180;
    double radLon2 = latLng2.longitude * pi / 180;

    double a = radLat1 - radLat2;
    double b = radLon1 - radLon2;

    double s = 2 *
        asin(sqrt(pow(sin(a / 2), 2) +
            cos(radLat1) * cos(radLat2) * pow(sin(b / 2), 2)));
    return (s * def).roundToDouble();

    return 0;
  }
  */
