import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/circle/banner.dart';
import 'package:rabbit_seat_app/models/error.dart';
import 'package:rabbit_seat_app/models/list/list_model.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/utils/rx.dart';
import 'package:rabbit_seat_app/views/circle/circle_details_view.dart';
import 'package:rabbit_seat_app/views/circle/publish_view.dart';
import 'package:rabbit_seat_app/views/contents/contents_view.dart';
import 'package:rabbit_seat_app/views/issue/issue_view.dart';

import '../../models/circle/circleModel.dart';
import '../../utils/http.dart';
import '../../utils/signal.dart';
import '../../widgets/circle/banner_widget.dart';
import '../../widgets/circle/circle_cell.dart';
import '../../widgets/refresh/index.dart';
import '../my/user_articles_view.dart';

/**
 * 圈子页面
 */
class CircleView extends StatefulWidget {
  const CircleView({Key? key}) : super(key: key);

  @override
  State<CircleView> createState() => _CircleViewState();
}

class _CircleViewState extends State<CircleView>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  List<String> _tabs = ["圈子", "最热", "最新"];

  late TabController _controller;

  int _tabIndex = 0;

  @override
  void initState() {
    super.initState();
    _controller = TabController(length: _tabs.length, vsync: this);

    Rx.circleRefreshSubject.listen((value) {
      if (value == -1) Rx.circleRefreshSubject.add(_tabIndex);
    });
  }

  @override
  Widget build(BuildContext context) {


    return Scaffold(
      body: _buildBody(),
      // backgroundColor: Color.fromARGB(255, 213, 213, 213),
      backgroundColor: Colors.white,
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        _tapBarWidget(),
        Expanded(
          child: TabBarView(
            controller: _controller,
            children: [
              ContentsPage(
                arguments: {'style': 0},
              ),
              ContentsPage(
                arguments: {'style': 1},
              ),
              ContentsPage(
                arguments: {'style': 2},
              ),
              // CircleWidget(),
              // HottestWidget(),
              // LatestWidget(),
            ],
          ),
        ),
      ],
    );
  }

  /// 顶部 控制器
  Widget _tapBarWidget() {
    return Container(
      padding:
      EdgeInsets.only(top: MediaQuery.of(context).padding.top, bottom: 10),
      child: Container(
        height: 44,
        child: Row(
          children: [
            /// Tab
            Expanded(
              child: Container(
                margin: EdgeInsets.only(left: 8),
                alignment: Alignment.centerLeft,
                child: TabBar(
                  // 选中文本颜色
                  labelColor: Color(0xFF000000),
                  // 默认文本颜色
                  unselectedLabelColor: Color(0xFF242424),
                  controller: _controller,
                  // 指示器颜色
                  indicatorColor: Business.mainColor,
                  // 指示器大小
                  indicatorSize: TabBarIndicatorSize.label,
                  // 指示器厚度
                  indicatorWeight: 2,
                  // 可滚动，让按钮左侧对齐
                  isScrollable: true,
                  // 默认文本样式
                  unselectedLabelStyle:
                  TextStyle(fontSize: 13, color: Colors.black),
                  // 选中文本样式
                  labelStyle:
                  TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                  // 标签
                  tabs: _tabs.map((e) => Text(e)).toList(),
                  onTap: (index) {
                    if (_tabIndex == index) {
                      Rx.circleRefreshSubject.add(index);
                    }
                    _tabIndex = index;
                  },
                ),
              ),
            ),

            /// 按钮
            Container(
              margin: EdgeInsets.only(right: 15, left: 15),
              child: GestureDetector(
                onTap: () {
                  print("拍色照片");
                  Navigator.push(context,
                      MaterialPageRoute(builder: (ctx) => PublishView()));
                },
                child: SizedBox(
                  width: 30,
                  height: 30,
                  child: Image.asset(
                    'assets/images/circle/1.png',
                    fit: BoxFit.fill,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

/// ==================================================================
///

class CircleWidget extends StatefulWidget {
  const CircleWidget({Key? key}) : super(key: key);

  @override
  State<CircleWidget> createState() => _CircleWidgetState();
}

class _CircleWidgetState extends State<CircleWidget>
    with AutomaticKeepAliveClientMixin {
  /// 元素列表数据
  List<CircleModel> _items = [];

  /// 页码
  int _pageIndex = 0;

  /// 刷新控制
  EasyRefreshController _controller = EasyRefreshController();
  ScrollController _scrollController = ScrollController();

  /// 最大滚动区域
  double _maxScrollExtent = 0;
  double _offset = 0;

  List<BannerModel> datas = [];

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration(milliseconds: 100), () {
      if (mounted) _controller.callRefresh();
    });

    KitSignal().link("ThumbUp", 'circle', (arg) {
      print(">>> 点赞信号处理");
      // 刷新数据
      if (arg is Map && arg.containsKey('id') && arg.containsKey('state')) {
        String id = arg["id"] as String;
        bool state = arg['state'] as bool;
        for (CircleModel model in _items) {
          if (model.id == id) {
            model.isUserLike = state;
            if (model.isUserLike == true) {
              model.likeCount += 1;
            } else {
              model.likeCount -= 1;
            }
            break;
          }
        }
        if (mounted) setState(() {});
      }
    });

    KitSignal().link("Comments", 'circle', (arg) {
      // 刷新数据
      if (arg is Map && arg.containsKey('id') && arg.containsKey('state')) {
        String id = arg["id"] as String;
        bool state = arg['state'] as bool;
        for (CircleModel model in _items) {
          if (model.id == id) {
            model.canComment = state;
            break;
          }
        }
        if (mounted) setState(() {});
      }
    });

    KitSignal().link("publish", 'circle', (arg) {
      // 刷新数据
      _controller.callRefresh();
    });

    /// 滚动监听
    _scrollController.addListener(() {
      // print(">>>> offset:${_scrollController.offset}");
      _offset = _scrollController.offset;
      // _scrollController.to
    });

    /// 刷新
    Rx.circleRefreshSubject.listen((value) {
      if (value == 0) _controller.callRefresh();
    });
  }

  @override
  void dispose() {
    KitSignal().off("ThumbUp", 'circle');
    KitSignal().off("Comments", 'circle');
    KitSignal().off("publish", 'circle');

    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // banner 组件
    Widget _bannerWidget = SliverToBoxAdapter(
      child: BannerWidget(datas: this.datas),
    );

    return Container(
      color: Color(0xFFF9F9F9), //Color.fromARGB(255, 213, 213, 213),
      child: NotificationListener<OverscrollNotification>(
        onNotification: (notification) {
          _maxScrollExtent = notification.metrics.maxScrollExtent;
          // notification.
          Future.delayed(Duration(milliseconds: 500), () {
            print(">>> 执行回弹 ------******** --------");
            if (_offset > _maxScrollExtent - 60) {
              _scrollController.animateTo(_maxScrollExtent - 60,
                  duration: Duration(milliseconds: 300), curve: Curves.easeIn);
            }
          });
          return false;
        },
        child: EasyRefresh.custom(
          controller: _controller,
          scrollController: _scrollController,
          header: CustomRefreshHeader(),
          footer: CustomRefreshFooter(),
          slivers: [
            // banner
            _bannerWidget,
            // 数据列表
            SliverList(
              delegate: SliverChildBuilderDelegate(
                _itemBuilder,
                childCount: _items.length,
              ),
            )
          ],
          shrinkWrap: true,
          onRefresh: () async {
            print("refresh");
            _loadDatas(0);
            _loadBannerDatas();
          },
          onLoad: () async {
            print("load");
            _loadDatas(_pageIndex + 1);
          },
        ),
      ),
    );
  }

  /// 列表元素
  Widget _itemBuilder(BuildContext context, int index) {
    // CircleModel model = context.watch<CircleProvider>().items[index];
    CircleModel model = _items[index];

    bool _isViolation = model.postStatus == 'Reported' ? true : false;
    Widget _child = Container();
    if (_isViolation == true) {
      _child = Stack(
        children: [
          CircleCell(
            data: model,
            onPortraitTap: () {
              if (model.createBy != null && model.createBy!.id != null) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => IssuePage(
                      arguments: {"id": model.createBy!.id!},
                    ),
                  ),
                );

                // Navigator.push(
                //     context,
                //     MaterialPageRoute(
                //         builder: (_) =>
                //             UserArticlesView(userId: model.createBy!.id!)));
              }
            },
          ),
          Positioned.fill(
              child: Container(
                color: Colors.white38,
                padding: EdgeInsets.only(left: 22.w, right: 22.w, top: 24.h),
                alignment: Alignment.topRight,
                child: Text(
                  '违规下架',
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFFFF3700),
                  ),
                ),
              )),
        ],
      );
    } else {
      _child = CircleCell(
        data: model,
        onPortraitTap: () {
          print(">>>>>>>>>");
          if (model.createBy != null && model.createBy!.id != null) {
            // Navigator.push(
            //     context,
            //     MaterialPageRoute(
            //         builder: (_) =>
            //             UserArticlesView(userId: model.createBy!.id!)));
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) =>
                    IssuePage(arguments: {"id": model.createBy!.id!}),
              ),
            );
          }
        },
      );
    }

    return GestureDetector(
      onTap: () {
        Navigator.push(context, MaterialPageRoute(
          builder: (context) {
            return CircleDetailsView(
              arguments: {"id": model.id},
            );
          },
        ));
      },
      child: _child,
      // child: CircleCell(
      //   data: model,
      //   onPortraitTap: () {
      //     print(">>>>>>>>>");
      //     if (model.createBy != null && model.createBy!.id != null) {
      //       Navigator.push(context, MaterialPageRoute(builder: (_) =>
      //           UserArticlesView(userId: model.createBy!.id!)));
      //     }
      //   },
      // ),
    );
    // return CircleCell(index: index % 3,);
  }

  /// 加载数据
  void _loadDatas(int index) async {
    try {
      Map<String, dynamic> params = {
        "pageNo": index,
        "pageSize": 10,
        "orderType": 0, // 0-默认，1-最热，2-最新
      };
      final data = await Http.get("/api/apparticle/app/list", data: params);
      final result = ResultModel<ListModel>.fromJson(data, (json) {
        return ListModel<CircleModel>.fromJson(json as Map<String, dynamic>,
                (centent) {
              return CircleModel.fromJson(centent as Map<String, dynamic>);
            });
      });
      if (result.code == 200) {
        ListModel<CircleModel> listModel =
        result.data as ListModel<CircleModel>;
        if (listModel != null) {
          if (index == 0) {
            _items = listModel.content ?? [];
            // 重置加载没有更多数据
            _controller.resetLoadState();
            // 刷新结束
            _controller.finishRefresh();
          } else {
            _items.addAll(listModel.content ?? []);
            // 刷新结束， 控制加载，如果当前的加载条目，大于总条目，显示没有更多数据
            _controller.finishLoad(
                noMore: _items.length >= listModel.totalElements);
          }
        }
        _pageIndex = index;
        if (mounted) setState(() {});
      } else {
        index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      print('>>> Error:$e');
      index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
      EasyLoading.showToast(e.message ?? "数据解析失败！");
    } catch (e) {
      print('>>> Error:$e');
      index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
      EasyLoading.showToast(e.toString());
    }
  }

  // 加载横幅数据
  void _loadBannerDatas() async {
    try {
      final data = await Http.get("/api/banner/applist");
      final result = ResultModel<List<BannerModel>?>.fromJson(data, (json) {
        if (json is List) {
          return (json as List<dynamic>?)
              ?.map((e) => BannerModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        this.datas = result.data as List<BannerModel>;
        if (mounted) setState(() {});
      } else {
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      EasyLoading.showToast(e.message ?? "网络请求失败！");
    } catch (e) {
      EasyLoading.showToast(e.toString());
    }
  }

  @override
  bool get wantKeepAlive => true;
}

/// ==================================================================
///

class HottestWidget extends StatefulWidget {
  const HottestWidget({Key? key}) : super(key: key);

  @override
  State<HottestWidget> createState() => _HottestWidgetState();
}

class _HottestWidgetState extends State<HottestWidget>
    with AutomaticKeepAliveClientMixin {
  /// 元素列表数据
  List<CircleModel> _items = [];

  /// 页码
  int _pageIndex = 0;

  /// 刷新控制
  EasyRefreshController _controller = EasyRefreshController();
  ScrollController _scrollController = ScrollController();

  /// 最大滚动区域
  double _maxScrollExtent = 0;
  double _offset = 0;

  List<BannerModel> datas = [];

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration(milliseconds: 100), () {
      if (mounted) _controller.callRefresh();
    });

    KitSignal().link("ThumbUp", 'hottest', (arg) {
      print(">>> 点赞信号处理");
      // 刷新数据
      if (arg is Map && arg.containsKey('id') && arg.containsKey('state')) {
        String id = arg["id"] as String;
        bool state = arg['state'] as bool;
        for (CircleModel model in _items) {
          if (model.id == id) {
            model.isUserLike = state;
            if (model.isUserLike == true) {
              model.likeCount += 1;
            } else {
              model.likeCount -= 1;
            }
            break;
          }
        }
        if (mounted) setState(() {});
      }
    });

    KitSignal().link("Comments", 'hottest', (arg) {
      // 刷新数据
      if (arg is Map && arg.containsKey('id') && arg.containsKey('state')) {
        String id = arg["id"] as String;
        bool state = arg['state'] as bool;
        for (CircleModel model in _items) {
          if (model.id == id) {
            model.canComment = state;
            break;
          }
        }
        if (mounted) setState(() {});
      }
    });

    /// 滚动监听
    _scrollController.addListener(() {
      // print(">>>> offset:${_scrollController.offset}");
      _offset = _scrollController.offset;
      // _scrollController.to
    });

    /// 刷新
    Rx.circleRefreshSubject.listen((value) {
      if (value == 1) _controller.callRefresh();
    });
  }

  @override
  void dispose() {
    KitSignal().off("ThumbUp", 'hottest');
    KitSignal().off("Comments", 'hottest');

    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF9F9F9), //Color.fromARGB(255, 213, 213, 213),
      child: NotificationListener<OverscrollNotification>(
        onNotification: (notification) {
          _maxScrollExtent = notification.metrics.maxScrollExtent;
          // notification.
          Future.delayed(Duration(milliseconds: 500), () {
            print(">>> 执行回弹 ------******** --------");
            if (_offset > _maxScrollExtent - 60) {
              _scrollController.animateTo(_maxScrollExtent - 60,
                  duration: Duration(milliseconds: 300), curve: Curves.easeIn);
            }
          });
          return false;
        },
        child: EasyRefresh.custom(
          controller: _controller,
          scrollController: _scrollController,
          header: CustomRefreshHeader(),
          footer: CustomRefreshFooter(),
          slivers: [
            /// 包装表头
            SliverToBoxAdapter(
              child: BannerWidget(
                datas: this.datas,
              ),
            ),

            SliverList(
              delegate: SliverChildBuilderDelegate(
                _itemBuilder,
                childCount: _items.length,
              ),
            ),
          ],
          shrinkWrap: true,
          onRefresh: () async {
            print("refresh");
            _loadDatas(0);
            _loadBannerDatas();
          },
          onLoad: () async {
            print("load");
            _loadDatas(_pageIndex + 1);
          },
        ),
      ),
    );
  }

  /// 列表元素
  Widget _itemBuilder(BuildContext context, int index) {
    // CircleModel model = context.watch<CircleProvider>().items[index];
    CircleModel model = _items[index];

    Widget _child = Container();

    /// 判断是否违规
    bool _isViolation = model.postStatus == 'Reported' ? true : false;
    if (_isViolation == true) {
      _child = Stack(
        children: [
          Positioned.fill(child: CircleCell(data: model)),
          Positioned.fill(
              child: Container(
                color: Colors.white38,
              )),
        ],
      );
    } else {
      _child = CircleCell(data: model);
    }
    return GestureDetector(
      onTap: () {
        Navigator.push(context, MaterialPageRoute(
          builder: (context) {
            return CircleDetailsView(
              arguments: {"id": model.id},
            );
          },
        ));
      },
      child: CircleCell(
        data: model,
        onPortraitTap: () {
          print(">>>>>>>>>");
          if (model.createBy != null && model.createBy!.id != null) {
            // Navigator.push(
            //     context,
            //     MaterialPageRoute(
            //         builder: (_) =>
            //             UserArticlesView(userId: model.createBy!.id!)));

            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) =>
                    IssuePage(arguments: {'id': model.createBy!.id!}),
              ),
            );
          }
        },
      ),
    );
    // return CircleCell(index: index % 3,);
  }

  /// 加载数据
  void _loadDatas(int index) async {
    try {
      Map<String, dynamic> params = {
        "pageNo": index,
        "pageSize": 10,
        "orderType": 1, // 0-默认，1-最热，2-最新
      };
      final data = await Http.get("/api/apparticle/app/list", data: params);
      final result = ResultModel<ListModel>.fromJson(data, (json) {
        return ListModel<CircleModel>.fromJson(json as Map<String, dynamic>,
                (centent) {
              return CircleModel.fromJson(centent as Map<String, dynamic>);
            });
      });
      if (result.code == 200) {
        ListModel<CircleModel> listModel =
        result.data as ListModel<CircleModel>;
        if (listModel != null) {
          if (index == 0) {
            _items = listModel.content ?? [];
            // 重置加载没有更多数据
            _controller.resetLoadState();
            // 刷新结束
            _controller.finishRefresh();
          } else {
            _items.addAll(listModel.content ?? []);
            // 刷新结束， 控制加载，如果当前的加载条目，大于总条目，显示没有更多数据
            _controller.finishLoad(
                noMore: _items.length >= listModel.totalElements);
          }
        }
        _pageIndex = index;
        if (mounted) setState(() {});
      } else {
        index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      print('>>> Error:$e');
      index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
      EasyLoading.showToast(e.message ?? "数据解析失败！");
    } catch (e) {
      print('>>> Error:$e');
      index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
      EasyLoading.showToast(e.toString());
    }
  }

  // 加载横幅数据
  void _loadBannerDatas() async {
    try {
      final data = await Http.get("/api/banner/applist");
      final result = ResultModel<List<BannerModel>?>.fromJson(data, (json) {
        if (json is List) {
          return (json as List<dynamic>?)
              ?.map((e) => BannerModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        this.datas = result.data as List<BannerModel>;
        if (mounted) setState(() {});
      } else {
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      EasyLoading.showToast(e.message ?? "网络请求失败！");
    } catch (e) {
      EasyLoading.showToast(e.toString());
    }
  }

  @override
  bool get wantKeepAlive => true;
}

/// ==================================================================
///

class LatestWidget extends StatefulWidget {
  const LatestWidget({Key? key}) : super(key: key);

  @override
  State<LatestWidget> createState() => _LatestWidgetState();
}

class _LatestWidgetState extends State<LatestWidget>
    with AutomaticKeepAliveClientMixin {
  /// 元素列表数据
  List<CircleModel> _items = [];

  /// 页码
  int _pageIndex = 0;

  /// 刷新控制
  EasyRefreshController _controller = EasyRefreshController();
  ScrollController _scrollController = ScrollController();

  /// 最大滚动区域
  double _maxScrollExtent = 0;
  double _offset = 0;

  List<BannerModel> datas = [];

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration(milliseconds: 100), () {
      if (mounted) _controller.callRefresh();
    });

    KitSignal().link("ThumbUp", 'latest', (arg) {
      print(">>> 点赞信号处理");
      // 刷新数据
      if (arg is Map && arg.containsKey('id') && arg.containsKey('state')) {
        String id = arg["id"] as String;
        bool state = arg['state'] as bool;
        for (CircleModel model in _items) {
          if (model.id == id) {
            model.isUserLike = state;
            if (model.isUserLike == true) {
              model.likeCount += 1;
            } else {
              model.likeCount -= 1;
            }
            break;
          }
        }
        if (mounted) setState(() {});
      }
    });

    KitSignal().link("Comments", 'latest', (arg) {
      // 刷新数据
      if (arg is Map && arg.containsKey('id') && arg.containsKey('state')) {
        String id = arg["id"] as String;
        bool state = arg['state'] as bool;
        for (CircleModel model in _items) {
          if (model.id == id) {
            model.canComment = state;
            break;
          }
        }
        if (mounted) setState(() {});
      }
    });

    /// 滚动监听
    _scrollController.addListener(() {
      // print(">>>> offset:${_scrollController.offset}");
      _offset = _scrollController.offset;
      // _scrollController.to
    });

    /// 刷新
    Rx.circleRefreshSubject.listen((value) {
      if (value == 2) _controller.callRefresh();
    });
  }

  @override
  void dispose() {
    KitSignal().off("ThumbUp", 'latest');
    KitSignal().off("Comments", 'latest');

    _scrollController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF9F9F9), //Color.fromARGB(255, 213, 213, 213),
      child: NotificationListener<OverscrollNotification>(
        onNotification: (notification) {
          _maxScrollExtent = notification.metrics.maxScrollExtent;
          // notification.
          Future.delayed(Duration(milliseconds: 500), () {
            print(">>> 执行回弹 ------******** --------");
            if (_offset > _maxScrollExtent - 60) {
              _scrollController.animateTo(_maxScrollExtent - 60,
                  duration: Duration(milliseconds: 300), curve: Curves.easeIn);
            }
          });
          return false;
        },
        child: EasyRefresh.custom(
          controller: _controller,
          scrollController: _scrollController,
          header: CustomRefreshHeader(),
          footer: CustomRefreshFooter(),
          slivers: [
            /// 包装表头
            SliverToBoxAdapter(
              child: BannerWidget(
                datas: this.datas,
              ),
            ),

            SliverList(
              delegate: SliverChildBuilderDelegate(
                _itemBuilder,
                childCount: _items.length,
              ),
            ),
          ],
          shrinkWrap: true,
          onRefresh: () async {
            print("refresh");
            _loadDatas(0);
            _loadBannerDatas();
          },
          onLoad: () async {
            print("load");
            _loadDatas(_pageIndex + 1);
          },
        ),
      ),
    );
  }

  /// 列表元素
  Widget _itemBuilder(BuildContext context, int index) {
    // CircleModel model = context.watch<CircleProvider>().items[index];
    CircleModel model = _items[index];
    Widget _child = Container();

    /// 判断是否违规
    bool _isViolation = model.postStatus == 'Reported' ? true : false;
    if (_isViolation == true) {
      _child = Stack(
        children: [
          Positioned.fill(child: CircleCell(data: model)),
          Positioned.fill(
              child: Container(
                color: Colors.white38,
              )),
        ],
      );
    } else {
      _child = CircleCell(data: model);
    }

    return GestureDetector(
      onTap: () {
        Navigator.push(context, MaterialPageRoute(
          builder: (context) {
            return CircleDetailsView(
              arguments: {"id": model.id},
            );
          },
        ));
      },
      child: _child,
    );
    // return CircleCell(index: index % 3,);
  }

  /// 加载数据
  void _loadDatas(int index) async {
    try {
      Map<String, dynamic> params = {
        "pageNo": index,
        "pageSize": 10,
        "orderType": 2, // 0-默认，1-最热，2-最新
      };
      final data = await Http.get("/api/apparticle/app/list", data: params);
      final result = ResultModel<ListModel>.fromJson(data, (json) {
        return ListModel<CircleModel>.fromJson(json as Map<String, dynamic>,
                (centent) {
              return CircleModel.fromJson(centent as Map<String, dynamic>);
            });
      });
      if (result.code == 200) {
        ListModel<CircleModel> listModel =
        result.data as ListModel<CircleModel>;
        if (listModel != null) {
          if (index == 0) {
            _items = listModel.content ?? [];
            // 重置加载没有更多数据
            _controller.resetLoadState();
            // 刷新结束
            _controller.finishRefresh();
          } else {
            _items.addAll(listModel.content ?? []);
            // 刷新结束， 控制加载，如果当前的加载条目，大于总条目，显示没有更多数据
            _controller.finishLoad(
                noMore: _items.length >= listModel.totalElements);
          }
        }
        _pageIndex = index;
        if (mounted) setState(() {});
      } else {
        index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      print('>>> Error:$e');
      index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
      EasyLoading.showToast(e.message ?? "数据解析失败！");
    } catch (e) {
      print('>>> Error:$e');
      index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
      EasyLoading.showToast(e.toString());
    }
  }

  // 加载横幅数据
  void _loadBannerDatas() async {
    try {
      final data = await Http.get("/api/banner/applist");
      final result = ResultModel<List<BannerModel>?>.fromJson(data, (json) {
        if (json is List) {
          return (json as List<dynamic>?)
              ?.map((e) => BannerModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        this.datas = result.data as List<BannerModel>;
        if (mounted) setState(() {});
      } else {
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      EasyLoading.showToast(e.message ?? "网络请求失败！");
    } catch (e) {
      EasyLoading.showToast(e.toString());
    }
  }

  @override
  bool get wantKeepAlive => true;
}
