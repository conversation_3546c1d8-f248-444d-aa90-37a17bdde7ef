import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

/// 视频播放
class VideoPlayView extends StatefulWidget {

  final File? file;
  final String? url;

  const VideoPlayView({Key? key, this.file, this.url}) : super(key: key);

  @override
  State<VideoPlayView> createState() => _VideoPlayViewState();
}

class _VideoPlayViewState extends State<VideoPlayView> with WidgetsBindingObserver {

  VideoPlayerController? _controller;

  // 播放时间进度
  double _rate = 0.0;

  String _position = '00:00';
  String _duration = '00:00';

  @override
  void initState() {
    super.initState();

    // 添加观察者
    WidgetsBinding.instance?.addObserver(this);

    // 初始化
    _initVideoPlayerController();
  }

  @override
  void dispose() {
    super.dispose();
    // 销毁
    _disposeVideoPlayerController();
    // 添加观察者
    WidgetsBinding.instance?.removeObserver(this);
  }

  @override
  void didUpdateWidget(covariant VideoPlayView oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 刷新数据
    _initVideoPlayerController();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.inactive: // 不活跃（覆盖）如来电：双击home按，进入后台应用列表
        print("不活跃（覆盖）");
        if (_controller != null && _controller!.value.isPlaying)
          _controller!.pause();
        break;
      case AppLifecycleState.resumed: // 前台
        print("前台");
        break;
      case AppLifecycleState.paused: // 后台
        if (_controller != null && _controller!.value.isPlaying)
          _controller!.pause();
        print("后台");
        break;
      case AppLifecycleState.detached: // 申请将暂时停止
        if (_controller != null && _controller!.value.isPlaying)
          _controller!.pause();
        print("申请将暂时停止");
        break;
      case AppLifecycleState.hidden:
        print("AppLifecycleState.hidden");
        break;
    }
    super.didChangeAppLifecycleState(state);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefAppBar(context: context, title: "视频播放",),
      body: _bodyWidget(context),
      backgroundColor: Colors.black,
    );
  }

  /// --------------------
  /// Widgets
  /// --------------------

  /// 主体
  Widget _bodyWidget(BuildContext context) {

    // 数据不能为空
    if (widget.file == null && widget.url == null) return Center(child: Text('file 或 url 不能为空'),);

    // 是否初始化成功
    if (_controller != null && _controller!.value.isInitialized) {
      return Center(child: AspectRatio(
        aspectRatio: _controller!.value.aspectRatio,
        child: VisibilityDetector(
            key: Key("VideoPlayerKey"),
            child: Container(
              color: Colors.black,
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // 播放器
                  Center(
                    child: VideoPlayer(_controller!),
                  ),
                  // 播放按钮
                  GestureDetector(
                      onTap: () {
                        print('播放');
                        // 播放控制
                        if (_controller!.value.isPlaying == true) {
                          _controller!.pause();
                        } else {
                          _controller!.play();
                        }
                        setState(() {});
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Center(
                          child: _controller!.value.isPlaying == true
                              ? SizedBox()
                              : Icon(
                            Icons.play_circle_fill,
                            size: 54,
                            color: Colors.white,
                          ),
                        ),
                      )),
                  // 播放控制条
                  Positioned(
                    left: 0.0,
                    right: 0.0,
                    bottom: 0.0,
                    child: Container(
                      color: Colors.black12,
                      padding: EdgeInsets.only(left: 20, right: 20),
                      height: 35,
                      child: Row(
                        children: [
                          // 当前播放时间
                          Text(
                            _position,
                            style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                          SizedBox(
                            width: 15,
                          ),
                          // 播放进度
                          Expanded(
                            child: //视频进度条
                            LinearProgressIndicator(
                              backgroundColor: Color(0xFFF5F5F5),
                              value: _rate,
                              valueColor:
                              AlwaysStoppedAnimation(Color(0xFF00BFFF)),
                            ),
                          ),
                          SizedBox(
                            width: 15,
                          ),
                          // 视频时长
                          Text(
                            _duration,
                            style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            onVisibilityChanged: (value) {
              // // 更具显示区域大小判断当前控件是否离开了可见区域
              if (value.visibleBounds.size.width <= 0 ||
                  value.visibleBounds.size.height <= 0) {
                // 如果不在可见区域，暂停播放
                if (_controller != null && _controller!.value.isPlaying) {
                  _controller!.pause();
                }
              }
            }),
      ),);
    } else {
      return Center(
        child: _loadingWidget(),
      );
    }
  }

  /// 加载菊花
  Widget _loadingWidget() {
    return Container(
      child: Theme(
        data: ThemeData(
          cupertinoOverrideTheme: CupertinoThemeData(
            brightness: Brightness.dark,
          ),
        ),
        child: CupertinoActivityIndicator(
          radius: 14,
        ),
      ),
    );
  }

  /// --------------------
  /// Methods
  /// --------------------

  /// 初始化视频播放控制器
  void _initVideoPlayerController() {
    // 没有可加载资源
    if (widget.file == null && widget.url == null) return;

    // 加载视频资源
    final VideoPlayerController playerController;
    if (widget.file != null) {
      playerController = VideoPlayerController.file(widget.file!);
    } else {
      playerController = VideoPlayerController.network(widget.url!);
    }
    _controller = playerController;

    // 设置音量(取值0~1)
    _controller!.setVolume(1.0);
    // 设置循环播放
    // _controller!.setLooping(true);

    // 初始化操作
    _controller!.initialize().then((_) {
      // 视频加载完成
      print("视频加载完成");
      // 刷新页面
      if (mounted) setState(() {});
    });

    // 添加监听
    _controller!.addListener(_onVideoPlayerControllerUpdate);
  }

  /// 视频控制器更新监听
  void _onVideoPlayerControllerUpdate() {
    if (_controller != null) {
      setState(() {
        //进度条的播放进度，用当前播放时间除视频总长度得到

        _rate = _controller!.value.position.inSeconds /
            _controller!.value.duration.inSeconds;

        Duration _durt = _controller!.value.duration;
        Duration _post = _controller!.value.position;

        if (_post.inHours > 0) {
          _position =
          '${_post.inHours}:${_post.inMinutes}:${_post.inSeconds % 60}';
        } else {
          _position = '${_post.inMinutes}:${_post.inSeconds % 60}';
        }

        if (_durt.inHours > 0) {
          _duration =
          '${_durt.inHours}:${_durt.inMinutes}:${_durt.inSeconds % 60}';
        } else {
          _duration = '${_durt.inMinutes}:${_durt.inSeconds % 60}';
        }
      });
    }
  }

  /// 销毁视频控制器
  void _disposeVideoPlayerController() {
    if (_controller != null) {
      // 移除监听
      _controller!.removeListener(_onVideoPlayerControllerUpdate);
      // 释放播放器
      _controller!.dispose();
      // 置空
      _controller = null;
    }
  }
}
