import 'package:bloc/bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/utils/global.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/utils/rx.dart';
import 'package:rabbit_seat_app/utils/rx_article.dart';

import '../../models/result/result_model.dart';
import 'report_state.dart';

class ReportCubit extends Cubit<ReportState> {
  ReportCubit(String articleId) : super(ReportState().init(articleId));

  /// 加载列表数据
  void loadDatas() async {

  }

  /// 提交举报内容
  Future<void> submitReportData() async {
    try {
      if (state.item == null) {
        EasyLoading.showToast('请选择举报内容！');
        return;
      }
      EasyLoading.show();
      final Map<String, dynamic> params = {
        'articleId': state.articleId,
        'content': state.item!,
        'reportUserId': Global.profile.user!.id,
      };
      final data = await Http.post('/api/apparticle/report', data: params);
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        EasyLoading.showToast("举报成功！");
        // RxArticle.articleReportSubject.add({"id": state.articleId , "val" : true});
        Rx.articleReportSubject.add({"id": state.articleId , "val" : true});
        emit(state.clone()..enabled = false);
      } else {
        EasyLoading.showToast(result.message);
      }
    } catch (e) {
      print(">>> 提交举报内容");
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    } finally {
      return;
    }
  }

  /// 设置选中项
  void setItem(int index) {
    emit(state.clone()..item = state.datas[index]);
  }
}
