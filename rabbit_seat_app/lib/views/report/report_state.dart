class ReportState {
  // 数据源
  List<String> datas = [];
  String? item;

  // 文章ID
  late String articleId;

  // 启动
  late bool enabled;

  ReportState init(String articleId) {
    return ReportState()
      ..articleId = articleId
      ..datas = [
        '广告骚扰',
        '盗图侵权',
        '人身攻击',
        '色情低俗',
        '违法违规',
        '涉嫌诈骗',
        '其他',
      ]
      ..enabled = true;
  }

  ReportState clone() {
    return ReportState()
      ..articleId = articleId
      ..datas = datas
      ..item = item
      ..enabled = enabled;
  }
}
