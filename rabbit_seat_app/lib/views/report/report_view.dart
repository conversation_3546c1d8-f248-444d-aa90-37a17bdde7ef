import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rabbit_seat_app/business.dart';

import 'package:rabbit_seat_app/widgets/buttom/ripple_button.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';

import 'report_cubit.dart';
import 'report_state.dart';

/**
 * 圈子文章举报
 */
class ReportPage extends StatelessWidget {
  final String articleId;

  const ReportPage({Key? key, required this.articleId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => ReportCubit(articleId),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<ReportCubit>(context);

    return Scaffold(
      backgroundColor: Color(0xFFF9F9F9),
      appBar: DefAppBar(context: context, title: '举报'),
      body: BlocBuilder<ReportCubit, ReportState>(builder: (context, state) {
        return Column(
          children: [
            Expanded(
              child: Container(
                child: ListView.builder(
                  itemCount: cubit.state.datas.length,
                  itemBuilder: (_, index) {
                    String temp = cubit.state.datas[index];
                    bool _isSelected = temp == cubit.state.item;
                    return Column(
                      children: [
                        GestureDetector(
                          onTap: cubit.state.enabled
                              ? () {
                                  cubit.setItem(index);
                                }
                              : null,
                          child: Container(
                            height: 50,
                            color: Colors.white,
                            padding: EdgeInsets.only(left: 15, right: 12),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    temp,
                                    style: _isSelected
                                        ? TextStyle(
                                            fontSize: 15,
                                            fontWeight: FontWeight.w500,
                                            color: Business.mainColor,
                                          )
                                        : TextStyle(
                                            fontSize: 15,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.black,
                                          ),
                                  ),
                                ),
                                _isSelected
                                    ? Icon(
                                        Icons.check,
                                        color: Business.mainColor,
                                        size: 22,
                                      )
                                    : SizedBox(),
                              ],
                            ),
                          ),
                        ),
                        Divider(
                          indent: 15,
                          endIndent: 15,
                          height: 1,
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                  left: 30,
                  right: 30,
                  bottom: MediaQuery.of(context).padding.bottom + 20),
              child: RippleButton(
                radius: 25,
                color: Business.mainColor,
                child: Container(
                  height: 50,
                  alignment: Alignment.center,
                  child: Text(
                    "提交",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                onTap: cubit.state.enabled
                    ? () async {
                        await cubit.submitReportData();
                        Navigator.pop(context);
                      }
                    : null,
              ),
            ),
          ],
        );
      }),
    );
  }
}
