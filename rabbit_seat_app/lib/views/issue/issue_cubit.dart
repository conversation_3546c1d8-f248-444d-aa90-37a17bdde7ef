import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import '../../models/circle/circleModel.dart';
import '../../models/error.dart';
import '../../models/list/list_model.dart';
import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import '../../utils/rx_article.dart';
import 'issue_state.dart';

class IssueCubit extends Cubit<IssueState> {
  IssueCubit({Map<String, dynamic>? arguments}) : super(IssueState().init(arguments: arguments)) {
    _init();
  }

  late StreamSubscription _thumbsUpStream;
  late StreamSubscription _reportStream;
  late StreamSubscription _deleteStream;

  void _init() {
    // 点赞
    _thumbsUpStream = RxArticle.articleThumbsUpSubject.listen((value) {
      if (value is Map<String, dynamic> && value.containsKey('id') && value.containsKey("val")) {
        String id = value['id'] as String;
        bool val = value['val'] as bool;
        thumbsUpStateChangeById(id, val);
      }
    });

    // 开放评论
    _reportStream = RxArticle.articleCommentsStateSubject.where((event) => event is Map<String, dynamic>).listen((value) {
      if (value.containsKey('id') && value.containsKey("val")) {
        String id = value['id'] as String;
        bool val = value['val'] as bool;
        remarkStateChangeById(id, val);
      }
    });

    // 删除
    _deleteStream = RxArticle.articleDeleteSubject.where((event) => event is Map<String, dynamic>).listen((value) {
      if (value.containsKey('id')) {
        String id = value['id'] as String;
        deleteArticlesById(id);
      }
    });
  }


  /// 加载数据
  Future<void> loadDatas(int index) async {
    try {
      Map<String, dynamic> params = {
        "pageNo": index,
        "pageSize": 10,
        "userId": state.userId,
      };
      final data = await Http.get('/api/apparticle/app/list', data: params);
      final result = ResultModel<ListModel<CircleModel>?>.fromJson(data, (json) {
        return ListModel<CircleModel>.fromJson(json as Map<String, dynamic>,
                (jsonT) {
              return CircleModel.fromJson(jsonT as Map<String, dynamic>);
            });
      });
      if (result.code == 200) {
        ListModel? listModel = result.data as ListModel<CircleModel>?;
        if (listModel != null) {
          Map<String, CircleModel> _datas = {};
          listModel.content?.forEach((element) {
            _datas.addAll({element.id! : element});
          });
          if (index == 0) {
            state.datas = _datas;
          } else {
            state.datas.addAll(_datas);
          }
          state.pageCount = listModel.totalPages.toInt();
          state.pageIndex = index;
          emit(state.clone());
        }
      } else {
        EasyLoading.showToast(result.message);
      }
      return;
    } on MyError catch (e) {
      EasyLoading.showToast(e.message ?? '网络请求失败！');
      return;
    } catch (e) {
      EasyLoading.showToast(e.toString());
      return;
    }
  }

  /// 点赞
  Future<void> submitThumbsUp(String id) async {
    try {
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/like/$id');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        RxArticle.articleThumbsUpSubject.add({'id' : id, 'val' : true});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
      return ;
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
      return ;
    }
  }

  /// 取消点赞
  Future<void> cancelThumbUp(String id) async {
    try {
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/unlike/$id');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        // thumbsUpStateChangeById(id, false);
        RxArticle.articleThumbsUpSubject.add({'id' : id, 'val' : false});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
      return;
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
      return;
    }
  }

  /// 删除文章
  Future<void> deleteArticles(String id) async {
    try {
      EasyLoading.show();
      final data = await Http.del('/api/apparticle/$id');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        EasyLoading.showToast("文章删除成功!");
        RxArticle.articleDeleteSubject.add({"id" : id});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
      return;
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
      return;
    }
  }

  /// 开启或关闭评论
  Future<void> openOrCloseComments(String id, bool val) async {
    try {
      Map<String, dynamic> params = {
        "id": id,
        "canComment": val,
      };
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/canComment', data: params);
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        if (val == true) {
          EasyLoading.showToast("已开启评论！");
        } else {
          EasyLoading.showToast("已关闭评论！");
        }
        RxArticle.articleCommentsStateSubject.add({'id' : id, 'val' : val});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
      return;
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
      return;
    }
  }

  /// 点赞状态变化
  void thumbsUpStateChangeById(String id, bool val) {
    CircleModel? model = state.datas[id];
    if (model != null) {
      if (val == true) {
        model.likeCount += 1;
        model.isUserLike = true;
      } else {
        model.likeCount -= 1;
        model.isUserLike = false;
      }
      // 判断页面是否在显示状态
      emit(state.clone());
    }
  }

  /// 开发评论
  void remarkStateChangeById(String id, bool val) {
    CircleModel? model = state.datas[id];
    if (model != null) {
      model.canComment = val;
      // 判断页面是否在显示状态
      emit(state.clone());
    }
  }

  /// 删除文章
  void deleteArticlesById(String id) {
    if (state.datas.containsKey(id)) {
      state.datas.remove(id);
      emit(state.clone());
    }
  }

  @override
  Future<void> close() {
    // TODO: implement close

    _thumbsUpStream.cancel();
    _reportStream.cancel();
    _deleteStream.cancel();

    return super.close();
  }
}
