import 'package:rabbit_seat_app/utils/global.dart';

import '../../models/circle/circleModel.dart';

class IssueState {
  /// 样式 -1空白， 1本人，2其他
  int style = -1;

  String? userId;

  /// 数据
  Map<String, CircleModel> datas = {};

  List<CircleModel> get items => datas.values.toList();

  /// 页面
  int pageIndex = 0;
  int pageCount = 0;

  IssueState init({Map<String, dynamic>? arguments}) {
    IssueState state = IssueState();
    if (arguments != null) {
      if (arguments.containsKey('userId')) {
        state.userId = arguments['userId'] as String;
        state.style = 2;
      }
    } else {
      state.userId = Global.profile.user!.id;
      state.style = 1;
    }
    return state;
  }

  IssueState clone() {
    return IssueState()
      ..style = style
      ..userId = userId
      ..datas = datas
      ..pageIndex = pageIndex
      ..pageCount = pageCount;
  }
}
