import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:rabbit_seat_app/views/article/article_view.dart';

import '../../models/circle/circleModel.dart';
import '../../widgets/common/appbar.dart';
import '../../widgets/intercat/issue_cell.dart';
import '../../widgets/refresh/index.dart';
import '../circle/circle_details_view.dart';
import 'issue_cubit.dart';
import 'issue_state.dart';

/**
 * 发布的文章（我的发布、用户主页）
 */
class IssuePage extends StatefulWidget {
  final Map<String, dynamic>? arguments;

  const IssuePage({Key? key, this.arguments}) : super(key: key);

  @override
  State<IssuePage> createState() => _IssuePageState();
}

class _IssuePageState extends State<IssuePage>
    with AutomaticKeepAliveClientMixin {
  /// 控制器
  late EasyRefreshController _refreshController;

  @override
  void initState() {
    super.initState();

    _refreshController = EasyRefreshController();
  }

  @override
  void dispose() {
    _refreshController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => IssueCubit(arguments: widget.arguments),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<IssueCubit>(context);

    if (cubit.state.style == -1) return Center(child: Text('参数异常！'));

    Widget _bodyWidget =
        BlocBuilder<IssueCubit, IssueState>(builder: (context, state) {
      return EasyRefresh.custom(
        firstRefresh: true,
        // 没有数据组件
        emptyWidget: cubit.state.items.length == 0 ? EmptyWidget() : null,
        controller: _refreshController,
        header: CustomRefreshHeader(),
        footer: CustomRefreshFooter(),
        slivers: [
          /// 列表数据
          SliverList(
            delegate: SliverChildBuilderDelegate(
              _itemBuilder,
              childCount: cubit.state.items.length,
            ),
          ),
        ],
        shrinkWrap: true,
        onRefresh: () async {
          // 刷新
          await cubit.loadDatas(0);
          // 重置加载没有更多数据
          _refreshController.resetLoadState();
          // 刷新结束
          _refreshController.finishRefresh();
        },
        onLoad: () async {
          // 加载更多
          await cubit.loadDatas(cubit.state.pageIndex + 1);
          // 刷新结束， 控制加载，如果当前的加载条目，大于总条目，显示没有更多数据
          _refreshController.finishLoad(
              noMore: cubit.state.pageIndex >= cubit.state.pageCount);
        },
      );
    });

    return cubit.state.style == 1
        ? _bodyWidget
        : Scaffold(
            appBar: DefAppBar(context: context, title: '用户主页'),
            body: _bodyWidget,
          );
  }

  /// 列表元素项
  Widget _itemBuilder(context, index) {
    final cubit = BlocProvider.of<IssueCubit>(context);
    CircleModel model = cubit.state.items[index];
    return GestureDetector(
      onTap: () {
        Navigator.push(context, MaterialPageRoute(builder: (_) {
          return ArticlePage(
            arguments: {'id': model.id},
          );
        }));
      },
      child: IssueCell(
        data: model,
        onDeleteArticles: () async {
          await cubit.deleteArticles(model.id!);
        },
        onOnOrOffComments: () async {
          await cubit.openOrCloseComments(model.id!, !model.canComment);
        },
        onThumbUp: () async {
          if (model.isUserLike == true) {
            await cubit.cancelThumbUp(model.id!);
          } else {
            await cubit.submitThumbsUp(model.id!);
          }
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
