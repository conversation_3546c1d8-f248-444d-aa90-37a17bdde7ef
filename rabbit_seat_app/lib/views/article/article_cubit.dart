import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/models/circle/commentModel.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/utils/rx.dart';

import '../../models/circle/circleModel.dart';
import '../../models/result/result_model.dart';
import '../../utils/rx_article.dart';
import 'article_state.dart';

class ArticleCubit extends Cubit<ArticleState> {
  ArticleCubit({required String id}) : super(ArticleState().init(id)) {
    _init();
  }

  late StreamSubscription _thumbsUpStream;
  // late StreamSubscription _reportStream;
  late StreamSubscription _commentsStream;
  late StreamSubscription _deleteStream;

  /// 自定义初始化
  void _init() async {
    _loadArticleDatas();

    // 点赞
    _thumbsUpStream = RxArticle.articleThumbsUpSubject.listen((value) {
      if (value is Map<String, dynamic> && value.containsKey('id') && value.containsKey("val")) {
        String id = value['id'] as String;
        bool val = value['val'] as bool;
        thumbsUpStateChangeById(id, val);
      }
    });

    // 举报
    // _reportStream = RxArticle.articleReportSubject.listen((value) {
    //   if (value is Map<String, dynamic> && value.containsKey('id')) {
    //     String id = value['id'] as String;
    //     reportStateChangeById(id);
    //   }
    // });

    // 开放评论
    _commentsStream = RxArticle.articleCommentsStateSubject.listen((value) {
      if (value is Map<String, dynamic> && value.containsKey('id') && value.containsKey("val")) {
        String id = value['id'] as String;
        bool val = value['val'] as bool;
        remarkStateChangeById(id, val);
      }
    });

    // 删除
    _deleteStream = RxArticle.articleDeleteSubject.where((event) => event is Map<String, dynamic>).listen((value) {
      if (value.containsKey('id')) {
        String id = value['id'] as String;
        deleteArticlesById(id);
      }
    });
  }

  /// 加载数据
  Future<void> _loadArticleDatas() async {
    try {
      if (state.id.isEmpty) return;
      EasyLoading.show();
      final data = await Http.get('/api/apparticle/${state.id}');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) {
        return CircleModel.fromJson(json as Map<String, dynamic>);
      });
      if (res.code == 200) {
        emit(state.clone()..data = res.data);
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
    } catch (e) {
      print(">>> _loadArticleDatas Error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    } finally {
      return;
    }
  }

  /// 发送评论
  Future<void> sendComments(
      {required String id, required String content, String? parentId}) async {
    try {
      if (content == null || content.isEmpty) {
        EasyLoading.showToast("未编辑评论内容！");
        return;
      }
      Map<String, dynamic> params = {'content': content, 'articleId': id};
      if (parentId != null) {
        params.addAll({'parentId': parentId});
      }
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/comment', data: params);
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        EasyLoading.showToast("评论成功！");
        // 重新加载数据
        await _loadArticleDatas();
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
    }
  }

  /// 删除文章
  Future<void> deleteArticles() async {
    try {
      EasyLoading.show();
      final data = await Http.del('/api/apparticle/${state.data!.id}');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        EasyLoading.showToast("文章删除成功!");
        RxArticle.articleDeleteSubject.add({'id' : state.data!.id});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
      return ;
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
      return ;
    }
  }

  /// 删除评论
  Future<void> deleteComments(comments) async {
    try {
      EasyLoading.show();
      final data = await Http.del('/api/apparticle/comment/${comments.id}');
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        EasyLoading.showToast("删除成功！");
        // 刷新或从新加载
        await _loadArticleDatas();
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
      return ;
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
      return ;
    }
  }

  /// 开启或关闭评论
  Future<void> openOrCloseComments() async {
    try {
      bool _status = !state.data!.canComment;
      Map<String, dynamic> params = {
        "id": state.data!.id,
        "canComment": _status,
      };
      EasyLoading.show();
      final data = await Http.post('/api/apparticle/canComment', data: params);
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        if (_status == true) {
          EasyLoading.showToast("已开启评论！");
        } else {
          EasyLoading.showToast("已关闭评论！");
        }
        RxArticle.articleCommentsStateSubject.add({"id" : state.data!.id, "val" : _status});
        // 发送通知
        Rx.articleCommentsStateSubject.add({"id": state.data!.id, "state": _status});
        // 刷新页面
        emit(state.clone()..data!.canComment = _status);
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
      return;
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
      return;
    }
  }

  /// 点赞
  Future<void> submitThumbsUp() async {
    try {
      EasyLoading.show();
      bool _val = !state.data!.isUserLike;
      String _url = _val ? '/api/apparticle/like/' : '/api/apparticle/unlike/';
      final data = await Http.post(_url + state.data!.id!);
      EasyLoading.dismiss();
      final res = ResultModel.fromJson(data, (json) => json);
      if (res.code == 200) {
        // thumbsUpStateChangeById(id, true);
        RxArticle.articleThumbsUpSubject.add({'id' : state.data!.id, 'val' : _val});
      } else {
        print(res.message);
        EasyLoading.showToast(res.message);
      }
      return ;
    } catch (e) {
      print(">>> error:$e");
      EasyLoading.dismiss();
      EasyLoading.showToast("网络加载失败！");
      return ;
    }
  }

  /// 点赞状态变化
  void thumbsUpStateChangeById(String id, bool val) {
    if (state.data!.id == id) {
      if (val == true) {
        state.data!.likeCount += 1;
        state.data!.isUserLike = true;
      } else {
        state.data!.likeCount -= 1;
        state.data!.isUserLike = false;
      }
      // 判断页面是否在显示状态
      emit(state.clone());
    }
  }

  /// 举报
  void reportStateChangeById(String id) {
    if (state.data!.id == id) {
      state.data!.postStatus = 'Reported';
      // 判断页面是否在显示状态
      emit(state.clone());
    }
  }

  /// 开发评论
  void remarkStateChangeById(String id, bool val) {
    if (state.data!.id == id) {
      state.data!.canComment = val;
      // 判断页面是否在显示状态
      emit(state.clone());
    }
  }

  /// 删除文章
  void deleteArticlesById(String id) {
    // 返回主页
    state.data == null;
    // emit(state.clone());
  }


  @override
  Future<void> close() {

    print(">>> article_cubit close() ----------");

    _thumbsUpStream.cancel();
    // _reportStream.cancel();
    _commentsStream.cancel();
    _deleteStream.cancel();

    return super.close();
  }
}
