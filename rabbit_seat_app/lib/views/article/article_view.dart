import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/utils/global.dart';
import 'package:rabbit_seat_app/widgets/circle/ArticleCell.dart';
import 'package:rabbit_seat_app/widgets/circle/ArticleInfoWidget.dart';

import '../../models/circle/commentModel.dart';
import '../../widgets/circle/circle_cell.dart';
import '../../widgets/circle/overlay_text_input.dart';
import '../../widgets/circle/remark_bar.dart';
import '../../widgets/circle/remark_cell.dart';
import '../../widgets/dialog/dialog.dart';
import '../../widgets/dialog/overlay_utils.dart';
import '../issue/issue_view.dart';
import '../report/report_view.dart';
import 'article_cubit.dart';
import 'article_state.dart';

/**
 * 全向详情
 */
class ArticlePage extends StatefulWidget {
  /// 参数
  final Map<String, dynamic>? arguments;

  // final String id;
  GlobalKey _menuKey = GlobalKey();

  ArticlePage({Key? key, this.arguments}) : super(key: key);
  @override
  State<ArticlePage> createState() => _ArticlePageState();
}

class _ArticlePageState extends State<ArticlePage> {
  @override
  Widget build(BuildContext context) {
    var arguments = widget.arguments;
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    if (arguments == null || arguments.containsKey('id') == false) {
      EasyLoading.showToast('记录id不能为空！');
      return Container();
    }

    return BlocProvider(
      create: (BuildContext context) => ArticleCubit(id: arguments['id']),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<ArticleCubit>(context);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black,
            size: 18,
          ),
        ),
        backgroundColor: Colors.white,
        actions: [
          IconButton(
              key: widget._menuKey,
              onPressed: () {
                _showMenuDialog(context);
              },
              icon: const Icon(
                Icons.more_horiz_rounded,
                color: Colors.black,
              )),
        ],
        elevation: 1,
        title: const Text(
          "圈子详情",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF000000),
          ),
        ),
        centerTitle: true,
      ),
      resizeToAvoidBottomInset: false, // 底部弹出输入框，阻止界面resize
      body: BlocBuilder<ArticleCubit, ArticleState>(builder: (context, state) {
        return _bodyWidget(context);
      }),
    );
  }

  /// 限制菜单
  void _showMenuDialog(BuildContext context) {
    final cubit = BlocProvider.of<ArticleCubit>(context);

    if (cubit.state.data == null) return;

    Size _size = const Size(0, 0);
    Widget _childWidget = Container();
    // 举报
    if (cubit.state.data!.createBy!.id == Global.profile.user!.id) {
      // 自己提交的内容，可以删除
      _childWidget = Column(
        children: [
          InkWell(
            onTap: () {
              OverlayUtils.close();
              DefDialog.showDialog1(
                  context: context,
                  message: '确定删除此条圈子吗?？',
                  confirm: () async {
                    await cubit.deleteArticles();
                    Navigator.pop(context);
                  });
            },
            child: Container(
              height: 40,
              alignment: Alignment.center,
              child: Text(
                "删除",
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF000000),
                ),
              ),
            ),
          ),
          Divider(
            height: 1,
            color: Color(0xFFEEEEEE),
          ),
          InkWell(
            onTap: () {
              OverlayUtils.close();
              cubit.openOrCloseComments();
            },
            child: Container(
              height: 40,
              alignment: Alignment.center,
              child: Text(
                cubit.state.data!.canComment == true ? '关闭评论' : "开启评论",
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF000000),
                ),
              ),
            ),
          )
        ],
      );
      _size = const Size(90, 81);
    } else {
      // 游客
      _childWidget = InkWell(
        onTap: () {
          OverlayUtils.close();
          print(">>> 举报当前文章");

          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (_) => ReportPage(
                        articleId: cubit.state.data!.id!,
                      )));
        },
        child: Container(
          child: Text(
            "举报",
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: Color(0xFF000000),
            ),
          ),
        ),
      );
      _size = Size(90, 40);
    }
    // 判断是否是自己
    OverlayUtils.showMenuView(
      context: context,
      key: widget._menuKey,
      size: _size,
      child: _childWidget,
    );
  }

  /// 主体部件
  Widget _bodyWidget(BuildContext context) {
    final cubit = BlocProvider.of<ArticleCubit>(context);

    if (cubit.state.data == null) return Container();

    // 是否开放评论
    Widget _remarkWidget = Container();
    // if (_data!.canComment == true) {
    // 是否为空
    if (cubit.state.data!.comments == null ||
        cubit.state.data!.comments!.length > 0) {
      _remarkWidget = _remarkListWidget(context);
    } else {
      if (cubit.state.data!.canComment == true) {
        _remarkWidget = _emptyDataWidget('快来发表第一条评论吧！');
      } else {
        // 未开放评论
        _remarkWidget = _emptyDataWidget('用户未开放评论！');
      }
    }
    return Column(
      children: [
        Expanded(
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Container(
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 记录数据
                      ArticleInfoWidget(
                        data: cubit.state.data!,
                        portraitTap: () {
                          // 跳转用户信息页面
                          // if (cubit.state.data!.createBy != null &&
                          //     cubit.state.data!.createBy!.id != null) {
                          //   Navigator.push(
                          //     context,
                          //     MaterialPageRoute(builder: (_) {
                          //       return IssuePage(arguments: {
                          //         "userId": cubit.state.data!.createBy!.id!
                          //       });
                          //     }),
                          //   );
                          // }
                        },
                      ),
                      // 分割线
                      Container(
                        color: Color(0xFFF9F9F9),
                        height: 10.h,
                      ),
                      // 抬头
                      Container(
                        padding: EdgeInsets.only(left: 15, top: 17),
                        color: Colors.white,
                        child: Text(
                          "评论 (${cubit.state.data!.comments != null ? cubit.state.data!.comments!.length : 0})",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // 列表数据
              _remarkWidget,
            ],
          ),
        ),
        RemarkBar(
          isLike: cubit.state.data!.isUserLike,
          likeNumber: cubit.state.data!.likeCount.toInt(),
          onRemarkAction: () {
            // 判断是否开放评论
            if (cubit.state.data!.canComment) {
              OverlayTextInput.show(context, hintText: "写评论...",
                  onSubmitted: (text) {
                if (text.length > 255) {
                  EasyLoading.showToast("评论长度过长，255个字符之内");
                  return;
                }
                cubit.sendComments(id: cubit.state.data!.id!, content: text);
              });
            } else {
              EasyLoading.showToast("暂未开放评论！");
            }
          },
          onThumbUpAction: () async {
            await cubit.submitThumbsUp();
          },
        ),
      ],
    );
  }

  /// 没有数据提示部件
  Widget _emptyDataWidget(String? text) {
    return SliverToBoxAdapter(
      child: Container(
        padding: EdgeInsets.only(top: 30.h),
        alignment: Alignment.topCenter,
        child: Text(
          text ?? '',
          style: TextStyle(fontSize: 13.sp, color: Color(0xFF595959)),
        ),
      ),
    );
  }

  /// 评论列表数据
  Widget _remarkListWidget(BuildContext context) {
    final cubit = BlocProvider.of<ArticleCubit>(context);

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          CommentModel model = cubit.state.data!.comments![index];
          return RemarkCell(
            key: GlobalKey(),
            data: model,
            onShowAll: (comments) {
              setState(() {
                model.showAll = true;
              });
            },
            onTap: (comments) {
              /// 判断用户是否开启评论
              if (cubit.state.data!.canComment == true) {
                /// 评论评论
                OverlayTextInput.show(context,
                    hintText: "回复：${comments.user!.nickName}",
                    onSubmitted: (text) {
                  cubit.sendComments(
                      id: cubit.state.data!.id!,
                      content: text,
                      parentId: comments.id);
                });
              } else {
                EasyLoading.showToast("暂未开放评论！");
              }
            },
            onLongPress: (key, comments) {
              /// 文章作者删除，游客评论
              print("文章作者删除，游客评论");
              if (cubit.state.data!.createBy!.id == Global.profile.user!.id) {
                OverlayUtils.showMenuView(
                  context: context,
                  key: key,
                  size: Size(100, 50),
                  child: InkWell(
                    onTap: () {
                      print("删除某一条数据！");
                    },
                    child: Container(
                      child: Text(
                        "删除",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ),
                );
              }
            },
            addToOnTap: (comments, addToModel) {
              /// 判断用户是否开启评论
              if (cubit.state.data!.canComment == true) {
                /// 追加评论
                OverlayTextInput.show(context,
                    hintText: "回复：${addToModel.user!.nickName}",
                    onSubmitted: (text) {
                  cubit.sendComments(
                      id: cubit.state.data!.id!,
                      content: text,
                      parentId: comments.id);
                });
              } else {
                EasyLoading.showToast("暂未开放评论！");
              }
            },
            onCommentsDelTap: (comments) {
              /// 删除自己添加的评论
              DefDialog.showDialog1(
                  context: context,
                  message: '确定删除此条评论吗?？',
                  confirm: () async {
                    await cubit.deleteComments(comments);
                  });
            },
          );
        },
        childCount: cubit.state.data!.comments!.length,
      ),
    );
  }
}
