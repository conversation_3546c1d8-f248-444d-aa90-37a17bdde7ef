import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/user/index.dart';
import 'package:rabbit_seat_app/provider/user_provider.dart';

import '../../services/user_service.dart';
import '../../utils/global.dart';
import '../../utils/utils.dart';
import '../../widgets/buttom/ripple_button.dart';
import '../../widgets/common/appbar.dart';
import '../../widgets/contacts/my_button.dart';

class ModifyNameView extends StatefulWidget {
  const ModifyNameView({Key? key}) : super(key: key);

  @override
  State<ModifyNameView> createState() => _ModifyNameViewState();
}

class _ModifyNameViewState extends State<ModifyNameView> {

  late TextEditingController textEditingController;

  @override
  void initState() {
    super.initState();

    textEditingController = TextEditingController();
  }

  String? nickName;

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    // dynamic passValue = ModalRoute.of(context)?.settings.arguments;
    // textEditingController?.text = passValue["nickName"];
    // nickName = passValue["nickName"];

    nickName = context.watch<UserProvider>().user?.nickName;
    if (nickName != null && nickName!.isNotEmpty) {
      textEditingController.text = nickName!;
    }

    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: "修改昵称",
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(left: 12.w, top: 12.h, right: 12.w),
          height: 50.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.w),
            boxShadow: [
              BoxShadow(
                color: Color(0x17828282),
                offset: Offset(0, 0),
                blurRadius: 10,
                spreadRadius: 10,
              )
            ],
          ),
          padding: EdgeInsets.only(left: 15, right: 15),
          alignment: Alignment.center,
          child: TextField(
            controller: textEditingController,
            onChanged: (v) {
              nickName = v;
            },
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: '请输入昵称',
              hintStyle: const TextStyle(color: Color(0xFFAFAFAF)),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 12.w, top: 12.h, right: 12.w),
          child: Ink(
            decoration: BoxDecoration(
              color: Business.mainColor,
              borderRadius: BorderRadius.circular(50 / 2),
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(50 / 2),
              child: Container(
                height: 50,
                alignment: Alignment.center,
                child: Text(
                  "确定",
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              onTap: () async {
                _uploadUserNickName();
              },
            ),
          ),
        ),
        /*
            Container(
              width: 295.w,
              child: TextField(
                controller: textEditingController,
                onChanged: (v) {
                  nickName = v;
                },
                /// 设置输入框样式
                decoration: InputDecoration(
                  /// 边框
                  border: const OutlineInputBorder(
                    borderRadius: BorderRadius.all(
                      /// 里面的数值尽可能大才是左右半圆形，否则就是普通的圆角形
                      Radius.circular(10),
                    ),
                  ),

                  ///设置内容内边距
                  contentPadding:
                      EdgeInsets.only(top: 0, bottom: 0, left: 20.w),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.only(top: 20.h),
              alignment: Alignment.bottomCenter,
              child: MyButton(
                text: "确定",
                onTap: () async {
                  _uploadUserNickName();
                  // if (nickName == "") {
                  //   Util.showMessage(context, "提示", "昵称不能为空", () {});
                  //   return;
                  // }
                  // var user = UserModel();
                  // user.id = Global.profile.user?.id;
                  // user.nickName = nickName;
                  // var res = await UserService.updateUser(user);
                  // if (res == true) {
                  //   Navigator.of(context)
                  //       .popUntil(ModalRoute.withName("userInfo"));
                  //   UserModel myUser = UserModel();
                  //   await UserService.getUser(Global.profile.user?.id);
                  //   // eventBus.fire(getUserEvent(myUser));
                  // }
                },
                width: 265.w,
                height: 42.h,
              ),
            )
             */
      ],
    );
  }

  Widget _bodyWidget() {
    return Container(
      padding: EdgeInsets.only(left: 12, top: 12, right: 12),
      child: Column(
        children: [
          Container(
            height: 50,
            margin: EdgeInsets.only(bottom: 15),
            padding: EdgeInsets.only(left: 15, right: 15),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            child: TextField(
              onChanged: (value) {},
              style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
              maxLines: 1,
              autofocus: true,
              cursorColor: Business.mainColor,
              onSubmitted: (value) {},
              decoration: InputDecoration(
                isDense: true,
                hintText: "请输入昵称",
                hintStyle: TextStyle(
                  fontSize: 15.sp,
                  color: Color(0xFFA5A5A5),
                ),
                border: InputBorder.none,
              ),
            ),
          ),
          RippleButton(
            onTap: () {
              _uploadUserNickName();
            },
            color: Business.mainColor,
            radius: 25,
            child: Container(
              alignment: Alignment.center,
              height: 50,
              child: Text(
                "确定",
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 上传用户昵称
  void _uploadUserNickName() async {
    if (nickName == null || nickName!.isEmpty) {
      EasyLoading.showToast('请输入昵称！');
      return;
    }
    EasyLoading.show();
    UserService.updateUserInfo(
      uid: Global.profile.user!.id!,
      nickName: nickName,
      success: (data) async {
        EasyLoading.dismiss();
        EasyLoading.showToast("修改成功！");

        context.read<UserProvider>().setNickName(nickName!);

        Navigator.pop(context, true);

        // UserModel myUser = UserModel();
        // await UserService.getUser(Global.profile.user?.id);
        // eventBus.fire(getUserEvent(myUser));
      },
      failure: (msg) {
        EasyLoading.dismiss();
        EasyLoading.showToast(msg);
      },
    );
  }
}
