import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../utils/utils.dart';
import '../../widgets/common/appbar.dart';
import '../../widgets/contacts/my_button.dart';

class Step2Route extends StatefulWidget {
  const Step2Route({Key? key}) : super(key: key);

  @override
  _Step2RouteState createState() => _Step2RouteState();
}

class _Step2RouteState extends State<Step2Route> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefAppBar(context: context, title: "更换手机号"),
      body: _buildBody(),
    );
  }

  String telephone = "";

  Widget _buildBody() {
    return Container(
      padding: EdgeInsets.only(top: 25.h),
      child: Center(
        child: Column(
          children: [
            Util.getText("更换手机号后，下次可使用新手机号登录", fontSize: 14),
            Container(
              width: 265.w,
              padding: EdgeInsets.only(top: 25.h),
              child: TextField(
                keyboardType: TextInputType.phone,
                onChanged: (v) {
                  telephone = v;
                },

                /// 设置输入框样式
                decoration: InputDecoration(
                  hintText: "请输入手机号",

                  /// 边框
                  border: const OutlineInputBorder(
                    borderRadius: BorderRadius.all(
                      /// 里面的数值尽可能大才是左右半圆形，否则就是普通的圆角形
                      Radius.circular(50),
                    ),
                  ),

                  ///设置内容内边距
                  contentPadding:
                      EdgeInsets.only(top: 0, bottom: 0, left: 20.w),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.only(top: 20.h),
              alignment: Alignment.bottomCenter,
              child: MyButton(
                text: "下一步",
                onTap: () async {
                  if (telephone != "") {
                    Navigator.pushNamed(context, '/tel-step3',
                        arguments: {"telephone": telephone});
                  }
                },
                width: 265.w,
                height: 42.h,
              ),
            )
          ],
        ),
      ),
    );
  }
}
