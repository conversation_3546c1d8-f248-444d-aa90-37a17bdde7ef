import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/user/user_model.dart';
import 'package:rabbit_seat_app/services/token_service.dart';

import '../../services/user_service.dart';
import '../../utils/global.dart';
import '../../utils/utils.dart';
import '../../widgets/common/appbar.dart';
import '../../widgets/contacts/my_button.dart';


class Step3Route extends StatefulWidget {
  const Step3Route({Key? key}) : super(key: key);

  @override
  _Step3RouteState createState() => _Step3RouteState();
}

class _Step3RouteState extends State<Step3Route> {
  String telephone = "";
  @override
  Widget build(BuildContext context) {
    dynamic passValue = ModalRoute.of(context)?.settings.arguments;
    textEditingController?.text = passValue["telephone"];
    telephone = passValue["telephone"];
    return Scaffold(
      appBar: DefAppBar(context: context, title: "更换手机号"),
      body: _buildBody(),
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    textEditingController = TextEditingController();
  }

  @override
  void dispose() {
    // 组件销毁时判断Timer是否仍然处于激活状态，是则取消
    if (timer?.isActive == true) {
      timer?.cancel();
    }
    super.dispose();
  }

  TextEditingController? textEditingController;

  String timeText = "获取验证码";
  Timer? timer;
  num time = 60;
  String code = "";


  Widget _buildBody1() {
    return Container(
      padding: EdgeInsets.only(top: 35.h),
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image(
              image: const AssetImage('assets/images/my/step1.png'),
              width: 135.w,
              height: 110.h,
            ),
            Container(
                padding: EdgeInsets.only(top: 10.h),
                child: Util.getText("为了账号安全，请验证身份")),
            Container(
              width: 265.w,
              padding: EdgeInsets.only(top: 50.h),
              child: TextField(
                controller: textEditingController,
                readOnly: true,

                /// 设置输入框样式
                decoration: InputDecoration(
                  /// 边框
                  border: const OutlineInputBorder(
                    borderRadius: BorderRadius.all(
                      /// 里面的数值尽可能大才是左右半圆形，否则就是普通的圆角形
                      Radius.circular(50),
                    ),
                  ),

                  ///设置内容内边距
                  contentPadding:
                      EdgeInsets.only(top: 0, bottom: 0, left: 20.w),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.only(top: 15.h),
              child:
                  Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                Container(
                  width: 152.w,
                  child: TextField(
                    onChanged: (v) {
                      code = v;
                    },

                    /// 设置输入框样式
                    decoration: InputDecoration(
                      hintText: "验证码",

                      /// 边框
                      border: const OutlineInputBorder(
                        borderRadius: BorderRadius.all(
                          /// 里面的数值尽可能大才是左右半圆形，否则就是普通的圆角形
                          Radius.circular(50),
                        ),
                      ),

                      ///设置内容内边距
                      contentPadding:
                          EdgeInsets.only(top: 0, bottom: 0, left: 20.w),
                    ),
                  ),
                ),
                Padding(
                    padding: EdgeInsets.only(left: 13.w),
                    child: InkWell(
                      onTap: () async {
                        if (time != 60) {
                          return;
                        }
                        TokenService.sendSMSCode(phone: telephone, success: (data) {
                          timer = Timer.periodic(
                                  const Duration(milliseconds: 1000), (_timer) {
                                time--;
                                setState(() {
                                  timeText = time.toString() + "s";
                                  if (time == 0) {
                                    timeText = "获取验证码";
                                    time = 60;
                                    _timer.cancel();
                                  }
                                });
                              });
                        }, failure: (msg) {});
                        // await TokenService.sendSmsCode(telephone);
                        // timer = Timer.periodic(
                        //     const Duration(milliseconds: 1000), (_timer) {
                        //   time--;
                        //   setState(() {
                        //     timeText = time.toString() + "s";
                        //     if (time == 0) {
                        //       timeText = "获取验证码";
                        //       time = 60;
                        //       _timer.cancel();
                        //     }
                        //   });
                        // });
                      },
                      child: Util.getBox(
                          100.w,
                          42.h,
                          Colors.transparent,
                          30.w,
                          Util.getText(timeText,
                              fontSize: 13, color: Business.mainColor),
                          borderColor: Business.mainColor,
                          borderWidth: 0.5.w),
                    ))
              ]),
            ),
            Container(
              padding: EdgeInsets.only(top: 13.h),
              alignment: Alignment.bottomCenter,
              child: MyButton(
                text: "下一步",
                onTap: () async {
                  TokenService.verifySMSCode(phone: telephone, code: code, success: (data) async {
                    if (data == code) {
                      var modifyUser = UserModel();
                      modifyUser.telephone = telephone;
                      modifyUser.id = Global.profile.user?.id;
                      var res = await UserService.updateUser(modifyUser);
                      if (res == true) {
                        Navigator.of(context)
                            .popUntil(ModalRoute.withName("/user-info"));
                        UserModel myUser = UserModel();
                        // eventBus.fire(getUserEvent(myUser));
                      }
                    }
                  }, failure: (msg) {
                    // EasyLoading.showToast(msg);
                    Util.showMessage(context, "提示", "验证码错误", () {});
                  });
                  // var res = await TokenService.verifyCode(telephone, code);
                  // if (res == null) {
                  //   Util.showMessage(context, "提示", "验证码错误", () {});
                  //   return;
                  // }
                  // if (res.code == code) {
                  //   var modifyUser = User();
                  //   modifyUser.telephone = telephone;
                  //   modifyUser.id = Global.profile.user?.id;
                  //   var res = await UserService.updateUser(modifyUser);
                  //   if (res == true) {
                  //     Navigator.of(context)
                  //         .popUntil(ModalRoute.withName("userInfo"));
                  //     User myUser = User();
                  //     eventBus.fire(getUserEvent(myUser));
                  //   }
                  // }
                },
                width: 265.w,
                height: 42.h,
              ),
            )
          ],
        ),
      ),
    );
  }


  Widget _buildBody() {
    return Container(
      padding: EdgeInsets.only(left: 30.w, right: 20.w),
      child: ListView(
        children: [
          Container(
            alignment: Alignment.center,
            margin: EdgeInsets.only(top: 59),
            child: Image(
              image: const AssetImage('assets/images/my/step1.png'),
              width: 135.w,
              height: 110.h,
            ),
          ),
          Container(
              alignment: Alignment.center,
              padding: EdgeInsets.only(top: 12.h),
              child: Util.getText("为了账号安全，请验证身份")),
          // 手机号
          Container(
            margin: EdgeInsets.only(top: 50.h),
            height: 50.h,
            decoration: BoxDecoration(
              border: Border.all(color: Color(0xFFE1E1E1), width: 1),
              borderRadius: BorderRadius.circular(50.h / 2),
            ),
            padding: EdgeInsets.only(left: 30.w, right: 30.w),
            alignment: Alignment.center,
            child: TextField(
              controller: textEditingController,
              readOnly: true,

              /// 设置输入框样式
              decoration: InputDecoration(
                border: InputBorder.none,
              ),
            ),
          ),
          // 验证码
          Padding(
            padding: EdgeInsets.only(top: 15.h),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: 50.h,
                    decoration: BoxDecoration(
                      border: Border.all(color: Color(0xFFFFE1E1E1), width: 1),
                      borderRadius: BorderRadius.circular(50.h / 2),
                    ),
                    padding: EdgeInsets.only(left: 30.w, right: 30.w),
                    alignment: Alignment.center,
                    child: TextField(
                      onChanged: (v) {
                        code = v;
                      },

                      /// 设置输入框样式
                      decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: "验证码",
                          hintStyle: TextStyle(
                              fontSize: 15.sp, color: Color(0xFFAFAFAF))),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(left: 13.w),
                  height: 50.h,
                  decoration: BoxDecoration(
                    border: Border.all(color: Business.mainColor, width: 1),
                    borderRadius: BorderRadius.circular(50.h / 2),
                  ),
                  alignment: Alignment.center,
                  width: 120.w,
                  child: InkWell(
                    onTap: () async {
                      Util.removePrimaryFocus(context);
                      if (time != 60) {
                        return;
                      }
                      TokenService.sendSMSCode(phone: telephone, success: (data) {
                        timer = Timer.periodic(
                            const Duration(milliseconds: 1000), (_timer) {
                          time--;
                          setState(() {
                            timeText = time.toString() + "s";
                            if (time == 0) {
                              timeText = "获取验证码";
                              time = 60;
                              _timer.cancel();
                            }
                          });
                        });
                      }, failure: (msg) {});
                    },
                    child: Text(
                      timeText,
                      style:
                      TextStyle(fontSize: 15.sp, color: Business.mainColor),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 下一步
          GestureDetector(
            onTap: () {
              Util.removePrimaryFocus(context);
              TokenService.verifySMSCode(phone: telephone, code: code, success: (data) async {
                if (data == code) {
                  var modifyUser = UserModel();
                  modifyUser.telephone = telephone;
                  modifyUser.id = Global.profile.user?.id;
                  var res = await UserService.updateUser(modifyUser);
                  if (res == true) {
                    print(">>>>> 返回到指定页面");
                    Navigator.popUntil(context, (route) => route.settings.name == '/user-info');
                    // Navigator.of(context)
                    //     .popUntil(ModalRoute.withName("/user-info"));
                    UserModel myUser = UserModel();
                    // eventBus.fire(getUserEvent(myUser));
                  }
                }
              }, failure: (msg) {
                // EasyLoading.showToast(msg);
                Util.showMessage(context, "提示", "验证码错误", () {});
              });
            },
            child: Container(
              height: 50.h,
              decoration: BoxDecoration(
                color: Business.mainColor,
                borderRadius: BorderRadius.circular(50.h/2),
              ),
              margin: EdgeInsets.only(top: 13.h),
              alignment: Alignment.center,
              child: Text(
                '下一步',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
