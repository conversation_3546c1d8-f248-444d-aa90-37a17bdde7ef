import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cupertino_datetime_picker/flutter_cupertino_datetime_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/provider/user_provider.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';

import '../../models/user/baby_model.dart';
import '../../models/user/user_model.dart';
import '../../services/user_service.dart';
import '../../utils/global.dart';
import '../../utils/utils.dart';
import '../../widgets/baby/select_sex_status.dart';
import '../../widgets/contacts/my_button.dart';

class BabyAddView extends StatefulWidget {
  const BabyAddView({Key? key}) : super(key: key);

  @override
  State<BabyAddView> createState() => _BabyAddViewState();
}

class _BabyAddViewState extends State<BabyAddView> {
  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: "添加宝宝",
      ),
      body: _buildBody(),
    );
  }

  bool manActive = true;
  bool feMaleActive = false;
  String birthDay = "请选择生日";
  String nickName = "";
  DateTime selectTime = DateTime.now();

  Widget _buildBody() {
    return Container(
      padding: EdgeInsets.only(left: 12, right: 12),
      child: Center(
        child: ListView(
          children: [
            Container(
              // width: 330.w,
              margin: EdgeInsets.only(top: 10.h),
              padding: EdgeInsets.only(
                  top: 20.h, left: 16.w, bottom: 20.h, right: 16.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Util.getText("请选择宝宝性别", fontSize: 13),
                  Padding(
                    padding: EdgeInsets.only(top: 10.h),
                    child: Divider(
                      height: 1.h,
                      color: const Color.fromARGB(255, 217, 215, 215),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 20.h),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                            onTap: () {
                              setState(() {
                                feMaleActive = true;
                                manActive = false;
                              });
                            },
                            child: Container(
                              child: SelectSexStatus(
                                text: "女宝",
                                active: feMaleActive,
                                deActiveImage: "assets/images/baby/1.png",
                                activeImage: "assets/images/baby/4.png",
                                activeColor: const Color(0xFFF67866),
                              ),
                            )),
                        InkWell(
                          onTap: () {
                            setState(() {
                              feMaleActive = false;
                              manActive = true;
                            });
                          },
                          child: Container(
                            margin: EdgeInsets.only(left: 42.w),
                            child: SelectSexStatus(
                              text: "男宝",
                              active: manActive,
                              deActiveImage: "assets/images/baby/3.png",
                              activeImage: "assets/images/baby/2.png",
                              activeColor: const Color(0xFF0073F4),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
              decoration: Util.getBoxDecoration(),
            ),
            Container(
              width: 330.w,
              margin: EdgeInsets.only(top: 10.h),
              padding: EdgeInsets.only(
                  top: 20.h, left: 16.w, bottom: 20.h, right: 16.w),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Util.getText("昵称", fontSize: 13),
                      Container(
                        width: 150.w,
                        padding: EdgeInsets.only(right: 24.w),
                        child: TextField(
                          textAlign: TextAlign.end,
                          style: const TextStyle(
                              fontSize: 13, color: Color(0xFFAFAFAF)),
                          onChanged: (v) {
                            nickName = v;
                          },

                          /// 设置输入框样式
                          decoration: const InputDecoration(
                              border: InputBorder.none,
                              hintText: "请输入昵称",
                              hintStyle: TextStyle(color: Color(0xFFAFAFAF))),
                        ),
                      )
                    ],
                  ),
                  Container(
                    child: Divider(
                      height: 1.h,
                      color: const Color.fromARGB(255, 217, 215, 215),
                    ),
                  ),
                  InkWell(
                      onTap: () {
                        DatePicker.showDatePicker(context,
                            minDateTime: DateTime.fromMillisecondsSinceEpoch(
                                1262275200000),
                            maxDateTime: DateTime.now(),
                            initialDateTime: selectTime,
                            locale: DateTimePickerLocale.zh_cn, onConfirm:
                                (DateTime dateTime, List<int> selectedIndex) {
                          selectTime = dateTime;
                          setState(() {
                            birthDay =
                                formatDate(dateTime, [yyyy, '-', mm, '-', dd]);
                          });
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.only(top: 20.h),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Util.getText("生日", fontSize: 13),
                            Row(
                              children: [
                                Util.getText(birthDay,
                                    fontSize: 13,
                                    color: const Color(0xFFAFAFAF)),
                                Container(
                                  padding: EdgeInsets.only(left: 15.w),
                                  child: Image(
                                      image: const AssetImage(
                                          'assets/images/my/1.png'),
                                      width: 8.w),
                                )
                              ],
                            )
                          ],
                        ),
                      )),
                ],
              ),
              decoration: Util.getBoxDecoration(),
            ),
            Container(
              padding: EdgeInsets.only(top: 20.h),
              alignment: Alignment.bottomCenter,
              child: MyButton(
                text: "添加宝宝",
                onTap: () async {
                  _addBabyInfos();
                },
                width: 295.w,
                height: 42.h,
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 添加宝宝
  void _addBabyInfos() async {
    try {
      EasyLoading.show();
      Map<String, dynamic> params = {
        'birthday': birthDay,
        'sex': (feMaleActive ? "Female" : "Male"),
        'name': nickName,
      };
      if (birthDay == "请选择生日") {
        EasyLoading.showToast('请选择生日');
        return;
      }
      if (nickName == "") {
        EasyLoading.showToast('请输入昵称');
        return;
      }
      final data = await Http.post(
          '/api/appuser/babyinfo/${Global.profile.user!.id}',
          data: params);
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        EasyLoading.showToast('添加成功！');
        // 加载用户数据
        _loadUserInfo();
      } else {
        EasyLoading.showToast(result.message);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }

  /// 加载用户信息
  void _loadUserInfo() async {
    try {
      EasyLoading.show();
      final data =
          await Http.get("/api/appuser/user/${Global.profile.user!.id}");
      final result = ResultModel<UserModel?>.fromJson(
          data, (json) => UserModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (result.data != null) {
          context.read<UserProvider>().user = result.data as UserModel;
        }
        // 返回页面
        Navigator.pop(context);
      } else {
        EasyLoading.showToast(result.message);
      }
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}
