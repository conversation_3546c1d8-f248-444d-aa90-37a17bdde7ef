import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cupertino_datetime_picker/flutter_cupertino_datetime_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/user/baby_model.dart';
import 'package:rabbit_seat_app/models/user/index.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';

import '../../models/result/result_model.dart';
import '../../provider/user_provider.dart';
import '../../services/user_service.dart';
import '../../utils/global.dart';
import '../../utils/http.dart';
import '../../utils/utils.dart';
import '../../widgets/baby/select_sex_status.dart';
import '../../widgets/contacts/my_button.dart';

class BadyModifyView extends StatefulWidget {
  const BadyModifyView({Key? key}) : super(key: key);

  @override
  State<BadyModifyView> createState() => _BadyModifyViewState();
}

class _BadyModifyViewState extends State<BadyModifyView> {
  BabyModel? baby;
  TextEditingController? controller;
  bool manActive = true;
  bool feMaleActive = false;
  String birthDay = "请选择生日";
  String nickName = "";
  DateTime? selectTime;
  bool isInit = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    controller = TextEditingController();
    isInit = false;
  }
  

  void initData(BabyModel baby) {
    if (baby.birthday != "请选择生日") {
      selectTime = DateTime.tryParse(baby.birthday!);
    }
    birthDay = baby.birthday!;
    nickName = baby.name!;
    controller?.text = nickName;
    manActive = baby.sex == "Male";
    feMaleActive = !manActive;
  }

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    return Scaffold(
      appBar: DefAppBar(context: context, title: "修改宝宝信息"),
      body: _buildBody(),
      resizeToAvoidBottomInset: false,
    );
  }

  Widget _buildBody() {
    if (!isInit) {
      // 加载宝贝信息
      baby = context.watch<UserProvider>().editBaby;
      // 初始化数据
      initData(baby!);
      isInit = true;
    }

    // 没有数据
    if (baby == null)
      return Container(
        alignment: Alignment.center,
        child: Text('无编辑对象！'),
      );

    return Stack(children: [
      Center(
        child: Column(
          children: [
            Container(
              width: 330.w,
              margin: EdgeInsets.only(top: 10.h),
              padding: EdgeInsets.only(
                  top: 20.h, left: 16.w, bottom: 20.h, right: 16.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Util.getText("请选择宝宝性别", fontSize: 13),
                  Padding(
                    padding: EdgeInsets.only(top: 10.h),
                    child: Divider(
                      height: 1.h,
                      color: const Color.fromARGB(255, 217, 215, 215),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 20.h),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                            onTap: () {
                              setState(() {
                                feMaleActive = true;
                                manActive = false;
                              });
                            },
                            child: SelectSexStatus(
                              text: "女宝",
                              active: feMaleActive,
                              deActiveImage: "assets/images/baby/1.png",
                              activeImage: "assets/images/baby/4.png",
                              activeColor: const Color(0xFFF67866),
                            )),
                        InkWell(
                          onTap: () {
                            setState(() {
                              feMaleActive = false;
                              manActive = true;
                            });
                          },
                          child: Container(
                            margin: EdgeInsets.only(left: 42.w),
                            child: SelectSexStatus(
                              text: "男宝",
                              active: manActive,
                              deActiveImage: "assets/images/baby/3.png",
                              activeImage: "assets/images/baby/2.png",
                              activeColor: const Color(0xFF0073F4),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
              decoration: Util.getBoxDecoration(),
            ),
            Container(
              width: 330.w,
              margin: EdgeInsets.only(top: 10.h),
              padding: EdgeInsets.only(
                  top: 20.h, left: 16.w, bottom: 20.h, right: 16.w),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Util.getText("昵称", fontSize: 14),
                      Container(
                        width: 150.w,
                        padding: EdgeInsets.only(right: 24.w),
                        child: TextField(
                          textAlign: TextAlign.end,
                          controller: controller,
                          style: TextStyle(
                              fontSize: 14.sp, color: const Color(0xFFAFAFAF)),
                          onChanged: (v) {
                            nickName = v;
                          },

                          /// 设置输入框样式
                          decoration: const InputDecoration(
                              border: InputBorder.none,
                              hintText: "请输入昵称",
                              hintStyle: TextStyle(color: Color(0xFFAFAFAF))),
                        ),
                      )
                    ],
                  ),
                  Container(
                    child: Divider(
                      height: 1.h,
                      color: const Color.fromARGB(255, 217, 215, 215),
                    ),
                  ),
                  InkWell(
                      onTap: () {
                        DatePicker.showDatePicker(context,
                            minDateTime: DateTime.fromMillisecondsSinceEpoch(
                                1262275200000),
                            maxDateTime: DateTime.now(),
                            initialDateTime: selectTime,
                            locale: DateTimePickerLocale.zh_cn, onConfirm:
                                (DateTime dateTime, List<int> selectedIndex) {
                          setState(() {
                            selectTime = dateTime;
                            birthDay =
                                formatDate(dateTime, [yyyy, '-', mm, '-', dd]);
                            // DateFormat('yyyy-MM-dd').format(dateTime);
                          });
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.only(top: 20.h),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Util.getText("生日", fontSize: 14),
                            Row(
                              children: [
                                Util.getText(birthDay,
                                    fontSize: 13,
                                    color: const Color(0xFFAFAFAF)),
                                Container(
                                  padding: EdgeInsets.only(left: 15.w),
                                  child: Image(
                                      image: const AssetImage(
                                          'assets/images/my/1.png'),
                                      width: 8.w),
                                )
                              ],
                            )
                          ],
                        ),
                      )),
                ],
              ),
              decoration: Util.getBoxDecoration(),
            ),
          ],
        ),
      ),
      Container(
        padding: EdgeInsets.only(bottom: 40.h),
        alignment: Alignment.bottomCenter,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            MyButton(
              text: "保存",
              onTap: () async {
                _updateBabyInfos();
              },
              width: 295.w,
              height: 42.h,
            ),
            Container(
              margin: EdgeInsets.only(top: 12.h),
              child: InkWell(
                onTap: () async {
                  _deleteBabayInfos();
                },
                child: Util.getBox(
                    295.w,
                    42.h,
                    const Color.fromARGB(255, 253, 253, 253),
                    50.w,
                    Util.getText("删除",
                        color: Business.mainColor, fontSize: 18),
                    borderColor: Business.mainColor,
                    borderWidth: 0.5.w),
              ),
            )
          ],
        ),
      )
    ]);
  }

  /// 删除宝宝
  void _deleteBabayInfos() async {
    try {
      EasyLoading.show();
      Map<String, dynamic> params = {
        'name': baby!.name,
        'sex': baby!.sex,
        'birthday': baby!.birthday,
        'id': baby!.id,
      };
      final data = await Http.del(
          '/api/appuser/babyinfo/${Global.profile.user!.id}',
          data: params);
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        EasyLoading.showToast('删除成功！');
        context.read<UserProvider>().removeBabyInfo(baby!);
        Navigator.pop(context);
      } else {
        EasyLoading.showToast(result.message);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }

  /// 修改宝宝
  void _updateBabyInfos() async {
    try {
      EasyLoading.show();
      Map<String, dynamic> params = baby!.toJson();
      params['name'] = nickName;
      params['birthday'] = birthDay;
      final data = await Http.put(
          '/api/appuser/babyinfo/${Global.profile.user!.id}',
          data: params);
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        EasyLoading.showToast('保存成功！');
        // 加载用户数据
        context.read<UserProvider>().updateBabyInfo(
            baby!.id!, nickName, (feMaleActive ? "Female" : "Male"), birthDay);
        Navigator.pop(context);
      } else {
        EasyLoading.showToast(result.message);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}
