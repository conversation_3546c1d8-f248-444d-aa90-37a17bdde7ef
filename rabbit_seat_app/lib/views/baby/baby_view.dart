import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/models/user/baby_model.dart';
import 'package:rabbit_seat_app/provider/user_provider.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';

import '../../utils/global.dart';
import '../../widgets/contacts/my_box_edit.dart';
import '../../widgets/contacts/my_button.dart';

class BabyView extends StatefulWidget {
  const BabyView({Key? key}) : super(key: key);

  @override
  State<BabyView> createState() => _BabyViewState();
}

class _BabyViewState extends State<BabyView> {
  List<BabyModel> list = [];

  // StreamSubscription? subEvents;

  @override
  void dispose() {
    super.dispose();
    // subEvents?.cancel();
  }

  @override
  void initState() {
    super.initState();
    // init();
    // subEvents = eventBus.on<getUserEvent>().listen((event) {
    //   init();
    // });
  }

  // init() {
  //   setState(() {
  //     list = Global.profile.user?.babyInfos as List<BabyModel>;
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    // 加载宝宝信息
    list = context.watch<UserProvider>().user?.babyInfos ?? [];

    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: "宝宝信息",
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Stack(
      children: [
        Center(
          child: Column(
            children: list
                .map((m) => MyBoxEdit(
                      title: m.name!,
                      subTitle: m.birthday,
                      top: 10.h,
                      headerImage: m.sex == 'Male'
                          ? 'assets/images/baby/2.png'
                          : 'assets/images/baby/4.png',
                      // headerIcon: Icon(
                      //   m.sex == 'Male' ? Icons.male : Icons.female,
                      //   size: 18.sp,
                      //   color: m.sex == 'Male'
                      //       ? const Color(0xFF468DDD)
                      //       : const Color(0xFFF67866),
                      // ),
                      headerIcon: Padding(padding: EdgeInsets.only(left: 4), child: SizedBox(
                        width: 16,
                        height: 16,
                        child: Image.asset(
                          m.sex == 'Male'
                              ? 'assets/images/baby/6.png'
                              : 'assets/images/baby/5.png',
                        ),
                      ),),
                      onTap: () {
                        context.read<UserProvider>().setEditBaby(m);
                        Navigator.pushNamed(context, '/bady-info');
                      },
                    ))
                .toList(),
          ),
        ),
        Container(
          padding: EdgeInsets.only(bottom: 35.h),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: MyButton(
                text: "添加宝宝",
                width: 295.w,
                height: 42.h,
                icon: const Icon(
                  Icons.add,
                  color: Colors.white,
                ),
                onTap: () {
                  Navigator.pushNamed(context, '/bady-add');
                }),
          ),
        )
      ],
    );
  }
}
