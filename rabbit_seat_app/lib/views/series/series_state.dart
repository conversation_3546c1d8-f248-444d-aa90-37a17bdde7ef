import 'package:rabbit_seat_app/models/circle/banner.dart';
import 'package:rabbit_seat_app/models/circle/circleModel.dart';

class SeriesState {
  int pageIndex = 0;
  int pageCount = 0;
  int itemCount = 0;

  Map<String, CircleModel> datas = {};

  List<CircleModel> get items => datas.values.toList();
  List<BannerModel> banners = [];

  SeriesState init() {
    return SeriesState()..pageIndex = 0;
  }

  SeriesState clone() {
    return SeriesState()
      ..pageIndex = pageIndex
      ..datas = datas
      ..banners = banners;
  }
}
