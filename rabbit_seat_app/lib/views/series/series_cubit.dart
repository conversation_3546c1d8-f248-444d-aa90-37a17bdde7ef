import 'package:bloc/bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/utils/rx.dart';

import '../../models/circle/banner.dart';
import '../../models/circle/circleModel.dart';
import '../../models/list/list_model.dart';
import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import 'series_state.dart';

class SeriesCubit extends Cubit<SeriesState> {
  SeriesCubit() : super(SeriesState().init()) {
    _init();
  }

  void _init() async {
    // 点赞
    Rx.articleThumbsUpSubject.where((event) => (event as Map<String, dynamic>).isNotEmpty).listen((event) {
      // {'id' : state.data!.id, 'val': !_isLike}
      if (event is Map<String, dynamic> && event.containsKey('id') && event.containsKey('val')) {
        String _id = event['id'] as String;
        bool _val = event['val'] as bool;
        if (state.datas.containsKey(_id)) {
          CircleModel _model = state.datas[_id] as CircleModel;
          _model.isUserLike = _val;
          if (_val) {
            _model.likeCount+=1;
          } else {
            _model.likeCount-=1;
          }
          emit(state.clone());
        }
      }
    });

    // 评论
    Rx.articleCommentsStateSubject.where((event) => (event as Map<String, dynamic>).isNotEmpty).listen((event) {
      // {"id": state.data!.id, "state": _status}
      if (event is Map<String, dynamic> && event.containsKey('id') && event.containsKey('state')) {
        String _id = event['id'] as String;
        bool _state = event['state'] as bool;
        if (state.datas.containsKey(_id)) {
          CircleModel _model = state.datas[_id] as CircleModel;
          _model.canComment = _state;
          emit(state.clone());
        }
      }
    });

    // 刷新页面
    Rx.articleRefreshSubject.where((event) => event).listen((event) {
      // 刷新页面
    });
  }

  /// 加载数据
  Future<void> loadDatas(int index) async {
    try {
      Map<String, dynamic> params = {
        "pageNo": index,
        "pageSize": 10,
        "orderType": 0, // 0-默认，1-最热，2-最新
      };
      final data = await Http.get("/api/apparticle/app/list", data: params);
      final result = ResultModel<ListModel>.fromJson(data, (json) {
        return ListModel<CircleModel>.fromJson(json as Map<String, dynamic>, (centent) {
          return CircleModel.fromJson(centent as Map<String, dynamic>);
        });
      });
      if (result.code == 200) {
        ListModel<CircleModel>? listModel = result.data as ListModel<CircleModel>?;
        state.pageIndex = index;
        if (listModel != null) {
          state.pageCount = listModel.totalPages.toInt();
          state.itemCount = listModel.totalElements.toInt();

          Map<String, CircleModel> _datas = {};
          (listModel.content as List<CircleModel>?)?.forEach((element) {
            _datas.addAll({element.id! : element});
          });
          if (index == 0) {
            emit(state.clone()..datas = _datas);
          } else {
            Map<String, CircleModel> _temp = state.datas;
            _temp.addAll(_datas);
            emit(state.clone()..datas = _temp);
          }
        }
      } else {
        EasyLoading.showToast(result.message);
      }
      return;
    } catch (e) {
      print('>>> Error:$e');
      EasyLoading.showToast(e.toString());
      return;
    }
  }

  // 加载横幅数据
  Future<void>  loadBannerDatas() async {
    try {
      final data = await Http.get("/api/banner/applist");
      final result = ResultModel<List<BannerModel>?>.fromJson(data, (json) {
        if (json is List) {
          return (json as List<dynamic>?)
              ?.map((e) => BannerModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        emit(state.clone()..banners = result.data as List<BannerModel>);
      } else {
        EasyLoading.showToast(result.message);
      }
      return;
    } catch (e) {
      EasyLoading.showToast(e.toString());
      return;
    }
  }
}
