import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

import '../../models/circle/circleModel.dart';
import '../../widgets/circle/banner_widget.dart';
import '../../widgets/circle/circle_cell.dart';
import '../../widgets/refresh/index.dart';
import '../article/article_view.dart';
import 'series_cubit.dart';
import 'series_state.dart';

// class SeriesPage extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return BlocProvider(
//       create: (BuildContext context) => SeriesCubit(),
//       child: Builder(builder: (context) => _buildPage(context)),
//     );
//   }
//
//   Widget _buildPage(BuildContext context) {
//     final cubit = BlocProvider.of<SeriesCubit>(context);
//
//     return Container();
//   }
// }

class SeriesPage extends StatefulWidget {
  const SeriesPage({Key? key}) : super(key: key);

  @override
  State<SeriesPage> createState() => _SeriesPageState();
}

class _SeriesPageState extends State<SeriesPage>
    with AutomaticKeepAliveClientMixin {
  /// 刷新控制
  EasyRefreshController _controller = EasyRefreshController();

  @override
  void initState() {
    super.initState();

    // Future.delayed(Duration(milliseconds: 100), () {
    //   if (mounted) _controller.callRefresh();
    // });

    // KitSignal().link("ThumbUp", 'circle', (arg) {
    //   print(">>> 点赞信号处理");
    //   // 刷新数据
    //   if (arg is Map && arg.containsKey('id') && arg.containsKey('state')) {
    //     String id = arg["id"] as String;
    //     bool state = arg['state'] as bool;
    //     for (CircleModel model in _items) {
    //       if (model.id == id) {
    //         model.isUserLike = state;
    //         if (model.isUserLike == true) {
    //           model.likeCount += 1;
    //         } else {
    //           model.likeCount -= 1;
    //         }
    //         break;
    //       }
    //     }
    //     if (mounted) setState(() {});
    //   }
    // });

    // KitSignal().link("Comments", 'circle', (arg) {
    //   // 刷新数据
    //   if (arg is Map && arg.containsKey('id') && arg.containsKey('state')) {
    //     String id = arg["id"] as String;
    //     bool state = arg['state'] as bool;
    //     for (CircleModel model in _items) {
    //       if (model.id == id) {
    //         model.canComment = state;
    //         break;
    //       }
    //     }
    //     if (mounted) setState(() {});
    //   }
    // });

    // KitSignal().link("publish", 'circle', (arg) {
    //   // 刷新数据
    //   _controller.callRefresh();
    // });
  }

  @override
  void dispose() {
    // KitSignal().off("ThumbUp", 'circle');
    // KitSignal().off("Comments", 'circle');
    // KitSignal().off("publish", 'circle');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => SeriesCubit(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<SeriesCubit>(context);

    return BlocBuilder<SeriesCubit, SeriesState>(builder: (context, state) {
     return Container(
       color: Color(0xFFF9F9F9), //Color.fromARGB(255, 213, 213, 213),
       child: EasyRefresh.custom(
         controller: _controller,
         header: CustomRefreshHeader(),
         footer: CustomRefreshFooter(),
         slivers: [
           /// 包装表头
           SliverToBoxAdapter(
             child: BannerWidget(
               datas: cubit.state.banners,
             ),
           ),
           SliverList(
             delegate: SliverChildBuilderDelegate(
               _itemBuilder,
               childCount: cubit.state.items.length,
             ),
           ),
         ],
         shrinkWrap: true,
         onRefresh: () async {
           print("refresh");
           await cubit.loadBannerDatas();
           await cubit.loadDatas(0);


           // 重置加载没有更多数据
           _controller.resetLoadState();
           // 刷新结束
           _controller.finishRefresh();
         },
         onLoad: () async {
           print("load");
           await cubit.loadDatas(cubit.state.pageIndex + 1);
           // 刷新结束， 控制加载，如果当前的加载条目，大于总条目，显示没有更多数据
           _controller.finishLoad(
               noMore: cubit.state.items.length >= cubit.state.itemCount);
         },
       ),
     );
    });
  }

  /// 列表元素
  Widget _itemBuilder(BuildContext context, int index) {
    final cubit = BlocProvider.of<SeriesCubit>(context);

    // CircleModel model = context.watch<CircleProvider>().items[index];
    CircleModel model = cubit.state.items[index];//_items[index];
    return GestureDetector(
      onTap: () {
        Navigator.push(context, MaterialPageRoute(
          builder: (context) {
            return ArticlePage(arguments: {"id": model.id});
            // return CircleDetailsView(arguments: {"id" : model.id},);
          },
        ));
      },
      child: CircleCell(
        data: model,
      ),
    );
    // return CircleCell(index: index % 3,);
  }

  @override
  bool get wantKeepAlive => true;
}
