import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/login/login_model.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:tuya/tuya.dart';

import '../../utils/global.dart';
import '../../widgets/login/index.dart';

/**
 * 绑定手机号码
 */
class BindPhoneView extends StatefulWidget {
  final String openId;
  const BindPhoneView({Key? key, required this.openId}) : super(key: key);

  @override
  State<BindPhoneView> createState() => _BindPhoneViewState();
}

class _BindPhoneViewState extends State<BindPhoneView> {
  String? _phone;
  String? _code;
  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    return Scaffold(
      body: ListView(
        children: [
          Container(
            height: 44,
            child: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(left: 17.w),
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: Colors.black,
                  size: 22,
                )),
          ),
          Container(
            padding: EdgeInsets.only(top: 28.h),
            alignment: Alignment.center,
            child: Text(
              "请绑定手机号",
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: 5.h),
            alignment: Alignment.center,
            child: Text(
              "绑定后可直接用微信登录",
              style: TextStyle(
                fontSize: 14.sp,
                color: Color(0xFFA2A2A2),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 27.w, top: 30.h, right: 27.w),
            child: Column(
              children: [
                AccountWidget(
                  textColor: Color(0xFF131313),
                  hintTextColor: Color(0xFFC4C4C4),
                  onChanged: (value) {
                    setState(() {
                      _phone = value;
                    });
                  },
                ),
                SizedBox(
                  height: 15.h,
                ),
                VerifyCodeWidget(
                  phone: _phone,
                  textColor: Color(0xFF131313),
                  hintTextColor: Color(0xFFC4C4C4),
                  itemTextColor: Business.mainColor,
                  onChanged: (value) {
                    _code = value;
                  },
                ),
                SizedBox(
                  height: 15.h,
                ),
                LoginButton(
                  text: "登录",
                  onTap: () {
                    print("登录");
                    _registerWXAction();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 微信注册登录
  void _registerWXAction() async {
    try {
      if (_phone == null || _phone!.isEmpty) {
        EasyLoading.showToast("请输入手机号码！");
        return;
      }
      if (_phone!.length != 11 && _phone!.startsWith("1") == false) {
        EasyLoading.showToast('请输出正确的手机号!');
        return;
      }
      if (_code == null || _code!.isEmpty) {
        EasyLoading.showToast("请输入验证码！");
        return;
      }
      EasyLoading.show();
      Map<String, dynamic> params = {
        "openId": widget.openId,
        "telephone": _phone,
        "smsCode": _code,
      };
      final data = await Http.post('/api/token/wechatSignUp', data: params);
      EasyLoading.dismiss();
      final result = ResultModel<LoginModel>.fromJson(
          data, (json) => LoginModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        /// 登录 Tuya
        final res = await Tuya.login(_phone!, 'Passw0rDSeat!1');
        if (res != null) {
          EasyLoading.dismiss();

          Global.profile.token = result.data?.token;
          Global.profile.user = result.data?.user;
          Global.profile.user?.id = result.data?.uid;
          Global.saveProfile();

          // 页面跳转
          Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
        } else {
          print(">>> 登录失败！");
          EasyLoading.showToast('登录失败！');
        }
      } else {
        print(">>> 登录失败！");
        EasyLoading.showToast('登录失败！');
      }
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}
