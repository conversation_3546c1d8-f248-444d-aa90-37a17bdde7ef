import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/views/login/index.dart';

import '../../utils/global.dart';
import '../../utils/version.dart';
import '../../widgets/login/agreement.dart';
import '../../widgets/login/login_button.dart';
import '../../widgets/login/login_mode.dart';

/**
 * 快捷登录
 */
class LoginQuickView extends StatefulWidget {
  const LoginQuickView({Key? key}) : super(key: key);

  @override
  State<LoginQuickView> createState() => _LoginQuickViewState();
}

class _LoginQuickViewState extends State<LoginQuickView> {
  // 协议政策打钩
  bool tick = false;

  @override
  Widget build(BuildContext context) {
    // 版本检测
    AppVersion().checkAndroidVersion(context);
    AppVersion().checkIOSVersion(context);

    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
              image: AssetImage('assets/images/l_bg.png'), fit: BoxFit.cover),
        ),
        padding: EdgeInsets.only(left: 27.5.w, right: 27.5.w),
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            SizedBox(
              height: 210.h,
            ),
            Image.asset(
              'assets/images/l_wel.png',
              width: 286.w,
              height: 34.w,
            ),
            GestureDetector(
              onTap: () {
                Navigator.push(context,
                    MaterialPageRoute(builder: (_) => LoginPhoneView()));
              },
              child: Container(
                margin: EdgeInsets.only(top: 62.5.h),
                height: 55.h,
                decoration: BoxDecoration(
                  color: Color(0x0D000000),
                  borderRadius: BorderRadius.all(Radius.circular(55.h / 2)),
                ),
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "+86",
                      style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white),
                    ),
                    SizedBox(
                      width: 7.w,
                    ),
                    Text(
                      "1586****282",
                      style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white),
                    ),
                    SizedBox(
                      width: 7.w,
                    ),
                    Text(
                      '换号',
                      style: TextStyle(fontSize: 12.sp, color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 15.h,
            ),
            LoginButton(
              text: "一键登录",
              onTap: () {
                print("一键登录");
                // if (tick == true) {
                Global.profile.token =
                    "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJheTE2NTAxMjMzNjA1MjZuTUFrUiIsImlhdCI6MTY1MTc2MDM1MSwiZXhwIjoxNjUyMTIwMzUxfQ.np97ttLMSzdA3V67VeOH9NzDVn3-TsqcSEhd2xqMpSgoTGDbZwz6H_I9xue1EQY-STMcR1CNlCYclCCsxn6xHA";
                Navigator.pushNamedAndRemoveUntil(
                    context, 'home', (route) => false);
                // } else {
                //   // 提示勾选协议
                //
                // }
              },
            ),
            Padding(
              padding: EdgeInsets.only(top: 29.5.h, left: 7.w),
              child: AgreementWidget(
                onTickChange: (v) {
                  tick = v;
                  setState(() {});
                },
              ),
            ),
            SizedBox(
              height: 172.h,
            ),
            LoginModeWidget(),
          ],
        ),
      ),
    );
  }
}
