import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:rabbit_seat_app/models/privacyPolicy/privacy_policy_model.dart';
import 'package:rabbit_seat_app/widgets/index.dart';

import '../../models/login/index.dart';
import '../../services/help_service.dart';

class PolicyView extends StatefulWidget {
  const PolicyView({Key? key}) : super(key: key);

  @override
  State<PolicyView> createState() => _PolicyViewState();
}

class _PolicyViewState extends State<PolicyView> {

  /// 协议数据
  // PolicyModel? info;
  PrivacyPolicyModel? info;

  @override
  void initState() {
    super.initState();

    _loadDatas();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefAppBar(context: context, title: "隐私政策"),
      body: Container(
        padding: EdgeInsets.only(left: 20, right: 20, top: 20,  bottom: MediaQuery.of(context).padding.bottom + 20),
        child: info != null ? ListView(children: [Html(data: info!.codeValue)],) : SizedBox(),
      ),
    );
  }

  /// 加载用户协议数据
  void _loadDatas() async {
    EasyLoading.show();
    List<PrivacyPolicyModel>? list = await HelpService.loadPrivacyPolicyDatas();
    EasyLoading.dismiss();
    if (list != null && list.length > 0) {
      list.forEach((element) {
        if (element.codeName == 'app.help.privacyPolicy') {
          info = element;
          if (mounted) setState(() {});
        }
      });
    } else {
      EasyLoading.showToast('数据加载失败！');
    }

    // EasyLoading.show();
    // HelpService.loadPolicyData(success: (data) {
    //   info = data;
    //   if (mounted) setState(() {});
    //   EasyLoading.dismiss();
    // }, failure: (msg) {
    //   EasyLoading.dismiss();
    //   EasyLoading.showToast(msg);
    // });
  }
}
