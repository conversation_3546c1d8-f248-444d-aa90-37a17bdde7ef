import 'dart:async';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/models/user/index.dart';
import 'package:rabbit_seat_app/services/token_service.dart';
import 'package:rabbit_seat_app/utils/global.dart';
import 'package:rabbit_seat_app/utils/utils.dart';
import 'package:rabbit_seat_app/utils/version.dart';
import 'package:rabbit_seat_app/widgets/loading.dart';
import 'package:rabbit_seat_app/widgets/login/index.dart';
import 'package:tuya/tuya.dart';

import '../../models/login/login_model.dart';

import 'package:fluwx/fluwx.dart';

/**
 * 短信验证码登录
 */
class LoginPhoneView extends StatefulWidget {
  const LoginPhoneView({Key? key}) : super(key: key);

  @override
  State<LoginPhoneView> createState() => _LoginPhoneViewState();
}

class _LoginPhoneViewState extends State<LoginPhoneView> {
  bool tick = false;

  // 手机号码
  String? _phone;

  // 验证码
  String? _code;

  // 是否安装了微信
  bool _isWxInstalled = false;

  // 网络监控
  late StreamSubscription subscription;

  bool isLoading = false;

  @override
  void initState() {
    isLoading = !Util.hasInitLoading;
    super.initState();

    Future.delayed(const Duration(milliseconds: 2000), () {
      if (isLoading) {
        Util.hasInitLoading = true;
        setState(() {
          isLoading = false;
        });
      }
    });

    Util.initOtherSdk();
    _init();

    subscription = Connectivity().onConnectivityChanged.listen((event) {
      print(">>> Network state:$event");
      if (event == ConnectivityResult.none) {
        EasyLoading.showToast('当前网络不可用！');
      }
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose

    subscription.cancel();

    super.dispose();
  }

  // 自定义初始化
  void _init() async {
    try {
      // 获取微信安装情况
      var fluwx = Fluwx();
      var res = await fluwx.isWeChatInstalled;
      setState(() {
        _isWxInstalled = res;
      });
    } catch (e) {
      print(">>> $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const LoadingPage();
    }
    // 版本检测
    AppVersion().checkAndroidVersion(context);
    AppVersion().checkIOSVersion(context);

    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕

    return Scaffold(
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          Util.removePrimaryFocus(context);
        },
        child: Container(
          decoration: BoxDecoration(
            image: DecorationImage(
                image: AssetImage('assets/images/l_bg.png'), fit: BoxFit.cover),
          ),
          child: ListView(
            padding: EdgeInsets.zero,
            children: [
              Container(
                margin:
                    EdgeInsets.only(top: MediaQuery.of(context).padding.top),
                padding: EdgeInsets.only(left: 15.w),
                height: 44,
                alignment: Alignment.centerLeft,
                // child: IconButton(
                //   onPressed: () {
                //     Navigator.pop(context);
                //   },
                //   icon: Icon(
                //     Icons.arrow_back_ios,
                //     color: Colors.white,
                //     size: 22,
                //   ),
                // ),
              ),
              SizedBox(
                height: 125.h,
              ),
              _contentWidget(context),
              SizedBox(
                height: 100.h,
              ),
              Platform.isIOS
                  ? _isWxInstalled == true
                      ? LoginModeWidget()
                      : SizedBox()
                  : LoginModeWidget(),
            ],
          ),
        ),
      ),
    );
  }

  /// 主体内容
  Widget _contentWidget(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 27.w, right: 27.w),
      child: Column(
        children: [
          Container(
            child: Text(
              "欢迎来到 两只兔子",
              style: TextStyle(fontSize: 35.sp, color: Colors.white),
            ),
          ),
          SizedBox(
            height: 55.h,
          ),
          AccountWidget(
            onChanged: (text) {
              print("_phone:$text");
              setState(() {
                _phone = text;
              });
            },
          ),
          SizedBox(
            height: 15.h,
          ),
          VerifyCodeWidget(
            phone: _phone,
            onChanged: (text) {
              _code = text;
            },
          ),
          SizedBox(
            height: 15.h,
          ),
          LoginButton(
            text: "登录",
            onTap: () {
              Util.removePrimaryFocus(context);

              print("登录");

              // 协议处理
              if (tick == true) {
                _loginAction1(context);

                // _loginAction(context);
                // Global.profile.token =
                // "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.**************************************************************************************.np97ttLMSzdA3V67VeOH9NzDVn3-TsqcSEhd2xqMpSgoTGDbZwz6H_I9xue1EQY-STMcR1CNlCYclCCsxn6xHA";

                // Global.profile.token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJheTE2NTAxMjMzNjA1MjZuTUFrUiIsImlhdCI6MTY1Mzg5NjQzMSwiZXhwIjoxNjU0MjU2NDMxfQ.5MVL-LsFCXroTszcTXevh4ILXyUaVDyV8l-1N_W--o7MHlP5QROhhWWCOw9I6MkTJw-Nd1RbYhgUeMPdsRzs1A';
                // Navigator.pushNamedAndRemoveUntil(
                //     context, '/', (route) => false);

                // _loginAction();
              } else {
                // 提示勾选协议
                EasyLoading.showToast("请先勾选隐私协议");
              }
            },
          ),
          Padding(
            padding: EdgeInsets.only(top: 26.w, left: 7.w),
            child: AgreementWidget(
              onTickChange: (v) {
                tick = v;
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 登录事件
  void _loginAction(BuildContext context) async {
    EasyLoading.show();
    TokenService.loginByAccountPassword(
        phone: "***********",
        password: "Passw0rDSeat!1",
        success: (data) async {
          Global.profile.token = data?.token;
          Global.profile.user = UserModel();
          Global.profile.user?.id = data?.uid;
          Global.saveProfile();
          // var res2 = await Tuya.LoginById(res!.uid!);
          final res = await Tuya.login('***********', 'Passw0rDSeat');
          print(">>> tuya:${res.toString()}");
          EasyLoading.dismiss();

          // 页面跳转
          Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
        },
        failure: (msg) {
          EasyLoading.dismiss();
          EasyLoading.showToast(msg);
        });
  }

  void _loginAction1(BuildContext context) async {
    try {
      if (_phone == null || _phone!.isEmpty) {
        EasyLoading.showToast("请输入手机号码！");
        return;
      }
      if (_phone!.length != 11 && _phone!.startsWith("1") == false) {
        EasyLoading.showToast('请输出正确的手机号!');
        return;
      }

      /// 正则检验手机号码格式
      /// 强度：1[0-9]{10}
      /// 强度：0?(13|14|15|17|18|19)[0-9]{9}
      /// 强度：(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}
      // bool exp = RegExp('1[0-9]{10}').hasMatch(_phone!);
      // if (exp == false) {
      //   EasyLoading.showToast("手机号码输入不正确！");
      //   return;
      // }

      if (_code == null || _code!.isEmpty) {
        EasyLoading.showToast("请输入验证码！");
        return;
      }
      EasyLoading.show();
      TokenService.loginByVerifyCode(
        phone: _phone!,
        code: _code!,
        success: (data) async {
          /// 登录 Tuya
          final res = await Tuya.login(_phone!, 'Passw0rDSeat!1');
          if (res != null) {
            EasyLoading.dismiss();

            Global.profile.token = data.token;
            Global.profile.user = data.user;
            Global.profile.user?.id = data.uid;
            Global.saveProfile();

            // 页面跳转
            Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
          } else {
            print(">>> 登录失败！");
            EasyLoading.showToast('登录失败！');
          }
        },
        failure: (msg) {
          EasyLoading.dismiss();
          EasyLoading.showToast(msg);
        },
      );
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}
