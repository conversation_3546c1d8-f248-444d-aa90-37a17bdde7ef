import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:rabbit_seat_app/widgets/intercat/interact_cell.dart';

import '../../widgets/refresh/empty_widget.dart';
import '../../widgets/refresh/index.dart';
import 'interact_cubit.dart';
import 'interact_state.dart';


/**
 * 交互
 */
class InteractPage extends StatefulWidget {
  const InteractPage({Key? key}) : super(key: key);

  @override
  State<InteractPage> createState() => _InteractPageState();
}

class _InteractPageState extends State<InteractPage> with AutomaticKeepAliveClientMixin {

  /// 控制器
  late EasyRefreshController _refreshController;

  @override
  void initState() {
    super.initState();

    _refreshController = EasyRefreshController();
  }


  @override
  void dispose() {

    _refreshController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => InteractCubit(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<InteractCubit>(context);

    return BlocBuilder<InteractCubit, InteractState>(builder: (context, state) {
      return EasyRefresh.custom(
        firstRefresh: true,
        // 没有数据组件
        emptyWidget: cubit.state.items.length == 0 ? EmptyWidget() : null,
        controller: _refreshController,
        header: CustomRefreshHeader(),
        footer: CustomRefreshFooter(),
        slivers: [
          /// 列表数据
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) => InteractCell(data: cubit.state.items[index]),
              childCount: cubit.state.items.length,
            ),
          ),
        ],
        shrinkWrap: true,
        onRefresh: () async {
          // 刷新
          await cubit.loadDatas(0);
          // 重置加载没有更多数据
          _refreshController.resetLoadState();
          // 刷新结束
          _refreshController.finishRefresh();
        },
        onLoad: () async {
          // 加载更多
          await cubit.loadDatas(cubit.state.pageIndex + 1);
          // 刷新结束， 控制加载，如果当前的加载条目，大于总条目，显示没有更多数据
          _refreshController.finishLoad(
              noMore: cubit.state.pageIndex >= cubit.state.pageCount);
        },
      );
    });
  }

  @override
  bool get wantKeepAlive => true;
}
