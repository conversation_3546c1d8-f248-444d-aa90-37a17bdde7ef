import 'package:bloc/bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import '../../models/error.dart';
import '../../models/interact/interact_model.dart';
import '../../models/list/list_model.dart';
import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import 'interact_state.dart';

class InteractCubit extends Cubit<InteractState> {
  InteractCubit() : super(InteractState().init());

  /// 加载数据
  Future<void> loadDatas(int index) async {
    try {
      Map<String, dynamic> params = {"pageNo": index, "pageSize": 10};
      final data = await Http.get('/api/interactMesage/list', data: params);
      final result = ResultModel<ListModel<InteractModel>?>.fromJson(
        data,
        (json) => ListModel<InteractModel>.fromJson(
          json as Map<String, dynamic>,
          (jsonT) => InteractModel.fromJson(jsonT as Map<String, dynamic>),
        ),
      );
      if (result.code == 200) {
        ListModel<InteractModel>? _listModel =
            result.data as ListModel<InteractModel>?;
        if (_listModel != null) {
          if (index == 0) {
            state.items = _listModel.content ?? [];
          } else {
            state.items.addAll(_listModel.content ?? []);
          }
          state.pageIndex = index;
          state.pageCount = _listModel.totalPages.toInt();
          emit(state.clone());
        }
      } else {
        EasyLoading.showToast(result.message);
      }
      return;
    } on MyError catch (e) {
      EasyLoading.showToast(e.message ?? '网络请求失败！');
      return;
    } catch (e) {
      EasyLoading.showToast(e.toString());
      return;
    }
  }
}
