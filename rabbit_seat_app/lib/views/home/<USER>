import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
// import 'package:flutter_gif/flutter_gif.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/models/home/<USER>';
import 'package:rabbit_seat_app/provider/device_provider.dart';
import 'package:rabbit_seat_app/utils/rx.dart';
import 'package:rabbit_seat_app/utils/signal.dart';
import 'package:rabbit_seat_app/views/common/video_play_view.dart';
import 'package:rabbit_seat_app/widgets/dialog/dialog.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../models/device/device_model.dart';
import '../../services/device_service.dart';
import '../../widgets/dialog/overlay_utils.dart';
import '../../widgets/gif/index.dart';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/refresh/index.dart';
import '../device/device_manage_view.dart';

/// ======================================================================
/// ======================================================================

/// 头部
class HeaderWidget extends StatelessWidget {
  /// 设备名称
  late String name;

  /// 设备在线情况
  late bool online;

  late bool isShare;

  /// 电量 '电量百分比',
  late String energy;

  /// 温度:'座椅温度',
  late String temp;

  /// 菜单点击
  Function(Key)? onMenuTap;

  /// 更多点击
  Function()? onMoreTap;

  HeaderWidget(
      {Key? key,
      this.name = '',
      this.online = false,
      this.isShare = false,
      this.energy = '',
      this.temp = '',
      this.onMenuTap,
      this.onMoreTap})
      : super(key: key);

  final GlobalKey _headerKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    // Helper function to get temperature status text and color
    Map<String, dynamic> getTemperatureStatus(String tempStr) {
      double? tempValue = double.tryParse(tempStr);
      if (tempValue == null) {
        return {
          'text': '--',
          'color': const Color(0xFF4B4B4B)
        }; // Default for invalid temp
      } else if (tempValue < 18) {
        return {'text': '较冷', 'color': Colors.blue};
      } else if (tempValue >= 18 && tempValue < 25) {
        return {'text': '舒适', 'color': Colors.green};
      } else {
        // tempValue >= 25
        return {'text': '较热', 'color': Colors.red};
      }
    }

    // 在线
    return Container(
      padding: EdgeInsets.only(top: 10, left: 10.w, right: 10.w),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            color: Colors.transparent,
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 设备
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        InkWell(
                          onTap: (){
                            Navigator.pop(context);
                          },
                          child: Container(
                            padding: EdgeInsets.only(right: 5.w),
                            child: Image.asset(
                              "assets/images/device/back.png",
                              width: 25.w,
                            ),
                          ),
                        ),
                        Container(
                          constraints: BoxConstraints(
                            maxWidth: MediaQuery.of(context).size.width / 3,
                          ),
                          child: Text(
                            name,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 20.sp,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF000000)),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 3.h,
                    ),
                    // 状态
                    online
                        ? Row(
                            children: [
                              Container(
                                width: 30.w,
                              ),
                              Icon(
                                Icons.circle,
                                color: online ? Colors.green : Colors.red,
                                size: 10.w,
                              ),
                              SizedBox(
                                width: 3,
                              ),
                              Text(
                                "在线",
                                style: TextStyle(
                                  fontSize: 11.sp,
                                  color: Color(0xFF515151),
                                ),
                              ),
                              Padding(
                                padding:
                                    EdgeInsets.only(left: 8.5.w, right: 8.5.w),
                                child: Container(
                                  color: Color(
                                    0xFF515151,
                                  ),
                                  width: 2.w,
                                  height: 10.w,
                                ),
                              ),
                              Text(
                                "电量:$energy%",
                                style: TextStyle(
                                    fontSize: 11.sp, color: Color(0xFF515151)),
                              ),
                            ],
                          )
                        : Row(
                            children: [
                              Icon(
                                Icons.circle,
                                color: Colors.red,
                                size: 10.w,
                              ),
                              SizedBox(
                                width: 3.w,
                              ),
                              Text(
                                '离线',
                                style: TextStyle(
                                  fontSize: 11.sp,
                                  color: Color(0xFFED0000),
                                ),
                              ),
                            ],
                          ),
                  ],
                ),
              ],
            ),
          ),

          Expanded(child: Container()),
          // 右侧控制
          Container(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    print("温度");
                  },
                  child: Container(
                    height: 30.h,
                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                    alignment: Alignment.center,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(
                        Radius.circular(30 / 2),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0x1C000000),
                          offset: Offset(0, 0),
                          blurRadius: 5,
                          spreadRadius: 3,
                        )
                      ],
                    ),
                    child: online
                        ? Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                "环境温度感知:",
                                style: TextStyle(
                                    fontSize: 13.sp,
                                    color: const Color(0xFF4B4B4B)),
                              ),
                              SizedBox(width: 3.w),
                              Text(
                                getTemperatureStatus(temp)['text'],
                                style: TextStyle(
                                    fontSize: 13.sp,
                                    fontWeight: FontWeight.bold,
                                    color: getTemperatureStatus(temp)['color']),
                              ),
                            ],
                          )
                        : const Text('设备已经离线',
                            style: TextStyle(
                              fontSize: 13,
                              color: Color(0xFF4B4B4B),
                            )),
                  ),
                ),
                SizedBox(
                  width: 5.w,
                ),
                (isShare
                    ? Container(
                        child: const Text('共享设备',
                            style: TextStyle(
                              fontSize: 13,
                              color: Color(0xFF4B4B4B),
                            )),
                      )
                    : GestureDetector(
                        onTap: () {
                          print("更多设备");

                          if (onMoreTap != null) onMoreTap!();
                        },
                        child: Container(
                          width: 30.w,
                          height: 30.w,
                          alignment: Alignment.center,
                          child: Image.asset(
                            "assets/images/home/<USER>",
                            fit: BoxFit.fill,
                            width: 20.w,
                            height: 20.w,
                          ),
                        ),
                      )),
              ],
            ),
          )
        ],
      ),
    );
  }
}
