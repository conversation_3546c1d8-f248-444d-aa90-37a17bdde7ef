import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:async/async.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/privacyPolicy/privacy_policy_model.dart';
import 'package:rabbit_seat_app/provider/device_provider.dart';
import 'package:rabbit_seat_app/services/help_service.dart';
import 'package:rabbit_seat_app/test_data.dart';
import 'package:rabbit_seat_app/utils/rx.dart';
import 'package:rabbit_seat_app/utils/utils.dart';
import 'package:rabbit_seat_app/views/home/<USER>';
import 'package:rabbit_seat_app/widgets/device/new_device_control.dart';
import 'package:rabbit_seat_app/widgets/home/<USER>';
import 'package:rabbit_seat_app/widgets/home/<USER>';
import 'package:tuya/tuya.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'package:rabbit_seat_app/models/device/device_model.dart';
import 'package:rabbit_seat_app/utils/event_channel.dart';
import 'package:rabbit_seat_app/widgets/animation/ripple.dart';
import 'package:rabbit_seat_app/widgets/dialog/dialog.dart';
import 'package:rabbit_seat_app/widgets/dialog/overlay_utils.dart';

// import '../../widgets/gif/index.dart';
import 'package:rabbit_seat_app/widgets/home/<USER>';
import 'package:rabbit_seat_app/widgets/home/<USER>';
import 'package:rabbit_seat_app/widgets/home/<USER>';
import 'package:rabbit_seat_app/widgets/home/<USER>';
import 'package:rabbit_seat_app/widgets/home/<USER>';
import 'package:rabbit_seat_app/widgets/refresh/index.dart';
import 'package:rabbit_seat_app/views/device/dev_ruled_view.dart';
import 'package:rabbit_seat_app/views/device/device_manage_view.dart';
import 'hom_cubit.dart';
import 'hom_state.dart';

import 'package:gif/gif.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

/**
 * 主页
 */
class DeviceView extends StatefulWidget {
  const DeviceView({Key? key}) : super(key: key);

  @override
  State<DeviceView> createState() => _DeviceViewState();
}

class _DeviceViewState extends State<DeviceView>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  /// 刷新控制
  EasyRefreshController _controller = EasyRefreshController();

  late DeviceModel initDevice;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 获取路由参数

    initDevice = ModalRoute.of(context)!.settings.arguments as DeviceModel;
    if (initDevice.productId == '6sho1h3rxg4a2bbn') {
      showNewControl = true;
    }
  }

  /// gif 控制器
  late final GifController gifController;

  String initbgUrl = 'assets/images/gif/def.gif';

  /// 背景图片
  late String _bgUrl = initbgUrl;

  /// 是否播放
  // late bool isPlay = false;

  /// 播放时间长度
  int normalDuration = 2110;
  int _duration = 2110 * 2;

  BuildContext? _context;

  // 页面可见性key
  final GlobalKey _visibleKey = GlobalKey();

  // 页面显示刷新数据计时器控制器
  // 秒
  int seconds = 7;
  int initDataSeconds = 7;

  // 计时器
  //RestartableTimer? timer;
  Timer? _timer;

  bool _isFirstLoad = false;

  bool _isLoading = false;

  bool _isShowAlertDialog = false;

  // 需要在页面销毁时调用Stream的cancel方法取消监听
  StreamSubscription? _nativeSubscription;

  /**
   * 是否显示新功能-新的设备型号：6sho1h3rxg4a2bbn
   */
  bool showNewControl = false;

  @override
  void initState() {
    super.initState();

    gifController = GifController(vsync: this);

    gifController.addListener(() {
      if (gifController.isCompleted == true) {
        printLog(">>> isCompleted");

        // _resetBgImageEvents();
      }
    });

    // flutter   调用  iOS
    if (Platform.isIOS) {
      //flutter调用原生
      MethodChannel _methodChannel = MethodChannel('Home_View');
      //调用原生
      _methodChannel.invokeListMethod('initTuyaDeviceToken');
    }

    initSeconds();
  }

  void initSeconds() async {
    PrivacyPolicyModel? timeSetting =
        await HelpService.getDeviceDataRefreshTime();
    if (timeSetting != null && timeSetting.codeValue != null) {
      initDataSeconds = int.tryParse(timeSetting.codeValue!) ?? 0;
    }
  }

  /// 接收到远程通知数据
  void receiveRemoteMsg(arg) {
    print(">>> MEventChannel");

    print(">>> MEventChannel    --- home_view");
    if (arg != null && arg is String) {
      if (arg == '离车报警') {
        // 返回首页
        Future.delayed(Duration(milliseconds: 1000), () {
          if (_context != null) {
            // 告警信息
            if (_isShowAlertDialog == false) {
              _isShowAlertDialog = true;
              _showAlarmDialog(_context!);
            }
            // 刷新数据
            _refreshHandler(_context!);
          }
        });
      } else if (arg == '消息提醒') {
        // 跳转到消息页面
        Rx.sendMyItemBadgeSubject.add(1);
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    gifController.dispose();

    // 销毁计时器
    disposeTimer();

    // MEventChannel().dispose();
    if (_nativeSubscription != null) {
      _nativeSubscription?.cancel();
      _nativeSubscription = null;
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _context = context;
    return BlocProvider(
      create: (BuildContext context) => HomCubit(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    _context = context;
    final cubit = BlocProvider.of<HomCubit>(context);

    if (cubit.state.displayDevices == null) {
      // 初始化设备
      cubit.setDevice(initDevice);
    }
    return BlocBuilder<HomCubit, HomState>(builder: (context, state) {
      return VisibilityDetector(
          key: _visibleKey,
          child: Scaffold(
            backgroundColor: Colors.white,
            body: Column(
              children: [
                GestureDetector(
                  onDoubleTap: () {
                    EasyLoading.showToast(
                        "错误码:${state.displayDevices?.errValue}");
                  },
                  child: Container(
                    margin: EdgeInsets.only(
                        top: MediaQuery.of(context).padding.top),
                    // height: 56.h,
                    // color: Colors.grey,
                    child: _headerWidget(context),
                  ),
                ),
                Expanded(
                  child: EasyRefresh.custom(
                    firstRefresh: true,
                    controller: _controller,
                    header: CustomRefreshHeader(),
                    slivers: [
                      /// 包装部件
                      SliverToBoxAdapter(
                        child: _bodyWidget(context),
                      ),
                      // SliverList(
                      //   delegate: SliverChildBuilderDelegate(
                      //     (_, index) {
                      //       return Container(
                      //         height: 1,
                      //       );
                      //     },
                      //     childCount: 1,
                      //   ),
                      // ),
                    ],
                    shrinkWrap: true,
                    onRefresh: () async {
                      _refreshHandler(context);
                    },
                  ),
                ),
              ],
            ),
          ),
          onVisibilityChanged: (info) async {
            print(">>>> info:${info.toString()}");
            if (info.visibleBounds.size.width == 0 &&
                info.visibleBounds.size.height == 0) {
              // 页面消失
              print(">>> home 页面消失 -------");
              // 销毁计时器
              disposeTimer();
            } else if (info.visibleBounds.size.width == info.size.width &&
                info.visibleBounds.size.height == info.size.height) {
              // 页面显示
              print(">>> home 页面显示 -------");
              if (_isFirstLoad == true) await _refreshHandler(context);
            }
          });
    });
  }

  bool isGifing = false;

  Future<void> getDevice(context) async {
    final cubit = BlocProvider.of<HomCubit>(context);
    var _device = await Business.getDevice(initDevice.id!);
    if (_device != null) {
      // // test
      // _device = testDevice;

      cubit.setDevice(_device);

      if(!_isFirstLoad && _device.lptimOnoff){
        showSleepDialog(context);
      }
    }
  }

  /// 刷新数据
  Future<void> _refreshHandler(BuildContext context) async {
    final cubit = BlocProvider.of<HomCubit>(context);
    if (cubit.isDoing || isGifing) {
      createTimer(context);
      return;
    }
    // 销毁计时器
    disposeTimer();

    _isLoading = true;

    await getDevice(context);

    // await cubit.loadMyDevices();
    // await cubit.loadAlarmInfos();

    _isLoading = false;

    _resetBgImageEvents();

    _controller.finishRefresh();

    _isFirstLoad = true;

    // if (cubit.state.displayDevices != null &&
    //     cubit.state.displayDevices!.isRemote == true) {
    //   DefDialog.showDialog2(
    //       context: context,
    //       message: '远程模式下，座椅加热将在五分钟后 自动停止',
    //       itemText: '知道了',
    //       confirm: () {});
    // }
    if (cubit.state.devices != null) {
      context
          .read<DeviceProvider>()
          .setDeviceCount(cubit.state.devices!.length);
      context.read<DeviceProvider>().setMinExpiredTime(cubit.state.devices!);
      if (cubit.state.displayDevices != null)
        context
            .read<DeviceProvider>()
            .setCurrentDeviceExpireTime(cubit.state.displayDevices!.activeTime);
    } else {
      context.read<DeviceProvider>().setDeviceCount(0);
      context.read<DeviceProvider>().setCurrentDeviceExpireTime(0);
    }

    // 告警信息
    if (cubit.state.displayDevices?.leaveWarnStatus == 'Warning' &&
        _isShowAlertDialog == false) {
      _isShowAlertDialog = true;
      _showAlarmDialog(context);
    }

    // 电池高温异常
    if (cubit.state.displayDevices?.isHighTemp == true) {
      if (!hasShowBatWarmDialog) {
        _showHighTempWarnDialog();
      }
    }

    /// 创建计时器
    createTimer(context);

    return;
  }

  /// 计时器
  void createTimer(BuildContext context) {
    // 销毁计时器
    disposeTimer();
    seconds = initDataSeconds;
    // 计时器
    _timer ??= Timer.periodic(const Duration(seconds: 1), (timer) {
      _timerEventHandler(context);
    });
  }

  /// 计时器事件
  void _timerEventHandler(BuildContext context) async {
    // print(">>> home refresh timer seconds:$seconds");
    if (_isFirstLoad == false) return;
    if (seconds > 0) {
      seconds--;
    } else {
      // 销毁
      disposeTimer();
      // 刷新数据
      _refreshHandler(context);
    }
  }

  /// 销毁计时器
  void disposeTimer() {
    if (_timer != null && _timer!.isActive) {
      _timer?.cancel();
    }
    _timer = null;
  }

  /// 显示远程提示弹框
  void _showRemoteModeDialog(BuildContext context, String type) {
    DefDialog.showDialog2(
        context: context,
        message: '远程模式下，$type将在五分钟后自动停止',
        itemText: '知道了',
        confirm: () {});
  }

  /// 显示告警弹框
  void _showAlarmDialog(BuildContext context) {
    final cubit = BlocProvider.of<HomCubit>(context);

    DefDialog.showDialog3(
        context: context,
        title: '警告',
        message: '两只兔子检测到您的宝宝可能被遗忘在车内！',
        confirm: () async {
          await cubit.solveWarning();
          await cubit.loadDeviceInfos(
              id: cubit.state.displayDevices!.id,
              isShare: cubit.state.displayDevices!.isShare);
          _isShowAlertDialog = false;
        });
  }

  bool hasShowBatWarmDialog = false;
  void _showHighTempWarnDialog() {
    hasShowBatWarmDialog = true;
    DefDialog.showDialog3(
        context: context,
        title: '提示',
        message: '环境温度异常,电池已进入自我保护,请恢复常温后再试',
        confirm: () async {});
  }

  /// 头部部件
  Widget _headerWidget(BuildContext context) {
    final cubit = BlocProvider.of<HomCubit>(context);
    // 显示设备
    return HeaderWidget(
      name: cubit.state.displayDevices!.name ?? '--',
      online: cubit.state.displayDevices!.isOnline,
      temp: cubit.state.displayDevices!.temp ?? '--',
      energy: (cubit.state.displayDevices!.energy > 100 ||
              cubit.state.displayDevices!.energy < 0)
          ? '--'
          : cubit.state.displayDevices!.energy.toString(),
      isShare: cubit.state.displayDevices!.isShare != null &&
          cubit.state.displayDevices!.isShare == true,
      onMenuTap: (key) {},
      onMoreTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (ctx) => DevRuledPage(arguments: {
                      "id": cubit.state.displayDevices!.id,
                      "iccid": cubit.state.displayDevices!.iccid
                    })));
      },
    );
  }

  Autostart autostart = Autostart.loop;

  /// 主体部件
  Widget _bodyWidget(BuildContext context) {
    final cubit = BlocProvider.of<HomCubit>(context);

    ActionButtonStatus windStatus = cubit.state.displayDevices!.isWindDoing
        ? ActionButtonStatus.doing
        : (cubit.state.displayDevices!.isWind
            ? ActionButtonStatus.on
            : ActionButtonStatus.off);
    ActionButtonStatus hotStatus = cubit.state.displayDevices!.isHostDoing
        ? ActionButtonStatus.doing
        : (cubit.state.displayDevices!.isHost
            ? ActionButtonStatus.on
            : ActionButtonStatus.off);
    ActionButtonStatus autoStatus = cubit.state.displayDevices!.isAutoDoing
        ? ActionButtonStatus.doing
        : (cubit.state.displayDevices!.isAuto
            ? ActionButtonStatus.on
            : ActionButtonStatus.off);

    ActionButtonStatus leftStatus = cubit.state.displayDevices!.isLeftDoing
        ? ActionButtonStatus.doing
        : (cubit.state.displayDevices!.isLeft
            ? ActionButtonStatus.on
            : ActionButtonStatus.off);

    if (leftStatus == ActionButtonStatus.on) {
      print("leftStatus${leftStatus}");
    }

    ActionButtonStatus rightStatus = cubit.state.displayDevices!.isRightDoing
        ? ActionButtonStatus.doing
        : (cubit.state.displayDevices!.isRight
            ? ActionButtonStatus.on
            : ActionButtonStatus.off);

    ActionButtonStatus lampStatus = cubit.state.displayDevices!.isLampDoing
        ? ActionButtonStatus.doing
        : (cubit.state.displayDevices!.isLamp
            ? ActionButtonStatus.on
            : ActionButtonStatus.off);
    // 显示设备
    return Container(
      // constraints: BoxConstraints(
      //   minHeight: constraints.maxHeight,
      // ),
      child: Stack(
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(32, 32, 32, 32),
            child: Column(
              children: [
                Container(
                  // width: double.infinity,
                  padding: EdgeInsets.only(
                    top: 0.h,
                    left: 0.w,
                    right: 90.w,
                    bottom: 11,
                  ),
                  child: Stack(
                    children: [
                      Container(
                        height: 480.h,
                        child: Gif(
                          autostart: Autostart.no,
                          fit: BoxFit.fitHeight,
                          controller: gifController,
                          image: AssetImage(_bgUrl),
                          // fps: 24,
                          // duration: Duration(milliseconds: 1000),
                          duration: Duration(milliseconds: _duration),
                          onFetchCompleted: () {
                            gifController.reset();
                            if (autostart == Autostart.loop) {
                              gifController.repeat();
                            } else {
                              gifController
                                  .forward()
                                  .then((_) => gifController.stop());
                            }
                          },
                        ),
                      )
                    ],
                  ),
                ),
                // 底部按钮
                Container(
                    // height: 84.h,
                    padding: const EdgeInsets.only(
                        left: 15, right: 15, top: 12, bottom: 12),
                    decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.circular(42.h),
                        boxShadow: const [
                          BoxShadow(
                            color: Color(0x14000000), // 阴影的颜色
                            offset: Offset(0, 0), // 阴影与容器的距离
                            blurRadius: 30.0, // 高斯的标准偏差与盒子的形状卷积。
                            spreadRadius: 0.0, // 在应用模糊之前，框应该膨胀的量。
                          )
                        ]),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                                child: BottomControlButton(
                              status: windStatus,
                              text: '通风',
                              icon: "assets/images/device/8.png",
                              activeIcon: "assets/images/device/3.png",
                              onTap: () async {
                                if (cubit.state.displayDevices!.isOnline !=
                                    true) {
                                  return;
                                }
                                if (cubit.state.displayDevices!.isRemote ==
                                        true &&
                                    !cubit.state.displayDevices!.isWind) {
                                  _showRemoteModeDialog(context, '座椅通风');
                                  // return;
                                }

                                cubit.state.displayDevices!.isWindDoing = true;
                                var res = await cubit.setCommandEvent('fanSw',
                                    !cubit.state.displayDevices!.isWind);
                                if (res == true) {
                                  // cubit.loadMyDevices();
                                  // cubit.state.displayDevices!.isWind =
                                  //     !cubit.state.displayDevices!.isWind;
                                } else {
                                  return;
                                }

                                cubit.state.displayDevices!.isWindDoing = false;
                                // await cubit.setWindEvent();
                                setState(() {
                                  _duration = normalDuration * 2;
                                  _bgUrl = cubit.getWindGifImage();
                                  autostart = Autostart.loop;
                                });
                                // gifController.reset();
                                // if (cubit.state.displayDevices!.isWind) {
                                //   gifController.reset();
                                //   gifController.repeat(
                                //       period: Duration(milliseconds: _duration));
                                // } else {
                                //   gifController.reset();
                                //   gifController.stop();
                                // }
                              },
                            )),
                            SizedBox(
                              width: 15.w,
                            ),
                            Expanded(
                                child: BottomControlButton(
                              status: hotStatus,
                              text: "加热",
                              icon: "assets/images/device/9.png",
                              activeIcon: "assets/images/device/4.png",
                              onTap: () async {
                                if (cubit.state.displayDevices!.isOnline !=
                                    true) {
                                  return;
                                }
                                if (cubit.state.displayDevices!.isRemote ==
                                        true &&
                                    !cubit.state.displayDevices!.isHost) {
                                  _showRemoteModeDialog(context, '座椅加热');
                                  // return;
                                }

                                // await cubit.setHostEvent();
                                cubit.state.displayDevices!.isHostDoing = true;
                                var res = await cubit.setCommandEvent('hotSw',
                                    !cubit.state.displayDevices!.isHost);
                                if (res == true) {
                                  // cubit.loadMyDevices();
                                  // cubit.state.displayDevices!.isHost =
                                  //     !cubit.state.displayDevices!.isHost;
                                } else {
                                  return;
                                }
                                cubit.state.displayDevices!.isHostDoing = false;
                                setState(() {
                                  _duration = normalDuration * 2;
                                  _bgUrl = cubit.getHostGifImage();
                                  autostart = Autostart.loop;
                                });
                              },
                            )),
                            SizedBox(
                              width: 15.w,
                            ),
                            Expanded(
                                child: BottomControlButton(
                              status: autoStatus,
                              text: "自动模式",
                              icon: "assets/images/device/10.png",
                              activeIcon: "assets/images/device/5.png",
                              onTap: () async {
                                if (cubit.state.displayDevices!.isOnline !=
                                    true) {
                                  return;
                                }
                                if (cubit.state.displayDevices!.isRemote ==
                                        true &&
                                    !cubit.state.displayDevices!.isAuto) {
                                  _showRemoteModeDialog(context, '自动模式');
                                  // return;
                                }

                                cubit.state.displayDevices!.isAutoDoing = true;
                                var res = await cubit.setCommandEvent(
                                    'autoMode',
                                    !cubit.state.displayDevices!.isAuto);
                                if (res == true) {
                                } else {
                                  return;
                                }
                                cubit.state.displayDevices!.isAutoDoing = false;
                                _duration = normalDuration * 2;
                              },
                            )),
                          ],
                        ),
                      ],
                    )),
                showNewControl
                    ? NewDeviceControl(
                        device: cubit.state.displayDevices!,
                        onControl: (code, value) {
                          cubit.setCommandEvent(code, value);
                        })
                    : Container()
              ],
            ),
          ),
          // 告警
          cubit.state.displayDevices?.leaveWarnStatus == 'Warning'
              ? Positioned.fill(
                  child: Container(
                    padding: EdgeInsets.only(
                      top: 0.h,
                      left: 0.w,
                      right: 90.w,
                      bottom: 140.h,
                    ),
                    child: Center(
                      child: SizedBox(
                        width: 100,
                        height: 100,
                        child: Stack(
                          children: [
                            RippleWidget(
                              size: const Size(100, 100),
                              tween: Tween(begin: 40, end: 100),
                              color: Colors.red,
                              duration: 3500,
                              delay: 1150,
                            ),
                            const Center(
                              child: Image(
                                image: AssetImage("assets/images/home/<USER>"),
                                width: 40,
                                height: 40,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                )
              : const SizedBox(),

          // 右侧按钮
          Positioned(
              top: 85.h,
              right: 32.w,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  RightControlButton(
                    status: leftStatus,
                    text: "左保护",
                    icon: "assets/images/device/7.png",
                    activeIcon: "assets/images/device/2.png",
                    onTap: () async {
                      // if (cubit.state.displayDevices!.isRemote == true) {
                      //   _showRemoteModeDialog(context, '左保护');
                      //   // return;
                      // }
                      if (cubit.state.displayDevices!.isOnline != true) {
                        return;
                      }

                      isGifing = true;
                      cubit.state.displayDevices!.isLeftDoing = true;
                      var res = await cubit.setCommandEvent('protectionLeftSw',
                          !cubit.state.displayDevices!.isLeft);
                      if (res == true) {
                        // cubit.state.displayDevices!.isLeft =
                        //     !cubit.state.displayDevices!.isLeft;
                        // cubit.loadMyDevices();
                        // cubit.state.displayDevices!.isLeft =
                        //     !cubit.state.displayDevices!.isLeft;
                      } else {
                        return;
                      }
                      cubit.state.displayDevices!.isLeftDoing = false;

                      if (cubit.state.displayDevices!.isOnline) {
                        // gifController.stop();
                        // gifController.reset();
                        // gifController.stop();
                        // gifController.reset();
                        setState(() {
                          _duration = normalDuration;
                          _bgUrl = cubit.getLeftGifImage();
                          autostart = Autostart.once;
                        });

                        // await gifController.forward();
                      }
                      isGifing = false;
                    },
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  RightControlButton(
                    status: rightStatus,
                    text: "右保护",
                    icon: "assets/images/device/12.png",
                    activeIcon: "assets/images/device/1.png",
                    onTap: () async {
                      // if (cubit.state.displayDevices!.isRemote == true) {
                      //   _showRemoteModeDialog(context, '右保护');
                      //   // return;
                      // }
                      isGifing = true;
                      if (cubit.state.displayDevices!.isOnline != true) {
                        return;
                      }

                      cubit.state.displayDevices!.isRightDoing = true;
                      var res = await cubit.setCommandEvent('protectionRightSw',
                          !cubit.state.displayDevices!.isRight);
                      if (res == true) {
                        // cubit.state.displayDevices!.isRight =
                        //     !cubit.state.displayDevices!.isRight;
                        // cubit.loadMyDevices();
                        // cubit.state.displayDevices!.isRight =
                        //     !cubit.state.displayDevices!.isRight;
                      } else {
                        return;
                      }
                      cubit.state.displayDevices!.isRightDoing = false;

                      if (cubit.state.displayDevices!.isOnline) {
                        // gifController.stop();
                        // gifController.reset();

                        setState(() {
                          _duration = normalDuration;
                          _bgUrl = cubit.getRightGifImage();
                          autostart = Autostart.once;
                        });
                        // await gifController.forward(from: 0);
                      }
                      isGifing = false;
                    },
                  ),
                  SizedBox(
                    height: 30.h,
                  ),
                  RightControlButton(
                    status: lampStatus,
                    text: "氛围灯",
                    icon: "assets/images/device/11.png",
                    activeIcon: "assets/images/device/6.png",
                    onTap: () async {
                      if (cubit.state.displayDevices!.isOnline != true) {
                        return;
                      }
                      isGifing = true;
                      // if (cubit.state.displayDevices!.isRemote == true) {
                      //   _showRemoteModeDialog(context, '氛围灯');
                      //   // return;
                      // }

                      cubit.state.displayDevices!.isLampDoing = true;
                      var res = await cubit.setCommandEvent(
                          'F_light', !cubit.state.displayDevices!.isLamp);
                      if (res == true) {
                        // cubit.loadMyDevices();
                        // cubit.state.displayDevices!.isLamp =
                        //     !cubit.state.displayDevices!.isLamp;
                      }
                      cubit.state.displayDevices!.isLampDoing = false;

                      if (cubit.state.displayDevices!.isOnline) {
                        setState(() {
                          _duration = normalDuration;
                          _bgUrl = cubit.getLampGifImage();
                          autostart = Autostart.once;
                          printLog("$_bgUrl $_duration");
                        });

                        // gifController.stop();

                        // gifController.reset();
                        // await gifController.forward(from: 0);
                        // printLog("forward end");
                      }
                      isGifing = false;
                    },
                  ),
                ],
              )),
          // 离线重连
          Positioned(
              left: 32.w,
              right: 33.w,
              bottom: 130.h,
              child: Visibility(
                visible: cubit.state.displayDevices!.isOnline &&
                    cubit.state.displayDevices!.remoteExpiredTime == null &&
                    cubit.state.displayDevices!.isHighTemp,
                child: const HighTempTipWidget(),
              )),
          Positioned(
              left: 32.w,
              right: 33.w,
              bottom: 130.h,
              child: Offstage(
                offstage: cubit.state.displayDevices!.isOnline &&
                    cubit.state.displayDevices!.remoteExpiredTime == null,
                child: !cubit.state.displayDevices!.isOnline
                    ? const RebindingWidget()
                    : const RemoteTipWidget(),
              )),
        ],
      ),
    );
  }

  /// 背景图片控制
  void _resetBgImageEvents() {
    if (mounted && _context != null) {
      printLog(">>> _resetBgImageEvents");
      final cubit = BlocProvider.of<HomCubit>(_context!);
      if (cubit.state.displayDevices != null) {
        String res = cubit.getInitBgSources();
        setState(() {
          _duration = normalDuration * 2;
          autostart = Autostart.loop;

          _bgUrl = res;
        });
      }
    }
  }

  @override
  bool get wantKeepAlive => true;

  // 封装的弹窗函数
  void showSleepDialog(BuildContext context) {
    showDialog(
      context: context,
      // barrierDismissible: false, // 设置为 false，用户必须点击按钮才能关闭弹窗
      builder: (BuildContext context) {
        return AlertDialog(
          // 1. 设置圆角
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24.0),
          ),
          // 2. 标题
          title: const Text(
            '提示',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black87,
              fontWeight: FontWeight.bold,
              fontSize: 20.0,
            ),
          ),
          // 3. 内容
          content: const Text(
            '打开低功耗待机后，外接电源断开时，能明显降低产品待机时的电池消耗，但无法自动检测车门的开关与自动旋转',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black54,
              fontSize: 15.0,
              height: 1.5, // 行高
            ),
          ),
          // 4. 底部操作按钮
          actions: <Widget>[
            Container(
              // 让按钮横向填满
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 20.0),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  // 按钮背景色
                  backgroundColor: const Color(0xFFF9712E),
                  // 文字颜色
                  foregroundColor: Colors.white,
                  // 按钮的形状
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12.0),
                  // 阴影
                  elevation: 0,
                ),
                child: const Text(
                  '确定',
                  style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold),
                ),
                onPressed: () {
                  // 关闭弹窗
                  Navigator.of(context).pop();
                },
              ),
            ),
          ],
          // 调整标题和内容的内边距
          titlePadding: const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 12.0),
          contentPadding: const EdgeInsets.symmetric(horizontal: 24.0),
          // 将actions的默认padding设为0，由我们自己的Container控制
          actionsPadding: EdgeInsets.zero,
        );
      },
    );
  }
}
