import '../../models/device/device_model.dart';

class HomState {
  // 样式  -1异常，0空白，1没有数据，2显示设备
  int style = 0;

  // 加载异常
  String? loadError;

  // 设备列表数据
  Map<String, DeviceModel>? datas;

  List<DeviceModel>? get devices => datas?.values.toList();

  // 告警
  // bool isAlarm = false;

  // 当前显示设备
  DeviceModel? displayDevices;

  HomState init() {
    return HomState();
  }

  HomState clone() {
    return HomState()
      ..style = style
      ..loadError = loadError
      ..datas = datas
      ..displayDevices = displayDevices
      // ..isAlarm = isAlarm
    ;
  }
}
