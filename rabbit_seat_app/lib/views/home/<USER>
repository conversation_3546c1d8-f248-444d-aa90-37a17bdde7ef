import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/models/alarm/alarmModel.dart';
import 'package:rabbit_seat_app/models/list/list_model.dart';
import 'package:rabbit_seat_app/utils/global.dart';
import 'package:rabbit_seat_app/utils/rx.dart';

import '../../models/device/device_model.dart';
import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import 'hom_state.dart';

class HomCubit extends Cubit<HomState> {
  HomCubit() : super(HomState().init()) {
    _init();
  }

  /// 自定义初始化
  void _init() async {
    print("HomCubit init");

    // 删除设备
    Rx.removeDeviceByIdSubject.listen((event) {
      print(">>> ---------   删除设备 ----------");
      Map<String, dynamic>? datas = event as Map<String, dynamic>?;
      if (datas != null && datas.containsKey('id')) {
        deleteDeviceById(datas['id']);
      }
    });
    // 修改设备名称
    Rx.modifyDeviceNameByIdSubject.listen((event) {
      Map<String, dynamic>? datas = event as Map<String, dynamic>?;
      if (datas != null &&
          datas.containsKey('id') &&
          datas.containsKey('val')) {
        changeDeviceNameById(datas['id'], datas['val']);
      }
    });
    // 刷新数据
    Rx.homeRefreshSubject.listen((value) {
      loadMyDevices();
    });
  }

  /// 加载我的设备列表
  Future<void> loadMyDevices() async {
    try {
      // state.clone()..isAlarm = false;
      final data = await Http.get("/api/device/appList");
      final result = ResultModel<List<DeviceModel>?>.fromJson(data, (json) {
        if (json != null && (json is List<dynamic>)) {
          return (json)
              .map((e) => DeviceModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        List<DeviceModel>? _devs = result.data;
        if (_devs != null && _devs.isNotEmpty) {
          Map<String, DeviceModel> _map = {};
          _devs.forEach((element) {
            _map.addAll({element.id!: element});
          });

          var current = state.displayDevices;
          if (current == null) {
            current = _devs.first;
          }
          var findListDevice = _devs.firstWhere((m) {
            return m.id == current!.id;
          });

          emit(state.clone()
                ..style = 2
                ..datas = _map
                ..displayDevices = current
              // ..isOnline = _devs.first.online,
              );
          // 加载设备详情
          await loadDeviceInfos(
              id: current.id, isShare: findListDevice.isShare);
        } else {
          emit(state.clone()
            ..style = 1
            ..datas = null);
        }
      } else {
        emit(state.clone()
          ..style = -1
          ..datas = null
          ..displayDevices = null
          ..loadError = result.message);
        EasyLoading.showToast(result.message);
      }
      return;
    } catch (e) {
      print('>>> Error:$e');
      emit(state.clone()
        ..style = -1
        ..datas = null
        ..displayDevices = null
        ..loadError = e.toString());
      EasyLoading.showToast(e.toString());
    }
  }

  void setDevice(DeviceModel? device) {
    state.displayDevices = device;
  }

  /// 加载设备信息
  Future<void> loadDeviceInfos({String? id, bool? isShare}) async {
    try {
      // state.clone()..isAlarm = false;
      if (state.displayDevices == null) return;
      final data = await Http.get('/api/device/$id');
      final result = ResultModel<DeviceModel>.fromJson(
          data, (json) => DeviceModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (result.data != null) {
          result.data!.isShare = isShare;
          initHighTempInfo(result.data!);
          // // for test
          // result.data!.isOnline = true;
          if (!isDoing) {
            state.displayDevices = result.data;
            emit(state.clone());
          }
        }
      } else {
        EasyLoading.showToast(result.message);
      }
      return;
    } catch (e) {
      print(">>> loadDeviceInfos -- error:$e");
      EasyLoading.showToast(e.toString());
      return;
    }
  }

  void initHighTempInfo(DeviceModel device) {
    // 电池高温异常
    if (device != null &&
        device.statusList != null &&
        device.statusList!.isNotEmpty) {
      var batWarm =
          device.statusList!.firstWhere((element) => element.code == "batWarm");
      var recTemp =
          device.statusList!.firstWhere((element) => element.code == "recTemp");
      if (batWarm.value && recTemp.value > 42) {
        device.isHighTemp = true;
      }
    }
  }

  /// 加载异常数据
  /*
  Future<void> loadAlarmInfos() async {
    try {
      Map<String, dynamic> params = {
        'current': '1',
        'pageSize': '20',
        'pageNo': '0',
        'userId': Global.profile.user?.id,
      };
      // '/api/deviceEvent/leaveList?current=1&pageSize=20&pageNo=0'
      final data = await Http.get('/api/deviceEvent/leaveList', data: params);
      final result = ResultModel<ListModel<AlarmModel>>.fromJson(
        data,
            (json) => ListModel.fromJson(
          json as Map<String, dynamic>,
              (jsonT) => AlarmModel.fromJson(jsonT as Map<String, dynamic>),
        ),
      );
      if (result.code == 200) {
        if (result.data != null) {
          ListModel listModel = result.data;
          if (listModel.content != null && listModel.content!.length > 0) {
            AlarmModel model = listModel.content!.first;
            int _time = model.createOn.toInt();
            int _now = DateTime.now().millisecondsSinceEpoch;
            if (_now - _time < 3 * 24 * 60 * 60 * 1000) {
              // 异常
              // 是否当前设备
              if (model.device != null && model.device?.uuid == state.displayDevices?.uuid) {
                emit(state.clone()..isAlarm = true);
              }
            }
          }
        }
      } else {
        EasyLoading.showToast(result.message);
      }
      return;
    } catch (e) {
      if (e is DioError) {
        if (e.type != DioErrorType.cancel) {
          EasyLoading.showToast(e.message);
        }
      } else {
        EasyLoading.showToast(e.toString());
      }
      print(">>> loadDeviceInfos -- error:$e");
      return;
    }
  }
  */

  /// 删除某一个设备
  void deleteDeviceById(String id) {
    // 没有数据
    if (state.devices == null || state.devices!.length < 1) {
      emit(state.clone()..style = 1);
    } else {
      if (state.datas != null && state.datas!.containsKey(id)) {
        state.datas!.remove(id);
      }
      if (state.displayDevices != null && state.displayDevices!.id == id) {
        state.displayDevices = null;
        if (state.devices != null && state.devices!.length > 0) {
          state.displayDevices = state.devices!.first;
          loadDeviceInfos(id: state.displayDevices!.id);
        } else {
          state.style = 1;
        }
        emit(state.clone());
      }
    }
  }

  /// 修改设备名称
  void changeDeviceNameById(String id, String name) {
    if (state.datas != null && state.datas!.containsKey(id)) {
      DeviceModel? model = state.datas![id];
      if (model != null) {
        model.name = name;
        state.datas![id] = model;
      }
    }
    if (state.displayDevices?.id == id) {
      state.displayDevices!.name = name;
    }
    emit(state.clone());
  }

  /// 选中某一个设备
  Future<void> setDisplayDevice(DeviceModel val) async {
    emit(state.clone()..displayDevices = val);
    // 加载设备数据
    await loadDeviceInfos(id: val.id, isShare: state.displayDevices!.isShare);
  }

  /// 处理告警异常内容
  Future<void> solveWarning() async {
    try {
      //，调用这个接口 post  /deviceEvent/app/handleLeave/{id}  id是设备id  不需要参数
      final data = await Http.post(
          '/api/deviceEvent/app/handleLeave/${state.displayDevices?.id}');
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        print(">>> 已处理离车警告 ---------- ");
      } else {
        EasyLoading.showToast(result.message);
      }
      return;
    } catch (e) {
      EasyLoading.showToast(e.toString());
      return;
    }
  }

  /// 左保护
  Future<bool> setLeftEvent() async {
    if (state.displayDevices!.isOnline) {
      bool _isLeft = !state.displayDevices!.isLeft;
      // 发送指令
      bool _left_res = await sendCommand(
          id: state.displayDevices!.id,
          code: 'protectionLeftSw',
          value: _isLeft);
      if (_left_res) {
        state.displayDevices!.isLeft = _isLeft;
        emit(state.clone());
        return true;
      }
    }
    return false;

    // state.displayDevices!.isLeft = !state.displayDevices!.isLeft;
    // emit(state.clone());
    // return false;
  }

  /// 右保护
  Future<bool> setRightEvent() async {
    if (state.displayDevices!.isOnline) {
      bool _isRight = !state.displayDevices!.isRight;
      // 发送指令
      bool _right_res = await sendCommand(
          id: state.displayDevices!.id,
          code: 'protectionRightSw',
          value: _isRight);
      if (_right_res) {
        state.displayDevices!.isRight = _isRight;
        emit(state.clone());
        return true;
      }
    }
    return false;

    // state.displayDevices!.isRight = !state.displayDevices!.isRight;
    // emit(state.clone());
    // return false;
  }

  /// 氛围灯
  Future<bool> setLampEvent() async {
    if (state.displayDevices!.isOnline) {
      bool _isLamp = !state.displayDevices!.isLamp;
      // 发送指令
      bool _lamp_res = await sendCommand(
          id: state.displayDevices!.id, code: 'F_light', value: _isLamp);
      if (_lamp_res) {
        state.displayDevices!.isLamp = _isLamp;
        emit(state.clone());
        return true;
      }
    }
    return false;

    // state.displayDevices!.isLamp = !state.displayDevices!.isLamp;
    // emit(state.clone());
    // return false;
  }

  /// 通风
  Future<bool> setWindEvent() async {
    if (state.displayDevices!.isOnline) {
      bool _isWind = !state.displayDevices!.isWind;
      // 发送指令
      bool _wind_res = await sendCommand(
          id: state.displayDevices!.id, code: 'fanSw', value: _isWind);
      if (_wind_res) {
        state.displayDevices!.isWind = _isWind;
        if (state.displayDevices!.isWind == true) {
          bool _auto_res = await sendCommand(
              id: state.displayDevices!.id, code: 'autoMode', value: false);
          bool _host_res = await sendCommand(
              id: state.displayDevices!.id, code: 'hotSw', value: false);
          if (_auto_res) state.displayDevices!.isAuto = false;
          if (_host_res) state.displayDevices!.isHost = false;
        }
        emit(state.clone());
        return true;
      }
    }
    return false;

    // state.displayDevices!.isWind = !state.displayDevices!.isWind;
    // if (state.displayDevices!.isWind == true) {
    //   state.displayDevices!.isHost = false;
    //   state.displayDevices!.isAuto = false;
    // }
    // emit(state.clone());
    // return false;
  }

  /// 加热
  Future<bool> setHostEvent() async {
    if (state.displayDevices!.isOnline) {
      bool _isHost = !state.displayDevices!.isHost;
      // 发送指令
      bool _host_res = await sendCommand(
          id: state.displayDevices!.id, code: 'hotSw', value: _isHost);
      if (_host_res) {
        state.displayDevices!.isHost = _isHost;
        if (state.displayDevices!.isHost == true) {
          bool _wind_res = await sendCommand(
              id: state.displayDevices!.id, code: 'fanSw', value: false);
          bool _auto_res = await sendCommand(
              id: state.displayDevices!.id, code: 'autoMode', value: false);
          if (_wind_res) state.displayDevices!.isWind = false;
          if (_auto_res) state.displayDevices!.isAuto = false;
        }
        emit(state.clone());
        return true;
      }
    }
    return false;

    // state.displayDevices!.isHost = !state.displayDevices!.isHost;
    // if (state.displayDevices!.isHost == true) {
    //   state.displayDevices!.isWind = false;
    //   state.displayDevices!.isAuto = false;
    // }
    // emit(state.clone());
    // return false;
  }

  /// 自动
  Future<bool> setAutoEvent() async {
    // 判断是否在线
    if (state.displayDevices!.isOnline) {
      state.displayDevices!.isAutoDoing = true;
      emit(state.clone());
      await Future.delayed(const Duration(seconds: 1));
      bool _isAuto = !state.displayDevices!.isAuto;
      // 发送指令
      bool _auto_res = await sendCommand(
          id: state.displayDevices!.id, code: 'autoMode', value: _isAuto);
      state.displayDevices!.isAutoDoing = false;

      if (_auto_res) {
        state.displayDevices!.isAuto = _isAuto;
        // if (state.displayDevices!.isAuto == true) {
        //   bool _wind_res = await sendCommand(
        //       id: state.displayDevices!.id, code: 'fanSw', value: false);
        //   bool _host_res = await sendCommand(
        //       id: state.displayDevices!.id, code: 'hotSw', value: false);
        //   if (_wind_res) state.displayDevices!.isWind = false;
        //   if (_host_res) state.displayDevices!.isHost = false;
        // }
        emit(state.clone());
        return true;
      }
    }
    emit(state.clone());
    return false;

    // state.displayDevices!.isAuto = !state.displayDevices!.isAuto;
    // if (state.displayDevices!.isAuto == true) {
    //   state.displayDevices!.isWind = false;
    //   state.displayDevices!.isHost = false;
    // }
    // // state.url = getAutoGifImage();
    // emit(state.clone());
    // return false;
  }

  final waitTime = 2000;
  bool isDoing = false;
  Future<bool> setCommandEvent(String code, dynamic newValue) async {
    isDoing = true;
    var time1 = DateTime.now();
    // 判断是否在线
    var result = false;
    if (state.displayDevices!.isOnline) {
      emit(state.clone());

      // 发送指令
      bool res = await sendCommand(
          id: state.displayDevices!.id, code: code, value: newValue);

      var time2 = DateTime.now();
      var diffTime =
          time2.millisecondsSinceEpoch - time1.millisecondsSinceEpoch;
      var _waitTime = waitTime;
      if (code.contains('protection')) {
        _waitTime = 8000;
      }
      if (diffTime < _waitTime) {
        await Future.delayed(Duration(milliseconds: (_waitTime - diffTime)));
      }
      isDoing = false;
      await loadDeviceInfos(
          id: state.displayDevices!.id, isShare: state.displayDevices!.isShare);
      if (res) {
        emit(state.clone());
        result = true;
      }
    }
    emit(state.clone());
    isDoing = false;
    return result;
  }

  /// 发送指令
  Future<bool> sendCommand({String? id, String? code, dynamic value}) async {
    try {
      List<Map<String, dynamic>> command = [
        {'code': code, 'value': value}
      ];
      debugPrint(">>> sendCommand -- command:$command");

      Map<String, dynamic> params = {'commands': command};
      final data = await Http.post('/api/device/controll/$id', data: params);
      final result =
          ResultModel.fromJson(data as Map<String, dynamic>, (json) => json);
      if (result.code == 200) {
        return true;
      } else {
        EasyLoading.showToast(result.message);
      }
      return false;
    } catch (e) {
      print(">>> sendCommand -- error:$e");
      EasyLoading.showToast("请求异常");
      return false;
    }
  }

  /// 获取右侧按钮静态背景图片
  String getInitStillImage() {
    bool _isLeft = state.displayDevices!.isLeft;
    bool _isRight = state.displayDevices!.isRight;

    if (state.displayDevices == null) return 'assets/images/gif/A_0_0_0.png';

    if (_isLeft == true && _isRight == true) {
      return 'assets/images/gif/A_1_0_1.png';
    } else if (_isLeft == false && _isRight == true) {
      return 'assets/images/gif/A_1_0_0.png';
    } else if (_isLeft == true && _isRight == false) {
      return 'assets/images/gif/A_0_0_1.png';
    } else {
      return 'assets/images/gif/A_0_0_0.png';
    }
  }

  /// 获取左保护gif
  String getLeftGifImage() {
    /*
    if (state.displayDevices!.isLeft == true) {
      if (state.displayDevices!.isRight == true) {
        return 'assets/images/gif/' + '_10_c1d.gif';
      } else {
        return 'assets/images/gif/' + '_2_a1b.gif';
      }
    } else {
      if (state.displayDevices!.isRight == true) {
        return 'assets/images/gif/' + '_14_d3c.gif';
      } else {
        return 'assets/images/gif/' + '_6_b3a.gif';
      }
    }
    */
    final _isLeft = state.displayDevices!.isLeft;
    final _isRight = state.displayDevices!.isRight;

    // 左侧开启
    if (_isLeft == true) {
      if (_isRight == true) {
        return 'assets/images/gif/_5_b2d.gif';
      } else {
        return 'assets/images/gif/_1_a2c.gif';
      }
    }
    // 左侧关闭
    else {
      if (_isRight == true) {
        return 'assets/images/gif/_13_d4b.gif';
      } else {
        return 'assets/images/gif/_9_c4a.gif';
      }
    }
  }

  /// 获取右保护gif
  String getRightGifImage() {
    /*
    if (state.displayDevices!.isRight == true) {
      if (state.displayDevices!.isLeft == true) {
        return 'assets/images/gif/' + '_5_b2d.gif';
      } else {
        return 'assets/images/gif/' + '_1_a2c.gif';
      }
    } else {
      if (state.displayDevices!.isLeft == true) {
        return 'assets/images/gif/' + '_13_d4b.gif';
      } else {
        return 'assets/images/gif/' + '_9_c4a.gif';
      }
    }
    */
    final _isLeft = state.displayDevices!.isLeft;
    final _isRight = state.displayDevices!.isRight;

    // 右侧开始
    if (_isRight == true) {
      if (_isLeft == true) {
        return 'assets/images/gif/_10_c1d.gif';
      } else {
        return 'assets/images/gif/_2_a1b.gif';
      }
    }
    // 右侧关闭
    else {
      if (_isLeft == true) {
        return 'assets/images/gif/_14_d3c.gif';
      } else {
        return 'assets/images/gif/_6_b3a.gif';
      }
    }
  }

  /// 获取氛围灯gif
  String getLampGifImage() {
    /*
    if (state.displayDevices!.isLamp == true) {
      if (state.displayDevices!.isLeft == true &&
          state.displayDevices!.isRight == true) {
        return 'assets/images/gif/' + '_15_d5d.gif';
      } else if (state.displayDevices!.isLeft == true &&
          state.displayDevices!.isRight == false) {
        return 'assets/images/gif/' + '_7_b5b.gif';
      } else if (state.displayDevices!.isLeft == false &&
          state.displayDevices!.isRight == true) {
        return 'assets/images/gif/' + '_11_c5c.gif';
      } else {
        return 'assets/images/gif/' + '_3_a5a.gif';
      }
    } else {
      if (state.displayDevices!.isLeft == true &&
          state.displayDevices!.isRight == true) {
        return 'assets/images/gif/' + '_16_d6d.gif';
      } else if (state.displayDevices!.isLeft == true &&
          state.displayDevices!.isRight == false) {
        return 'assets/images/gif/' + '_8_b6b.gif';
      } else if (state.displayDevices!.isLeft == false &&
          state.displayDevices!.isRight == true) {
        return 'assets/images/gif/' + '_12_c6c.gif';
      } else {
        return 'assets/images/gif/' + '_4_a6a.gif';
      }
    }
    */
    final _isLamp = state.displayDevices!.isLamp;
    final _isLeft = state.displayDevices!.isLeft;
    final _isRight = state.displayDevices!.isRight;

    // 氛围灯开启
    if (_isLamp == true) {
      if (_isLeft == true && _isRight == true) {
        return 'assets/images/gif/_15_d5d.gif';
      } else if (_isLeft == true && _isRight == false) {
        return 'assets/images/gif/_11_c5c.gif';
      } else if (_isLeft == false && _isRight == true) {
        return 'assets/images/gif/_8_b6b.gif';
      } else {
        return 'assets/images/gif/_3_a5a.gif';
      }
    }
    // 氛围灯关闭
    else {
      if (_isLeft == true && _isRight == true) {
        return 'assets/images/gif/_16_d6d.gif';
      } else if (_isLeft == true && _isRight == false) {
        return 'assets/images/gif/_12_c6c.gif';
      } else if (_isLeft == false && _isRight == true) {
        return 'assets/images/gif/_7_b5b.gif';
      } else {
        return 'assets/images/gif/_4_a6a.gif';
      }
    }
  }

  /// 获取通风
  String getWindGifImage() {
    /*
    if (state.displayDevices!.isLeft == true &&
        state.displayDevices!.isRight == true) {
      return 'assets/images/gif/' + 'D_WIND_G.gif';
    } else if (state.displayDevices!.isLeft == false &&
        state.displayDevices!.isRight == true) {
      return 'assets/images/gif/' + 'C_WIND_G.gif';
    } else if (state.displayDevices!.isLeft == true &&
        state.displayDevices!.isRight == false) {
      return 'assets/images/gif/' + 'B_WIND_G.gif';
    } else {
      return 'assets/images/gif/' + 'A_WIND_G.gif';
    }
    */
    final _isLeft = state.displayDevices!.isLeft;
    final _isRight = state.displayDevices!.isRight;

    if (_isLeft == true && _isRight == true) {
      return 'assets/images/gif/' + 'D_WIND_G.gif';
    } else if (_isLeft == true && _isRight == false) {
      return 'assets/images/gif/' + 'C_WIND_G.gif';
    } else if (_isLeft == false && _isRight == true) {
      return 'assets/images/gif/' + 'B_WIND_G.gif';
    } else {
      return 'assets/images/gif/' + 'A_WIND_G.gif';
    }
  }

  /// 获取加热gif
  String getHostGifImage() {
    /*
    if (state.displayDevices!.isLeft == true &&
        state.displayDevices!.isRight == true) {
      return 'assets/images/gif/' + 'D_HOT_G.gif';
    } else if (state.displayDevices!.isLeft == false &&
        state.displayDevices!.isRight == true) {
      return 'assets/images/gif/' + 'C_HOT_G.gif';
    } else if (state.displayDevices!.isLeft == true &&
        state.displayDevices!.isRight == false) {
      return 'assets/images/gif/' + 'B_HOT_G.gif';
    } else {
      return 'assets/images/gif/' + 'A_HOT_G.gif';
    }
    */
    final _isLeft = state.displayDevices!.isLeft;
    final _isRight = state.displayDevices!.isRight;

    // 左右两侧保护开启 + 加热
    if (_isLeft == true && _isRight == true) {
      return 'assets/images/gif/' + 'D_HOT_G.gif';
    }
    // 右侧保护开启 + 加热
    else if (_isLeft == true && _isRight == false) {
      return 'assets/images/gif/' + 'C_HOT_G.gif';
    }
    // 左侧保护开启 + 加热
    else if (_isLeft == false && _isRight == true) {
      return 'assets/images/gif/' + 'B_HOT_G.gif';
    }
    // 加热
    else {
      return 'assets/images/gif/' + 'A_HOT_G.gif';
    }
  }

  /// 自动模式
  String getAutoGifImage() {
    return getInitStillImage();
  }

  /// 获取初始化gif/img素材
  String getInitBgSources() {
    if (state.displayDevices == null) return 'assets/images/gif/A_0_0_0.png';

    if (state.displayDevices!.isHost) {
      return getHostGifImage();
    } else if (state.displayDevices!.isWind) {
      return getWindGifImage();
    } else if (state.displayDevices!.isAuto) {
      return getAutoGifImage();
    } else {
      return getInitStillImage();
    }
  }

  /// 重置
  void reset() {
    Rx.homeGifPlaySubject.add(0);
    emit(state.clone()
      ..style = 0
      ..loadError = null
      ..datas = null
      ..displayDevices = null);
  }
}

// '_2_a1b.gif',     // 左开（动） - 灯关 - 右关
// '_6_b3a.gif',     // 左关（动） - 灯关 - 右关
// '_1_a2c.gif',     // 左关 - 灯关 - 右开（动）
// '_9_c4a.gif',     // 左关 - 灯关 - 右关（动）
// '_3_a5a.gif',     // 左关 - 灯开（动） - 右关
// '_4_a6a.gif',     // 左关 - 灯关（动） - 右关
// '_10_c1d.gif',    // 左开（动） - 灯关 - 右开
// '_14_d3c.gif',    // 左开（动） - 灯关 - 右开
// '_5_b2d.gif',     // 左开 - 灯关 - 右开（动）
// '_13_d4b.gif',    // 左开 - 灯关 - 右关（动）
// '_7_b5b.gif',     // 左开 - 灯开（动） - 右关
// '_8_b6b.gif',     // 左开 - 灯关（动） - 右关
// '_11_c5c.gif',    // 左关 - 灯开（动） - 右开
// '_12_c6c.gif',    // 左关 - 灯关（动） - 右开
// '_15_d5d.gif',    // 左开 - 灯开（动） - 右开
// '_16_d6d.gif',    // 左开 - 灯关（动） - 右开
