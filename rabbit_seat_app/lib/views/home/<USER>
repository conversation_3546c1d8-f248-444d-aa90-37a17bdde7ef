import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/circle/banner.dart';
import 'package:rabbit_seat_app/models/device/device_model.dart';
import 'package:rabbit_seat_app/test_data.dart';
import 'package:rabbit_seat_app/views/home/<USER>';
import 'package:rabbit_seat_app/widgets/circle/banner_widget.dart';

class IndexView extends StatefulWidget {
  const IndexView({Key? key}) : super(key: key);

  @override
  State<IndexView> createState() => _IndexViewState();
}

class _IndexViewState extends State<IndexView> {
  List<BannerModel> banners = [];
  List<DeviceModel> devices = [];
  void initBanners() async {
    var list = await Business.loadBannerDatas();
    setState(() {
      banners = list;
    });
  }

  void initDeviceList() async {
    var list = await Business.getDevices();
    setState(() {
      devices = list;// .isEmpty ? testDevices : list;
    });
  }

  void init() {
    initBanners();
    initDeviceList();
  }

  @override
  void initState() {
    super.initState();
    init();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 使用 CustomScrollView 时，通常不需要在外部包裹一个 Container 来做背景
      // 我们可以把背景直接放在 Scaffold 上
      backgroundColor: const Color(0xFFF2F6FF), // 将背景色移到这里
      body: CustomScrollView(
        // slivers 属性接收一个 Sliver 列表
        slivers: [
          // ==========================================================
          // 1. 顶部的固定内容，但它们也会跟随整体滚动
          //    使用 SliverToBoxAdapter 来包裹普通 Widget
          // ==========================================================
          SliverToBoxAdapter(
            child: Container(
              // 我们把页面左右的 padding 放在每个部分，这样更灵活
              padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top + 24, // 加上状态栏和间距
                  left: 14,
                  right: 14),
              child: BannerWidget(
                datas: banners,
                backgroundColor: Colors.transparent,
                padding: EdgeInsets.all(0),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Container(
                padding:
                    EdgeInsets.only(top: 24, bottom: 24, left: 14, right: 14),
                child: Row(
                  children: [
                    Text(
                      "全部设备",
                      style:
                          TextStyle(fontSize: 21, fontWeight: FontWeight.bold),
                    ),
                    const Spacer(),
                    // 加号图标
                    IconButton(
                      icon: const Icon(Icons.add_circle_outline, size: 30),
                      onPressed: () {
                        Business.showAddDeviceSelect(context);
                      },
                    ),
                  ],
                )),
          ),

          // ==========================================================
          // 2. 中间的设备网格
          //    使用 SliverGrid
          // ==========================================================
          // 为了让 Grid 和广告的左右边距对齐，我们给它也包裹一个 SliverPadding
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 14),
            sliver: SliverGrid.builder(
              itemCount: devices.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 11,
                crossAxisSpacing: 11,
                // childAspectRatio: 1.8,
              ),
              itemBuilder: (context, index) {
                final device = devices[index];
                return InkWell(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Image.asset(
                                'assets/images/device/p2.png',
                                width: 46,
                                height: 62,
                                fit: BoxFit.fill,
                              ),
                              Text(device.online ? "在线" : "离线",
                                  style: TextStyle(
                                      color: device.online
                                          ? Business.mainColor
                                          : Color(0xff515151),
                                      fontSize: 12)),
                            ],
                          ),
                        ),
                        Container(
                          child: Text(
                            device.name ?? "宝宝的座椅",
                            style: const TextStyle(
                                fontSize: 14, fontWeight: FontWeight.bold),
                          ),
                        )
                      ],
                    ),
                  ),
                  onTap: () {
                    Navigator.pushNamed(context, "/device", arguments: device);
                  },
                );
              },
            ),
          ),

          // ==========================================================
          // 3. 和 GridView 一起滚动的底部广告行-TODO 下次再添加，本次先注释掉
          //    同样使用 SliverToBoxAdapter
          // ==========================================================
          // SliverToBoxAdapter(
          //   child: Container(
          //     height: 100, // 广告的高度
          //     margin: const EdgeInsets.all(14), // 与其他元素的间距
          //     decoration: BoxDecoration(
          //       color: Colors.teal[100],
          //       borderRadius: BorderRadius.circular(12),
          //     ),
          //     child: Center(
          //       child: Text(
          //         "这个广告会跟着列表一起滚动",
          //         style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
