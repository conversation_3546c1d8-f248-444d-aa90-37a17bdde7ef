import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/utils/global.dart';
import 'package:rabbit_seat_app/utils/utils.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../models/privacyPolicy/privacy_policy_model.dart';
import '../../services/help_service.dart';


/**
 * 启动页
 */
class LaunchPage extends StatefulWidget {
  const LaunchPage({Key? key}) : super(key: key);

  @override
  State<LaunchPage> createState() => _LaunchPageState();
}

class _LaunchPageState extends State<LaunchPage> {
  GlobalKey _globalKey = GlobalKey();

  /// 协议数据
  PrivacyPolicyModel? info;

  @override
  void initState() {
    super.initState();

    _loadDatas();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: Image(
              image: AssetImage('assets/images/launch.png'),
              fit: BoxFit.cover,
            ),
          ),
        ],
      ),
    );
  }

  /// 加载用户协议数据
  void _loadDatas() async {
    try {
      List<PrivacyPolicyModel>? list =
          await HelpService.loadPrivacyPolicyDatas();
      if (list != null && list.length > 0) {
        list.forEach((element) {
          if (element.codeName == 'app.help.privacyPolicy') {
            info = element;
          }
          _showPolicyDialog(context);
        });
      } else {
        EasyLoading.showToast('隐私政策数据加载失败！');
      }
    } catch (e) {
      EasyLoading.showToast('隐私政策数据加载失败！');
    }
  }

  /// 显示隐私政策
  void _showPolicyDialog(BuildContext context) {
    showDialog(
        barrierDismissible: false,
        barrierColor: Colors.black12,
        context: context,
        builder: (_) {
          return Material(
            color: Colors.transparent,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  width: MediaQuery.of(context).size.width * 0.85,
                  height: MediaQuery.of(context).size.height * 0.75,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  padding:
                      EdgeInsets.only(left: 18, right: 18, top: 25, bottom: 30),
                  child: Column(
                    children: [
                      Text(
                        '隐私政策提示',
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: Colors.black),
                      ),
                      SizedBox(
                        height: 12,
                      ),
                      Expanded(
                        child: ListView(
                          children: [Html(data: info!.codeValue)],
                        ),
                      ),
                      SizedBox(
                        height: 45,
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                Global()
                                    .sharedPreferences
                                    .setBool('agreementKey', false);
                                SystemNavigator.pop();
                              },
                              child: Container(
                                height: 44,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(22),
                                  border: Border.all(
                                      color: Colors.orange, width: 1),
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  "不同意",
                                  style: TextStyle(
                                      fontSize: 16, color: Colors.orange),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 15,
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                Util.initOtherSdk();
                                Global()
                                    .sharedPreferences
                                    .setBool('agreementKey', true);
                                if (Global.profile.token == null ||
                                    Global.profile.token!.isEmpty) {
                                  Navigator.pushNamedAndRemoveUntil(
                                      context, '/login-code', (route) => false);
                                } else {
                                  Navigator.pushNamedAndRemoveUntil(
                                      context, '/', (route) => false);
                                }
                              },
                              child: Container(
                                height: 44,
                                decoration: BoxDecoration(
                                  color: Business.mainColor,
                                  borderRadius: BorderRadius.circular(22),
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  "同意",
                                  style: TextStyle(
                                      fontSize: 16, color: Colors.white),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              ],
            ),
          );
        });
  }
}
