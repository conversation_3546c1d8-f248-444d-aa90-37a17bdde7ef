import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:rabbit_seat_app/models/privacyPolicy/privacy_policy_model.dart';
import 'package:rabbit_seat_app/widgets/index.dart';

import '../../models/index.dart';
import '../../services/help_service.dart';

class SimRechargePolicyView extends StatefulWidget {
  const SimRechargePolicyView({Key? key}) : super(key: key);

  @override
  State<SimRechargePolicyView> createState() => _SimRechargePolicyViewState();
}

class _SimRechargePolicyViewState extends State<SimRechargePolicyView> {
  /// 协议数据
  // PolicyModel? info;
  PrivacyPolicyModel info = PrivacyPolicyModel();

  @override
  void initState() {
    super.initState();
    info.codeValue = "";
    _loadDatas();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefAppBar(context: context, title: "充值协议"),
      body: Container(
        padding: EdgeInsets.only(
            left: 20,
            right: 20,
            top: 20,
            bottom: MediaQuery.of(context).padding.bottom + 20),
        child: info != null
            ? ListView(
                children: [Html(data: info.codeValue)],
              )
            : SizedBox(),
      ),
    );
  }

  /// 加载用户协议数据
  void _loadDatas() async {
    EasyLoading.show();
    PrivacyPolicyModel? item = await HelpService.loadSimRechargePolicyDatas();
    EasyLoading.dismiss();
    if (item != null) {
      info = item;
      if (mounted) setState(() {});
    }
    // HelpService.loadAgreementData(success: (data) {
    //   info = data;
    //   if (mounted) setState(() {});
    //   EasyLoading.dismiss();
    // }, failure: (msg) {
    //   EasyLoading.dismiss();
    //   EasyLoading.showToast(msg);
    // });
  }
}
