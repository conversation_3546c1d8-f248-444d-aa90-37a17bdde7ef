import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import '../../models/device/device_model.dart';
import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import '../../utils/rx.dart';
import 'dev_ruled_state.dart';

class DevRuledCubit extends Cubit<DevRuledState> {
  DevRuledCubit({Map<String, dynamic>? arguments})
      : super(DevRuledState().init()) {
    if (arguments != null && arguments.containsKey('id')) {
      loadDeviceInfos(id: arguments['id']);
      _init();
    } else {
      state.loadError = '设备Id不能为空！';
      EasyLoading.showToast('设备Id不能为空！');
    }
  }

  void _init() {
    // 修改设备名称
    Rx.modifyDeviceNameByIdSubject.listen((event) {
      Map<String, dynamic>? datas = event as Map<String, dynamic>?;
      if (datas != null && datas.containsKey('id') && datas.containsKey('val')) {
        String id = datas['id'];
        String name = datas['val'];
        if (state.device?.id == id) {
          state.device!.name = name;
          emit(state.clone());
        }
      }
    });
  }

  /// 加载设备信息
  Future<void> loadDeviceInfos({String? id}) async {
    try {
      if (id == null) return;
      final data = await Http.get('/api/device/$id');
      final result = ResultModel<DeviceModel>.fromJson(
          data, (json) => DeviceModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (result.data != null) {
          state.loadError = null;
          state.device = result.data;
          emit(state.clone());
        }
      } else {
        EasyLoading.showToast(result.message);
        emit(state.clone()
          ..loadError = result.message
          ..device = null);
      }
      return;
    } catch (e) {
      print(">>> loadDeviceInfos -- error:$e");
      EasyLoading.showToast(e.toString());
      emit(state.clone()
        ..loadError = '数据加载异常！'
        ..device = null);
      return;
    }
  }

  /// 删除设备
  Future<bool> deleteDevice() async {
    try {
      EasyLoading.show();
      final data = await Http.post('/api/device/unbundling/${state.device?.id}',
          options: Options(contentType: 'application/x-www-form-urlencoded'));
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        EasyLoading.showToast("删除成功！");
        return true;
      } else {
        EasyLoading.showToast(result.message);
      }
      return false;
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
      return false;
    }
  }
}
