import 'package:city_pickers/city_pickers.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/device/device_model.dart';
import 'package:rabbit_seat_app/models/home/<USER>';
import 'package:rabbit_seat_app/models/share/share_model.dart';
import 'package:rabbit_seat_app/utils/global.dart';
import 'package:rabbit_seat_app/utils/signal.dart';
import 'package:rabbit_seat_app/views/device/share_add.dart';

import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import '../../widgets/common/appbar.dart';
import '../../widgets/dialog/dialog.dart';

/// 设备共享
class DevicesShareView extends StatefulWidget {
  final Map<String, dynamic>? arguments;

  const DevicesShareView({Key? key, this.arguments}) : super(key: key);

  @override
  State<DevicesShareView> createState() => _DevicesShareViewState();
}

class _DevicesShareViewState extends State<DevicesShareView> {
  DeviceModel? data;

  String? id;

  // int homeId = -1;

  /// 共享用户
  List<ShareModel> _items = [];

  int homeId = 0;

  @override
  void initState() {
    super.initState();

    if (widget.arguments != null) {
      if (widget.arguments!.containsKey('id')) {
        id = widget.arguments!["id"];
        _loadDatas();
      }
    }

    // 加载数据
    // _loadDatas();
    // 加载共享数据
    // _loadShareDatas();

    KitSignal().link('cancel-share', 'share-manager', (arg) {
      _loadShareDatas(data!.homeId.toInt());
    });
  }

  @override
  void dispose() {
    KitSignal().off('cancel-share', 'share-manager');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    String _title = '共享管理';
    // if (data != null) {
    //   _title = data!.name != null ? "共享 ${data!.name}" : '';
    // }

    return Scaffold(
      appBar: DefAppBar(context: context, title: _title),
      body: _bodyWidget(context),
    );
  }

  /// 主体
  Widget _bodyWidget(BuildContext context) {
    if (data == null) return Container();

    Widget _deviceImageWidget = Image.asset(
      'assets/images/device/p1.png',
      fit: BoxFit.fitHeight,
    ); //Image.asset("assets/images/add4.png");
    // if (data != null && data!.icon != null) {
    //   _deviceImageWidget = Image.network(
    //     data!.icon!,
    //   );
    // }

    return Padding(
      padding: EdgeInsets.only(left: 12, right: 12),
      child: Stack(
        children: [
          Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: Center(
                child: Container(
                  // color: Colors.green,
                  margin: EdgeInsets.only(top: 18),
                  height: 300.w,
                  width: 150.h,
                  child: _deviceImageWidget,
                ),
              )),
          Positioned(left: 0, top: 270.h, right: 0, child: Container(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _shareManageWidget(),
                _shareItemWidget(
                    asset: "assets/images/share/1.png",
                    title: "手机号邀请",
                    onTap: () {
                      print("手机号邀请");

                      if (data != null) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => ShareAddView(
                              arguments: {
                                "homeId": data!.homeId,
                                "deviceId": data!.id,
                                "expTime": data!.activeTime,
                              },
                            ),
                          ),
                        ).then((value) {
                          _loadShareDatas(data!.homeId.toInt());
                        });
                      }
                    }),
              ],
            ),
          )),
        ],
      ),
    );
    // return Padding(
    //   padding: EdgeInsets.only(left: 12, right: 12),
    //   child: Column(
    //     children: [
    //       Container(
    //         // color: Colors.green,
    //         margin: EdgeInsets.only(top: 18),
    //         height: 300,
    //         width: 150,
    //         child: _deviceImageWidget,
    //       ),
    //       _shareManageWidget(),
    //       /*
    //       _shareItemWidget(
    //           asset: "assets/images/share/2.png",
    //           title: "微信邀请",
    //           onTap: () {
    //             print("微信邀请");
    //           }),
    //        */
    //       _shareItemWidget(
    //           asset: "assets/images/share/1.png",
    //           title: "手机号邀请",
    //           onTap: () {
    //             print("手机号邀请");
    //
    //             if (data != null) {
    //               Navigator.push(
    //                 context,
    //                 MaterialPageRoute(
    //                   builder: (_) => ShareAddView(
    //                     arguments: {
    //                       "homeId": data!.homeId,
    //                       "deviceId": data!.id,
    //                       "expTime": data!.activeTime,
    //                     },
    //                   ),
    //                 ),
    //               ).then((value) {
    //                 _loadShareDatas(data!.homeId.toInt());
    //               });
    //             }
    //           }),
    //     ],
    //   ),
    // );
  }

  /// 共享管理
  Widget _shareManageWidget() {
    // 列表空间
    Widget _contentWidget = Container();
    if (_items != null && _items.length > 0) {
      _contentWidget = GridView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _items.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 1,
          mainAxisSpacing: 10.0,
          crossAxisSpacing: 14.0,
          childAspectRatio: 1,
        ),
        itemBuilder: (context, index) {
          ShareModel _model = _items[index];
          String _name = '';

          // 头像
          Widget _portraitWidget = Container(
            color: Colors.grey,
          );
          if (_model.user != null) {
            // 头像
            if (_model.user!.avatar != null) {
              _portraitWidget = Image.network(
                kReleaseBaseUrl + _model.user!.avatar!,
                fit: BoxFit.cover,
              );
            }

            /// 放开注释
            if (_model.user!.avatar != null) {
              if (_model.user!.avatar!.startsWith('http')) {
                _portraitWidget = Image.network(
                  '${_model.user!.avatar}',
                  fit: BoxFit.cover,
                );
              } else {
                _portraitWidget = Image.network(
                  '$kReleaseBaseUrl${_model.user!.avatar}',
                  fit: BoxFit.cover,
                );
              }
            }

            // 昵称
            if (_model.user!.nickName != null &&
                _model.user!.nickName!.isNotEmpty) {
              _name = _model.user!.nickName ?? '';
            } else if (_model.user!.telephone != null) {
              _name = _model.user!.telephone!
                  .replaceFirst(RegExp(r'\d{4}'), '****', 3);
            }
          }
          if (_model.user == null) _name = '新用户';
          return GestureDetector(
            onLongPress: () {
              print("长按删除");

              print("删除共享");
              DefDialog.showDialog1(
                context: context,
                title: "提示",
                message: "关闭TA的共享权限？",
                confirm: () {
                  print("确定取消共享");
                  _cancelShared(_model);
                },
                cancel: () {
                  print("取消不操作");
                },
              );
            },
            child: Container(
              alignment: Alignment.center,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ClipOval(
                    child: SizedBox(
                      width: 45,
                      height: 45,
                      child: _portraitWidget,
                      // child: Image.network(
                      //   'https://img0.baidu.com/it/u=1537915733,2247420980&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
                      //   fit: BoxFit.fill,
                      // ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 5),
                    child: Text(
                      _name,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF666666),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
    return Container(
      padding: EdgeInsets.only(left: 18, right: 18),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12)),
        boxShadow: [
          BoxShadow(
            color: Color(0x17828282),
            offset: Offset(0, 0),
            blurRadius: 10.5,
            spreadRadius: 5,
          )
        ],
      ),
      child: Container(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 55,
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "共享管理",
                      style: TextStyle(
                        fontSize: 15,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      print("共享管理");
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => ShareManagePage(
                                arguments: {"data": data, "items": _items},
                              )));
                    },
                    child: Container(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _items.length < 1
                                ? "无共享人员"
                                : "已共享至${_items.length}人",
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(0xFF666666),
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          SizedBox(
                            width: 8,
                            height: 13,
                            child: Image.asset("assets/images/share/3.png"),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Divider(height: 1, color: Color(0xFFEAEAEA)),
            Container(
              height: 110,
              padding: EdgeInsets.only(top: 15, bottom: 20),
              child: _contentWidget,
            )
          ],
        ),
      ),
    );
  }

  /// 取消共享
  void _cancelShared(ShareModel model) async {
    try {
      EasyLoading.show();
      final data = await Http.del('/api/deviceShare/${model.id}');
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        _items.remove(model);
        if (mounted) setState(() {});
        KitSignal().send('cancel-share');
      } else {
        EasyLoading.showToast(result.message);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }

  /// 共享方式
  Widget _shareItemWidget(
      {required String asset, required String title, Function()? onTap}) {
    return Padding(
      padding: EdgeInsets.only(top: 11),
      child: Ink(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(12)),
          boxShadow: [
            BoxShadow(
              color: Color(0x17828282),
              offset: Offset(0, 0),
              blurRadius: 10.5,
              spreadRadius: 5,
            )
          ],
        ),
        child: InkWell(
          borderRadius: BorderRadius.all(Radius.circular(12)),
          child: Container(
            height: 55,
            padding: EdgeInsets.only(left: 20, right: 16),
            child: Row(
              children: [
                SizedBox(
                  width: 24,
                  height: 24,
                  child: Image.asset(asset),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(left: 17, right: 15),
                    child: Text(
                      title,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF000000),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                    width: 8,
                    height: 13,
                    child: Image.asset(
                      "assets/images/share/3.png",
                      fit: BoxFit.fill,
                    )),
              ],
            ),
          ),
          onTap: onTap,
        ),
      ),
    );
  }

  /// 加载数据设备数据
  void _loadDatas() async {
    try {
      EasyLoading.show();
      final data = await Http.get('/api/device/$id');
      EasyLoading.dismiss();
      final result = ResultModel<DeviceModel>.fromJson(
          data, (json) => DeviceModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (result.data != null) {
          DeviceModel _device = result.data as DeviceModel;
          this.data = _device;
          // 加载共享数据
          _loadShareDatas(_device.homeId.toInt());
        }
        if (mounted) setState(() {});
      } else {
        EasyLoading.showToast(result.message);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }

  /// 加载共享列表数据
  void _loadShareDatas(int homeId) async {
    try {
      EasyLoading.show();
      final data = await Http.get('/api/deviceShare/list/$homeId');
      EasyLoading.dismiss();
      final result = ResultModel<List<ShareModel>>.fromJson(data, (json) {
        if (json != null && json is List<dynamic>) {
          return (json as List<dynamic>)
              .map((e) => ShareModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        if (result.data != null) {
          _items = result.data as List<ShareModel>;
        }
        // 刷新页面
        if (mounted) setState(() {});
      } else {
        EasyLoading.showToast(result.message);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}

/// ----------------------------------------------------------------------
/// 共享管理 当前设备 所有共享人员
class ShareManagePage extends StatefulWidget {
  final Map<String, dynamic>? arguments;

  const ShareManagePage({Key? key, this.arguments}) : super(key: key);

  @override
  State<ShareManagePage> createState() => _ShareManagePageState();
}

class _ShareManagePageState extends State<ShareManagePage> {
  // 设备数据
  DeviceModel? _data;

  /// 共享用户
  List<ShareModel> _items = [];

  @override
  void initState() {
    super.initState();

    if (widget.arguments != null &&
        widget.arguments!.containsKey('data') &&
        widget.arguments!.containsKey('items')) {
      _data = widget.arguments!['data'];
      _items = widget.arguments!['items'] ?? [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefAppBar(context: context, title: '共享管理'),
      body: _bodyWidget(context),
    );
  }

  /// 主体元素
  Widget _bodyWidget(BuildContext context) {
    if (_data == null) return Container();

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 10, left: 12, right: 12, bottom: 10),
            child: Text(
              _items.length > 0 ? (_data!.name ?? "") : '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 11,
                color: Color(0xFF666666),
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _items.length,
              itemBuilder: _itemBuilder,
            ),
          ),
        ],
      ),
    );
  }

  /// 列表元素项
  Widget _itemBuilder(context, index) {
    ShareModel _model = _items[index];

    String _name = '';
    // 头像
    Widget _portraitWidget = Container(
      color: Colors.grey,
    );
    if (_model.user != null) {
      if (_model.user!.avatar != null) {
        _portraitWidget = Image.network(
          kReleaseBaseUrl + _model.user!.avatar!,
          fit: BoxFit.cover,
        );
      }
      if (_model.user!.nickName != null && _model.user!.nickName!.isNotEmpty) {
        _name = _model.user!.nickName ?? '';
      } else if (_model.user!.telephone != null) {
        _name =
            _model.user!.telephone!.replaceFirst(RegExp(r'\d{4}'), '****', 3);
      }
    }
    if (_model.user == null) _name = '新用户';
    String _time = formatDate(
        DateTime.fromMillisecondsSinceEpoch(_model.createOn.toInt()),
        [yyyy, '.', mm, '.', dd]);

    return Padding(
      padding: EdgeInsets.only(left: 12, right: 12, bottom: 10),
      child: Ink(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(12)),
          boxShadow: [
            BoxShadow(
              color: Color(0x17828282),
              offset: Offset(0, 0),
              blurRadius: 10.5,
              spreadRadius: 5,
            )
          ],
        ),
        child: InkWell(
          borderRadius: BorderRadius.all(Radius.circular(12)),
          child: Container(
            height: 65,
            padding: EdgeInsets.only(left: 18, right: 18),
            child: Row(
              children: [
                ClipOval(
                  child: SizedBox(
                    width: 40,
                    height: 40,
                    child: _portraitWidget,
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(left: 11),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _name,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF000000),
                          ),
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        Text(
                          _time,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 11,
                            color: Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          onLongPress: () {
            print("删除共享");
            DefDialog.showDialog1(
              context: context,
              title: "提示",
              message: "关闭TA的共享权限？",
              confirm: () {
                print("确定取消共享");
                _cancelShared(_model);
              },
              cancel: () {
                print("取消不操作");
              },
            );
          },
        ),
      ),
    );
  }

  /// 取消共享
  void _cancelShared(ShareModel model) async {
    try {
      EasyLoading.show();
      final data = await Http.del('/api/deviceShare/${model.id}');
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        _items.remove(model);
        if (mounted) setState(() {});
        KitSignal().send('cancel-share');
      } else {
        EasyLoading.showToast(result.message);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}
