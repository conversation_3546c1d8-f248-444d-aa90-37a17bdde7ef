import 'package:bloc/bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../models/device/device_model.dart';
import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import 'dev_info_state.dart';

class DevInfoCubit extends Cubit<DevInfoState> {
  DevInfoCubit({Map<String, dynamic>? arguments}) : super(DevInfoState().init()) {
    if (arguments != null && arguments.containsKey('id')) {
      _init();
      loadDeviceInfos(id: arguments['id']);
    } else {
      state.loadError = '设备ID不能为空！';
    }
  }

  void _init() async {
    /// 加载本地版本信息
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    state.version = packageInfo.version;
    emit(state.clone());
  }

  /// 加载设备信息
  Future<void> loadDeviceInfos({String? id}) async {
    try {
      if (id == null) return;
      final data = await Http.get('/api/device/$id');
      final result = ResultModel<DeviceModel>.fromJson(
          data, (json) => DeviceModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        if (result.data != null) {
          state.loadError = null;
          state.device = result.data;
          emit(state.clone());
        }
      } else {
        state.device = null;
        state.loadError = result.message;
        emit(state.clone());
        EasyLoading.showToast(result.message);
      }
      return;
    } catch (e) {
      print(">>> loadDeviceInfos -- error:$e");
      EasyLoading.showToast(e.toString());
      state.device = null;
      state.loadError = '数据加载失败！';
      emit(state.clone());
      return;
    }
  }
}
