import 'package:date_format/date_format.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/error.dart';
import 'package:rabbit_seat_app/models/home/<USER>';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/provider/device_provider.dart';
import 'package:rabbit_seat_app/utils/date_format.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/utils/signal.dart';
import 'package:rabbit_seat_app/views/device/devices_share_view.dart';
import 'package:rabbit_seat_app/views/device/modify_device_name_view.dart';
import 'package:rabbit_seat_app/views/my/meal_view.dart';
import 'package:rabbit_seat_app/widgets/dialog/dialog.dart';

import '../../models/device/device_model.dart';
import '../../widgets/buttom/ripple_button.dart';
import '../../widgets/common/appbar.dart';

/**
 * 设备管理
 */
class DeviceManageView extends StatefulWidget {
  final Map<String, dynamic>? arguments;
  const DeviceManageView({Key? key, this.arguments}) : super(key: key);

  @override
  State<DeviceManageView> createState() => _DeviceManageViewState();
}

class _DeviceManageViewState extends State<DeviceManageView> {
  // DeviceModel? data;
  // HomeDeviceModel? data;

  // String? id;
  // int homeId = -1;

  @override
  void initState() {
    super.initState();


    // if (widget.arguments != null) {
    //   if (widget.arguments!.containsKey('id')) {
    //     id = widget.arguments!["id"];
    //     _loadDatas();
    //   }
    // }

    // KitSignal().link('modify-device-name', 'device-manage', (arg) {
    //   _loadDatas();
    // });
  }

  @override
  void dispose() {
    KitSignal().off('modify-device-name', 'device-manage');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    return Scaffold(
      appBar: DefAppBar(context: context, title: "设备管理"),
      body: _bodyWidget(context),
    );
  }

  /// 主体设备
  Widget _bodyWidget(BuildContext context) {
    DeviceModel? _model = context.watch<DeviceProvider>().editorDevice;
    if (_model == null) return Container(child: Center(child: Text("无可编辑对象！")));

    // 设备名称
    String _name = '';
    String _time = '';

    _name = _model.name ?? '';
    final _date =
        DateTime.fromMillisecondsSinceEpoch(_model.activeTime.toInt());
    DateTime endDate = DateTime(_date.year + 2, _date.month, _date.day);
    _time = formatDate(endDate, [yyyy, '-', mm, '-', dd]) + ' 到期';

    return ListView(
      padding: EdgeInsets.only(left: 12.5.w, top: 12.h, right: 12.w),
      children: [
        _boxWidget(Column(
          children: [
            _menuItemWidget(
                title: "设备名称",
                subTitle: _name,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => DeviceModifyNameView(
                                arguments: {
                                  'name': _model.name,
                                  'id': _model.id
                                },
                              )));
                }),
            _divider(),
            _menuItemWidget(
                title: "流量套餐",
                subTitle: _time,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => MealView(
                                arguments: {"id": _model.id},
                              )));
                }),
            _divider(),
            Visibility(
              visible: _model.isShare != null && _model.isShare == true,
              child: _menuItemWidget(
                  title: "共享管理",
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (ctx) => DevicesShareView(arguments: {
                                  "id": _model.id,
                                  'homeId': _model.homeId
                                })));
                  }),
            ),
            _divider(),
            _menuItemWidget(
                title: "设备信息",
                onTap: () {
                  Navigator.pushNamed(context, '/device-info');
                }),
          ],
        )),
        Visibility(
          visible: _model.isShare != null && _model.isShare == true,
          child: _boxWidget(_delItemWidget(onTap: () {
            DefDialog.showDialog1(
                context: context,
                message: "删除当前设备？",
                confirm: () {
                  // _removeDevices();
                  context.read<DeviceProvider>().deleteDevice(
                      completed: (value) {
                    if (value) Navigator.pop(context);
                  });
                },
                cancel: () {});
          })),
        )
      ],
    );
  }

  /// 容器
  Widget _boxWidget(Widget child) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12.w)),
        boxShadow: [
          BoxShadow(
            color: Color(0x17828282),
            offset: Offset(0, 0),
            blurRadius: 10,
            spreadRadius: 10,
          )
        ],
      ),
      child: child,
    );
  }

  /// 分割线
  Widget _divider() {
    return Divider(
      indent: 18.w,
      endIndent: 18.w,
      height: 1,
      color: Color(0xFFEAEAEA),
    );
  }

  /// 按钮
  Widget _menuItemWidget({String? title, String? subTitle, Function()? onTap}) {
    return RippleButton(
      child: Container(
        height: 64.h,
        padding: EdgeInsets.only(left: 18.w, right: 17.5.w),
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            // 标题
            Padding(
              padding: EdgeInsets.only(left: 18.w, right: 15.w),
              child: Text(
                title ?? '',
                maxLines: 1,
                style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF000000),
                ),
              ),
            ),
            // 子项
            Expanded(
                child: subTitle != null
                    ? Container(
                        alignment: Alignment.centerRight,
                        child: Text(
                          subTitle,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Color(0xFF666666),
                          ),
                        ),
                      )
                    : Container()),
            // 角标
            Padding(
              padding: EdgeInsets.only(left: 10.w),
              child: SizedBox(
                width: 8.w,
                height: 13.h,
                child: Image(
                  image: AssetImage('assets/images/my/1.png'),
                  fit: BoxFit.fill,
                ),
              ),
            ),
          ],
        ),
      ),
      radius: 12.w,
      color: Colors.white,
      onTap: onTap,
    );
  }

  /// 按钮
  Widget _delItemWidget({Function()? onTap}) {
    return RippleButton(
      radius: 12.w,
      color: Colors.white,
      child: Container(
        height: 64.h,
        padding: EdgeInsets.only(left: 18.w, right: 17.5.w),
        alignment: Alignment.center,
        child: Text(
          "删除设备",
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
            color: Business.mainColor,
          ),
        ),
      ),
      onTap: onTap,
    );
  }

// /// 加载数据
// void _loadDatas() async {
//   try {
//     EasyLoading.show();
//     final data = await Http.get('/api/device/$id');
//     EasyLoading.dismiss();
//     final result = ResultModel<DeviceModel>.fromJson(data, (json) => DeviceModel.fromJson(json as Map<String, dynamic>));
//     if (result.code == 200) {
//       this.data = result.data as DeviceModel;
//       if (mounted) setState(() {});
//     } else {
//       EasyLoading.showToast(result.message);
//     }
//   } on MyError catch (e) {
//     EasyLoading.dismiss();
//     EasyLoading.showToast(e.message ?? "网络加载失败！");
//   } catch (e) {
//     EasyLoading.dismiss();
//     EasyLoading.showToast(e.toString());
//   }
// }

// /// 删除设备
// void _removeDevices() async {
//   try {
//     EasyLoading.show();
//     //final data = await Http.del('/api/deviceShare/$id', options: Options(contentType: 'application/x-www-form-urlencoded'));
//     final data = await Http.post('/api/device/unbundling/$id', options: Options(contentType: 'application/x-www-form-urlencoded'));
//     EasyLoading.dismiss();
//     final result = ResultModel.fromJson(data, (json) => json);
//     if (result.code == 200) {
//       EasyLoading.showToast("删除成功！");
//       KitSignal().send('del-device', id);
//       Navigator.pop(context);
//     } else {
//       EasyLoading.showToast(result.message);
//     }
//   } on MyError catch (e) {
//     EasyLoading.dismiss();
//     EasyLoading.showToast(e.message ?? "网络加载失败！");
//   } catch (e) {
//     EasyLoading.dismiss();
//     EasyLoading.showToast(e.toString());
//   }
// }
}
