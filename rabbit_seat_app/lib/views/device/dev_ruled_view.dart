import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/provider/device_provider.dart';
import 'package:rabbit_seat_app/utils/rx.dart';
import 'package:rabbit_seat_app/utils/utils.dart';
import 'package:rabbit_seat_app/views/device/dev_info_view.dart';
import 'package:rabbit_seat_app/views/device/dev_name_view.dart';

import '../../widgets/buttom/ripple_button.dart';
import '../../widgets/common/appbar.dart';
import '../../widgets/dialog/dialog.dart';
import '../my/meal_view.dart';
import 'dev_ruled_cubit.dart';
import 'dev_ruled_state.dart';
import 'devices_share_view.dart';
import 'modify_device_name_view.dart';

/**
 * 设备管理
 */
class DevRuledPage extends StatelessWidget {
  final Map<String, dynamic>? arguments;

  const DevRuledPage({Key? key, this.arguments}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕

    return BlocProvider(
      create: (BuildContext context) => DevRuledCubit(arguments: arguments),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  void setExpTime(DeviceProvider provider) async {
    String a = arguments?['iccid'];
    var iccid = Util.decodeBase64(a);
    if (iccid != null) {
      final info = await provider.getDeviceSimInfo(iccid);
      var time = info?.expiredTime;
      if (time != null) {
        provider.expireTime = time.substring(0, 10);
      }
    }
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<DevRuledCubit>(context);
    var provider = context.watch<DeviceProvider>();
    context.watch<DeviceProvider>();
    setExpTime(provider);
    return Scaffold(
      appBar: DefAppBar(context: context, title: "设备管理"),
      body:
          BlocBuilder<DevRuledCubit, DevRuledState>(builder: (context, state) {
        if (cubit.state.loadError != null) {
          return Container(
            alignment: Alignment.center,
            child: Text(cubit.state.loadError ?? '数据加载异常！'),
          );
        } else if (cubit.state.device != null) {
          final _date = DateTime.fromMillisecondsSinceEpoch(
              cubit.state.device!.activeTime.toInt());
          DateTime endDate = DateTime(_date.year + 2, _date.month, _date.day);
          String _time = formatDate(endDate, [yyyy, '-', mm, '-', dd]) + ' 到期';

          String? expTime;
          if (cubit.state.device!.expiredTime != null &&
              cubit.state.device!.expiredTime!.length >= 10) {
            expTime = cubit.state.device!.expiredTime!.substring(0, 10);
          }

          return Container(
            child: ListView(
              padding: EdgeInsets.only(left: 12.5.w, top: 12.h, right: 12.w),
              children: [
                _boxWidget(Column(
                  children: [
                    _menuItemWidget(
                        title: "设备名称",
                        subTitle: cubit.state.device?.name ?? '',
                        onTap: () {
                          Navigator.push(
                              context,
                              // MaterialPageRoute(
                              //     builder: (_) => DeviceModifyNameView(
                              //           arguments: {
                              //             'name': cubit.state.device?.name,
                              //             'id': cubit.state.device?.id
                              //           },
                              //         )));
                              MaterialPageRoute(
                                  builder: (_) => DevNamePage(
                                        arguments: {
                                          'name': cubit.state.device?.name,
                                          'id': cubit.state.device?.id
                                        },
                                      )));
                        }),
                    _divider(),
                    _menuItemWidget(
                        title: "流量套餐",
                        subTitle: expTime != null ? '$expTime到期' : '',
                        onTap: () {
                          // Navigator.push(
                          //     context,
                          //     MaterialPageRoute(
                          //         builder: (_) => MealView(
                          //               arguments: {
                          //                 "id": cubit.state.device?.id
                          //               },
                          //             )));
                        }),
                    _divider(),
                    _menuItemWidget(
                        title: "共享管理",
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (ctx) => DevicesShareView(
                                          arguments: {
                                            "id": cubit.state.device?.id,
                                            'homeId': cubit.state.device?.homeId
                                          })));
                        }),
                    _divider(),
                    _menuItemWidget(
                        title: "设备信息",
                        onTap: () {
                          // Navigator.pushNamed(context, '/device-info');
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => DevInfoPage(
                                arguments: {'id': cubit.state.device?.id},
                              ),
                            ),
                          );
                        }),
                  ],
                )),
                _boxWidget(_delItemWidget(onTap: () {
                  DefDialog.showDialog1(
                      context: context,
                      message: "删除当前设备？",
                      confirm: () async {
                        bool res = await cubit.deleteDevice();
                        if (res) {
                          Rx.removeDeviceByIdSubject
                              .add({'id': cubit.state.device?.id});
                          Navigator.pop(context);
                        }
                      },
                      cancel: () {});
                })),
              ],
            ),
          );
        } else {
          return Container(child: Center(child: Text("无可编辑对象！")));
        }
      }),
    );
  }

  /// 容器
  Widget _boxWidget(Widget child) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12.w)),
        boxShadow: [
          BoxShadow(
            color: Color(0x17828282),
            offset: Offset(0, 0),
            blurRadius: 10,
            spreadRadius: 10,
          )
        ],
      ),
      child: child,
    );
  }

  /// 分割线
  Widget _divider() {
    return Divider(
      indent: 18.w,
      endIndent: 18.w,
      height: 1,
      color: Color(0xFFEAEAEA),
    );
  }

  /// 按钮
  Widget _menuItemWidget({String? title, String? subTitle, Function()? onTap}) {
    return RippleButton(
      child: Container(
        height: 64.h,
        padding: EdgeInsets.only(left: 18.w, right: 17.5.w),
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            // 标题
            Padding(
              padding: EdgeInsets.only(left: 18.w, right: 15.w),
              child: Text(
                title ?? '',
                maxLines: 1,
                style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF000000),
                ),
              ),
            ),
            // 子项
            Expanded(
                child: subTitle != null
                    ? Container(
                        alignment: Alignment.centerRight,
                        child: Text(
                          subTitle,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Color(0xFF666666),
                          ),
                        ),
                      )
                    : Container()),
            // 角标
            Padding(
              padding: EdgeInsets.only(left: 10.w),
              child: SizedBox(
                width: 8.w,
                height: 13.h,
                child: Image(
                  image: AssetImage('assets/images/my/1.png'),
                  fit: BoxFit.fill,
                ),
              ),
            ),
          ],
        ),
      ),
      radius: 12.w,
      color: Colors.white,
      onTap: onTap,
    );
  }

  /// 按钮
  Widget _delItemWidget({Function()? onTap}) {
    return RippleButton(
      radius: 12.w,
      color: Colors.white,
      child: Container(
        height: 64.h,
        padding: EdgeInsets.only(left: 18.w, right: 17.5.w),
        alignment: Alignment.center,
        child: Text(
          "删除设备",
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
            color: Business.mainColor,
          ),
        ),
      ),
      onTap: onTap,
    );
  }
}
