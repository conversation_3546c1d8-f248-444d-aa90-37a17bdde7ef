import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/device/device_model.dart';
import 'package:rabbit_seat_app/views/device/dev_ruled_view.dart';

import '../../widgets/common/index.dart';
import '../../widgets/device/device_item.dart';
import '../../widgets/refresh/index.dart';
import 'dev_list_cubit.dart';
import 'dev_list_state.dart';

/**
 * 设备列表
 */
class DevListPage extends StatelessWidget {
  /// 刷新控制
  EasyRefreshController _controller = EasyRefreshController();

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕

    return BlocProvider(
      create: (BuildContext context) => DevListCubit(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<DevListCubit>(context);

    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: "我的设备",
      ),
      body: BlocBuilder<DevListCubit, DevListState>(builder: (context, state) {
        int _itemCount =
            cubit.state.devices != null ? cubit.state.devices!.length : 0;

        return Container(
          color: Color.fromARGB(255, 249, 249, 249),
          child: EasyRefresh.custom(
            // 没有数据组件
            emptyWidget: _itemCount < 1 ? EmptyWidget() : null,
            controller: _controller,
            header: CustomRefreshHeader(),
            firstRefresh: true,
            slivers: [
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  _itemBuilder,
                  childCount: _itemCount,
                ),
              ),
            ],
            onRefresh: () async {
              await cubit.loadMyDevices();
              _controller.finishRefresh();
            },
          ),
        );
      }),
    );
  }

  /// 列表元素
  Widget _itemBuilder(context, index) {
    // DeviceModel model = _items[index];
    // DeviceModel model = Provider.of<DeviceProvider>(context).mdevices[index];
    // DeviceModel model = context.watch<DeviceProvider>().mdevices[index];

    final cubit = BlocProvider.of<DevListCubit>(context);
    DeviceModel model = cubit.state.devices![index];
    final _date = DateTime.fromMillisecondsSinceEpoch(model.activeTime.toInt());
    DateTime endDate = DateTime(_date.year + 2, _date.month, _date.day);
    String? expTime;
    if (model.expiredTime != null && model.expiredTime!.length >= 10) {
      expTime = model.expiredTime!.substring(0, 10);
    }
    return DeviceItem(
        icon: model.icon,
        title: model.name!,
        top: 10.h,
        subTitle: expTime != null ? "流量套餐: ${expTime} 到期" : "",
        onTap: () {
          // Provider.of<DeviceProvider>(context).setEditorDevice(model);
          // context.read<DeviceProvider>().setEditorDevice(model);
          //
          // Navigator.push(
          //     context,
          //     MaterialPageRoute(
          //         builder: (ctx) =>
          //             DeviceManageView(
          //                 arguments: {"id": model.id})));

          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (_) => DevRuledPage(
                        arguments: {'id': model.id, "iccid": model.iccid},
                      )));
        });
  }
}
