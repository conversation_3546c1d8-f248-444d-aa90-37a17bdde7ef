import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/device/device_model.dart';
import 'package:rabbit_seat_app/provider/device_provider.dart';
import 'package:rabbit_seat_app/utils/signal.dart';

import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import '../../utils/utils.dart';
import '../../widgets/buttom/ripple_button.dart';
import '../../widgets/common/appbar.dart';

/**
 * 设备修改名称
 */
class DeviceModifyNameView extends StatefulWidget {
  final Map<String, dynamic>? arguments;
  const DeviceModifyNameView({Key? key, this.arguments}) : super(key: key);

  @override
  State<DeviceModifyNameView> createState() => _DeviceModifyNameViewState();
}

class _DeviceModifyNameViewState extends State<DeviceModifyNameView> {
  // 输入控制器
  TextEditingController _controller = TextEditingController();
  // 设备名称
  String? _name;
  // 设备id
  // String? _id;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    // if (widget.arguments != null) {
    //   if (widget.arguments!.containsKey('name')) {
    //     _name = widget.arguments!["name"];
    //     _controller.text = _name??'';
    //   }
    //   if (widget.arguments!.containsKey('id')) {
    //     _id = widget.arguments!["id"];
    //   }
    // }
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: '修改设备名称',
      ),
      body: _bodyWidget(context),
    );
  }


  /// 主体
  Widget _bodyWidget(BuildContext context) {
    DeviceModel? _model = context.watch<DeviceProvider>().editorDevice;
    print(">>> dev -- name :${_model!.name}");
    if (_model == null) return Container(child: Center(child: Text("无可编辑对象！")));

    if (_name == null) {
      _name = _model.name;
      _controller.text = _model.name??'';
    }

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Util.removePrimaryFocus(context);
      },
      child: Container(
        padding: EdgeInsets.only(left: 12, top: 12, right: 12),
        child: Column(
          children: [
            Container(
              height: 50,
              padding: EdgeInsets.only(left: 15, right: 15),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x17828282),
                    offset: Offset(0, 0),
                    blurRadius: 10,
                    spreadRadius: 10,
                  )
                ],
              ),
              child: TextField(
                controller: _controller,
                onChanged: (value) {
                  _name = value;
                },
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF333333),
                ),
                maxLines: 1,
                autofocus: true,
                cursorColor: Business.mainColor,
                decoration: InputDecoration(
                  isDense: true,
                  hintText: "请输入设备名称",
                  hintStyle:
                  TextStyle(fontSize: 15, color: Color(0xFFA5A5A5)),
                  border: InputBorder.none,
                ),
              ),
            ),
            SizedBox(
              height: 15,
            ),
            RippleButton(
              onTap: () {
                Util.removePrimaryFocus(context);
                // _submitAddShareDatas();

                if (_name == null || _name!.isEmpty) {
                  EasyLoading.showToast('请输入设备名称!');
                  return ;
                }
                context.read<DeviceProvider>().modifyDeviceName(_name??'', completed: (value) {
                  if (value) Navigator.pop(context);
                });

              },
              radius: 25,
              color: Business.mainColor,
              child: Container(
                height: 50,
                alignment: Alignment.center,
                child: Text(
                  '保存',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }


// /// 提交名称修改
// void _submitAddShareDatas() async {
//   try {
//     if (_id == null && _id!.isEmpty) {
//       EasyLoading.showToast('设备ID不能为空！');
//       return;
//     }
//     if (_name == null || _name!.isEmpty) {
//       EasyLoading.showToast('请输入设备名称!');
//       return ;
//     }
//     EasyLoading.show();
//     Map<String, dynamic> params = {
//       "id": _id,
//       "name": _name,
//     };
//     final data = await Http.put('/api/device/config', data: params);
//     EasyLoading.dismiss();
//     final result = ResultModel.fromJson(data, (json) => json);
//     if (result.code == 200) {
//       EasyLoading.showToast("保存成功！");
//       KitSignal().send('modify-device-name', params);
//       Navigator.pop(context);
//     } else {
//       EasyLoading.showToast(result.message);
//     }
//   } catch (e) {
//     EasyLoading.dismiss();
//     EasyLoading.showToast(e.toString());
//   }
// }

}
