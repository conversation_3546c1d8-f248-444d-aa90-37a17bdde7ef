import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue/flutter_blue.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/models/tuya/tuyaHome.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/utils/rx.dart';
import 'package:rabbit_seat_app/widgets/animation/ripple.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';
import 'package:rabbit_seat_app/widgets/dialog/dialog.dart';
import 'package:tuya/tuya.dart';

import '../../models/home/<USER>';
import '../../models/tuya/tuyaDevice.dart';
import '../../models/tuya/tuyaScanDevice.dart';
import '../../utils/utils.dart';
import '../../widgets/buttom/ripple_button.dart';

/**
 * 添加设备
 */
class DeviceAddView extends StatefulWidget {
  const DeviceAddView({Key? key}) : super(key: key);

  @override
  State<DeviceAddView> createState() => _DeviceAddViewState();
}

class _DeviceAddViewState extends State<DeviceAddView>
    with SingleTickerProviderStateMixin {
  // 搜索设备
  bool isScanning = false;

  // 扫描失败！
  bool scanFail = false;

  // 秒
  var seconds = 0;

  // 计时器
  Timer? timer;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    disposeTimer();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: DefAppBar(context: context, title: "添加设备"),
      body: _buildBody(),
    );
  }

  bool scaning = false;
  bool hasDevice = false;
  String deviceName = "安全座椅";
  String uuid = "";
  num step = 1;
  bool getDivice = false;

  Widget _buildBody() {
    return Container(
      child: Stack(
        children: [
          Offstage(
            offstage: step != 1,
            child: step1(),
          ),
          Offstage(
            offstage: step != 2,
            child: step2(),
          )
        ],
      ),
    );
  }

  Widget step1() {
    return Padding(
        padding: EdgeInsets.all(24.w),
        child: Stack(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    "完成以下操作",
                    style: TextStyle(
                        color: const Color.fromARGB(255, 255, 119, 0),
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600),
                  ),
                ),
                Align(
                  alignment: Alignment.topLeft,
                  child: Container(
                      margin: EdgeInsets.only(top: 21.h),
                      child: Text(
                        "1.请用手压住安全座椅底部或将宝宝固定在安全座椅上",
                        style: TextStyle(
                          fontSize: 15.sp,
                        ),
                      )),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 21.h),
                  child: Image(
                      image: const AssetImage("assets/images/add2.png"),
                      width: 280.w),
                ),
                Align(
                  alignment: Alignment.topLeft,
                  child: Container(
                      margin: EdgeInsets.only(top: 30.h),
                      child: Text(
                        "2.请确保安全座椅连接充电器",
                        style: TextStyle(
                          fontSize: 15.sp,
                        ),
                      )),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 21.h),
                  child: Image(
                      image: const AssetImage("assets/images/add1.png"),
                      width: 280.w),
                ),
              ],
            ),
            Container(
              margin: EdgeInsets.only(bottom: 15.h),
              child: Align(
                  alignment: Alignment.bottomCenter,
                  child: InkWell(
                    onTap: () async {
                      // 蓝牙状态
                      final _blueIsOn = await FlutterBlue.instance.isOn;
                      if (_blueIsOn == false) {
                        DefDialog.showDialog2(
                            context: context,
                            title: '温馨提示',
                            message: '蓝牙功能未开启，请先开启蓝牙功能后使用！',
                            itemText: '知道了',
                            confirm: () {});
                        return;
                      }

                      // 定位状态
                      final _localIsOn = await Permission
                          .locationWhenInUse.serviceStatus.isEnabled;
                      if (_localIsOn == false) {
                        DefDialog.showDialog2(
                            context: context,
                            title: '温馨提示',
                            message: '定位功能未开启，请先开启定位功能后使用！',
                            itemText: '知道了',
                            confirm: () {});
                        return;
                      }

                      changeStep(2);
                    },
                    child: Container(
                      width: 315.w,
                      height: 38.h,
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(255, 255, 119, 0),
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Center(
                          child: Text(
                        "已经准备好了",
                        style: TextStyle(fontSize: 18.sp, color: Colors.white),
                      )),
                    ),
                  )),
            )
          ],
        ));
  }

  Widget step2() {
    return Padding(
      padding:
          EdgeInsets.only(top: 42.h, left: 24.w, right: 24.w, bottom: 24.h),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  scanFail ? '未发现新设备' : "正在搜索附近设备...",
                  style:
                      TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w600),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 12.h),
                  child: Text(
                    scanFail ? "请检查后再次尝试" : "请开启手机“蓝牙”功能",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Center(
            child: SizedBox(
              width: 270,
              height: 270,
              child: Stack(
                children: [
                  RippleWidget(
                    size: Size(270, 270),
                    tween: Tween(begin: 30, end: 270),
                    color: Color(0x4D262626),
                  ),
                  Center(
                    child: Image(
                        image: const AssetImage("assets/images/add3.png"),
                        width: 55.w),
                  ),
                ],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(bottom: 15.h),
            child: Align(
                alignment: Alignment.bottomCenter,
                child: InkWell(
                  onTap: () async {
                    // _showLinkDialog(context, '');
                    scanFail ? scanDevice() : stopScan();
                  },
                  child: Container(
                    width: 265.w,
                    height: 38.h,
                    decoration: BoxDecoration(
                      color: scanFail
                          ? Business.mainColor
                          : Color.fromARGB(255, 245, 245, 245),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Center(
                        child: Text(
                      scanFail ? "重新扫描" : "暂不添加",
                      style: TextStyle(
                          fontSize: 18.sp,
                          color: scanFail ? Colors.white : Color(0xFF666666),
                          fontWeight: FontWeight.w400),
                    )),
                  ),
                )),
          )
        ],
      ),
    );
  }

  changeStep(nextStep) {
    if (nextStep == 2) {
      scanDevice();
    }
    setState(() {
      step = nextStep;
    });
  }

  void stopScan() {
    scanFail = false;
    disposeTimer();
    Navigator.of(context).pop();
  }

  void doScan() async {
    var result = await Tuya.StartScanDevice;
    if (result != null) {
      var scanDevice = TuyaScanDevice.fromJson(json.decode(result));
      print('>>> scanDevice:$result');
      if (scanDevice.uuid != null &&
          scanDevice.productId == '4vyzrxvibcxaalgt') {
        uuid = scanDevice.uuid!;
        // var conform = await showFindDevice();
        if (scanFail == true) return;
        var conform = await _showLinkDialog(context, uuid, result);
        if (conform == true) {
          // 刷新数据
          Rx.homeRefreshSubject.add(1);
          Navigator.pushReplacementNamed(context, '/');
        }
      }
    }
  }

  void scanDevice() async {
    // 计时器
    createTimer();

    Util.checkLocationPermissionStatus(context, "查找设备", () {
      Util.checkBluetoothPermissionStatus(context, "查找设备", () {
        // 是否是 android 平台
        if (Platform.isAndroid) {
          // 蓝牙连接权限
          Util.checkBluetoothConnectPermissionStatus(context, "查找设备", () {
            Util.checkBluetoothScanPermissionStatus(context, "查找设备", () {
              doScan();
            });
          });

          return;
        }
        doScan();
      });
    });
  }

  // 显示连接提示框
  Future<bool?> _showLinkDialog(BuildContext context, String uuid, String findScanDeviceBean) {
    return showDialog(
      context: context,
      barrierColor: Colors.black26,
      builder: (context) {
        return LinkDialog(
            uuid: uuid,
            findScanDeviceBean: findScanDeviceBean);
      },
    );
  }

  /// 计时器
  void createTimer() {
    // 销毁计时器
    disposeTimer();
    // 计时器
    if (timer == null) {
      scanFail = false;
      seconds = 60;
      timer = Timer.periodic(Duration(seconds: 1), (timer) {
        print(">>> send code timer seconds:$seconds");
        setState(() {
          if (seconds > 0) {
            seconds--;
            print(">>> 倒计时：$seconds");
          } else {
            stopScanDevices();
            scanFail = true;
            // 销毁计时器
            disposeTimer();
          }
        });
      });
    }
  }

  /// 停止扫描设备
  void stopScanDevices() async {
    var result = await Tuya.stopScanDevice;
    if (result != null && result == true) {
      print(">>> 停止扫描设备");
    }
  }

  /// 销毁计时器
  void disposeTimer() {
    if (timer != null && timer!.isActive) {
      timer!.cancel();
      timer = null;
    }
  }
}

/// ===========================================================================

/**
 * 链接对话框
 */
class LinkDialog extends StatefulWidget {
  final String uuid;
  final String findScanDeviceBean;

  const LinkDialog(
      {Key? key,
      required this.uuid,
      required this.findScanDeviceBean})
      : super(key: key);

  @override
  State<LinkDialog> createState() => _LinkDialogState();
}

class _LinkDialogState extends State<LinkDialog> {
  // 0 默认，1正在链接，2链接失败
  int style = 0;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        alignment: Alignment.bottomCenter,
        padding: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 40.h),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.w),
          ),
          child: Stack(
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题
                  Container(
                    padding: EdgeInsets.only(top: 30),
                    child: Center(
                      child: Text(
                        style == 0
                            ? "求知2Pro"
                            : style == 1
                                ? '正在连接'
                                : '连接失败',
                        style: TextStyle(
                            fontSize: 24, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                  // 描述内容
                  Container(
                    margin: EdgeInsets.only(top: 5),
                    height: 18,
                    alignment: Alignment.center,
                    child: style == 1
                        ? Text(
                            '请确保手机靠近设备，且网络通畅',
                            style: TextStyle(
                              fontSize: 15,
                              color: Color(0xFF666666),
                            ),
                          )
                        : style == 2
                            ? Text(
                                "请检查后再次尝试",
                                style: TextStyle(
                                  fontSize: 15,
                                  color: Color(0xFF666666),
                                ),
                              )
                            : null,
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 14),
                    constraints: BoxConstraints(minHeight: 250),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Center(
                          child: style == 2
                              ? Image(
                                  image: const AssetImage(
                                      "assets/images/link_failure.png"),
                                  width: 90.h)
                              : Image(
                                  fit: BoxFit.fitHeight,
                                  image: const AssetImage(
                                      "assets/images/add4.png"),
                                  height: 400.h),
                        ),
                        // 加载动画
                        Align(
                          child: style == 1 ? Linking() : null,
                        ),
                      ],
                    ),
                  ),
                  // 按钮
                  Container(
                    padding: EdgeInsets.only(left: 20, right: 20, bottom: 20),
                    child: style == 0
                        ? Row(
                            children: [
                              Expanded(
                                child: RippleButton(
                                  color: Color(0xFFF5F5F5),
                                  radius: 50 / 2,
                                  child: Container(
                                    height: 50,
                                    alignment: Alignment.center,
                                    child: Text(
                                      '取消',
                                      style: TextStyle(
                                          fontSize: 18,
                                          color: Color(0xFF666666)),
                                    ),
                                  ),
                                  onTap: () {
                                    Navigator.of(context).pop();
                                  },
                                ),
                              ),
                              SizedBox(
                                width: 20,
                              ),
                              Expanded(
                                child: RippleButton(
                                  color: Business.mainColor,
                                  radius: 50 / 2,
                                  child: Container(
                                    height: 50,
                                    alignment: Alignment.center,
                                    child: Text(
                                      '连接',
                                      style: TextStyle(
                                          fontSize: 18, color: Colors.white),
                                    ),
                                  ),
                                  onTap: () async {
                                    setState(() {
                                      style = 1;
                                    });

                                    // 链接设备
                                    var res = await connectDevices(
                                        widget.uuid,
                                        widget.findScanDeviceBean);
                                    if (res == true) {
                                      EasyLoading.showToast('连接成功！');
                                      Navigator.pop(context, true);
                                    } else {
                                      setState(() {
                                        style = 2;
                                      });
                                    }

                                    // // 模拟测试
                                    // Future.delayed(Duration(milliseconds: 3000), () {
                                    //   print("______________");
                                    //   setState(() {
                                    //     style = 2;
                                    //   });
                                    // });
                                  },
                                ),
                              ),
                            ],
                          )
                        : style == 1
                            ? Container(
                                height: 50,
                              ) // RippleButton(
                            //     color: Color(0xFFF5F5F5),
                            //     radius: 50 / 2,
                            //     child: Container(
                            //       height: 50,
                            //       alignment: Alignment.center,
                            //       child: Text(
                            //         '取消1',
                            //         style: TextStyle(
                            //             fontSize: 18, color: Color(0xFF666666)),
                            //       ),
                            //     ),
                            //     onTap: () {
                            //       setState(() {
                            //         style = 0;
                            //       });
                            //     },
                            //   )
                            : RippleButton(
                                color: Business.mainColor,
                                radius: 50 / 2,
                                child: Container(
                                  height: 50,
                                  alignment: Alignment.center,
                                  child: Text(
                                    '重新连接',
                                    style: TextStyle(
                                        fontSize: 18, color: Colors.white),
                                  ),
                                ),
                                onTap: () async {
                                  // 设置样式
                                  setState(() {
                                    style = 1;
                                  });

                                  // 连接设备
                                  var res = await connectDevices(
                                      widget.uuid,
                                      widget.findScanDeviceBean);
                                  if (res == true) {
                                    EasyLoading.showToast('连接成功！');
                                    Navigator.pop(context, true);
                                  } else {
                                    setState(() {
                                      style = 2;
                                    });
                                  }

                                  // // 模拟测试
                                  // Future.delayed(Duration(milliseconds: 3000), () {
                                  //   print("+++++++++++");
                                  //   Navigator.pop(context, true);
                                  // });
                                },
                              ),
                  ),
                ],
              ),
              // 删除按钮
              Positioned(
                right: 0,
                child: style == 1
                    ? Container()
                    : IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: Icon(
                          Icons.clear,
                          color: Colors.grey,
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 创建home的数据
  String? _homeJson;

  /// 连接设备
  Future<bool> connectDevices(String uuid,
      String findScanDeviceBean) async {
    var home = await getHome();
    if (home == null) {
      EasyLoading.showToast("连接服务器失败！");
      return false;
    }
    var res = await Tuya.activeDevice(home.homeId!, uuid, findScanDeviceBean);
    // EasyLoading.dismiss();
    if (res != null && res != "" && res != "0") {
      try {
        var tuyaDevice = TuyaDevice.fromJson(json.decode(res));
        // EasyLoading.show();
        final data = await Http.get('/api/device/active/${tuyaDevice.devId}');
        // EasyLoading.dismiss();
        final result = ResultModel<HomeDeviceModel>.fromJson(data,
            (json) => HomeDeviceModel.fromJson(json as Map<String, dynamic>));
        if (result.code == 200) {
          return true;
        } else {
          EasyLoading.showToast(result.message);
        }
      } catch (e) {
        // EasyLoading.dismiss();
        // EasyLoading.showToast(e.toString());
        Util.showMessage(
            context, "激活错误", e.toString() + ",设备uid:" + uuid, () => {});
      }
    } else {
      Util.showMessage(context, "本地激活错误", "设备激活失败,设备uid:" + uuid, () => {});
    }
    return false;
  }

  Future<TuyaHome?> getHome() async {
    try {
      final data = await Http.get('/api/device/home');
      final result = ResultModel.fromJson(data, (json) {
        return TuyaHome.fromJson(json as Map<String, dynamic>);
      });
      if (result.code == 200) {
        return result.data;
      } else {
        EasyLoading.showToast(result.message);
      }
      return null;
    } catch (e) {
      print(">>> Error:$e");
      EasyLoading.showToast(e.toString());
      return null;
    }
  }

  /// 测试绑定
  Future<void> _bunding(String id) async {
    try {
      final data = await Http.post('/api/device/unbundlingTuya/$id');
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        return;
      } else {
        EasyLoading.showToast(result.message);
      }
      return;
    } catch (e) {
      print(">>> Error:$e");
      EasyLoading.showToast(e.toString());
      return;
    }
  }
}

/// ===========================================================================

/**
 * 旋转加载提示
 */
class Linking extends StatefulWidget {
  const Linking({Key? key}) : super(key: key);

  @override
  State<Linking> createState() => _LinkingState();
}

class _LinkingState extends State<Linking> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
        vsync: this, duration: Duration(milliseconds: 1000));
    // 监听
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // _controller.reset();
        // _controller.forward();
      }
    });
    // _controller.forward();
    // 循环播放
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.stop();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _controller,
      child: CircleAvatar(
        radius: 28,
        child: SizedBox(
          width: 56,
          height: 56,
          child: Image.asset('assets/images/linking.png'),
        ),
      ),
    );
  }
}
