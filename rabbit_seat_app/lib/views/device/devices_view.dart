import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/models/device/device_model.dart';
import 'package:rabbit_seat_app/provider/device_provider.dart';
import 'package:rabbit_seat_app/utils/signal.dart';
import 'package:rabbit_seat_app/views/device/device_manage_view.dart';

import '../../models/error.dart';
import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import '../../widgets/common/appbar.dart';
import '../../widgets/device/device_item.dart';
import '../../widgets/refresh/index.dart';
import '../../widgets/refresh/refresh_header.dart';

/**
 * 设备列表
 */
class DevicesView extends StatefulWidget {
  const DevicesView({Key? key}) : super(key: key);

  @override
  State<DevicesView> createState() => _DevicesViewState();
}

class _DevicesViewState extends State<DevicesView> {
  // 控制器
  /// 刷新控制
  EasyRefreshController _controller = EasyRefreshController();
  // List<DeviceModel> _items = [];

  // List<TuyaHome> tuyaHomes = [];
  // List<HomeDeviceModel> devices = [];
  // Map<String, TuyaHome> tagHomes = {};

  @override
  void initState() {
    super.initState();

    // var device = DeviceModel();
    // device.name = "宝宝座椅1";
    // items.add(device);

    // 加载数据
    // Future.delayed(Duration(milliseconds: 100), () {
    //   if (mounted) _loadDatas();
    // });

    // 加载数据
    // _loadDatas();

    // Future.delayed(Duration(milliseconds: 100), () {
    //   if (mounted) _controller.callRefresh();
    // });
    //
    // KitSignal().link('modify-device-name', 'device-list', (arg) {
    //   print(">>>>  KitSignal().link('modify-device-name', 'device-list'");
    //   _controller.callRefresh();
    // });
  }

  @override
  void dispose() {
    // KitSignal().off('modify-device-name', 'device-list');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    // List<HomeDeviceModel>? _list = context.watch<ProfileProvider>().devices;
    // if (_list != null) {
    //   devices = _list;
    // }

    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: "我的设备",
      ),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Container(
      color: Color.fromARGB(255, 249, 249, 249),
      child: EasyRefresh.custom(
        controller: _controller,
        header: CustomRefreshHeader(),
        slivers: [
          SliverList(
            delegate: SliverChildBuilderDelegate(
              _itemBuilder,
              childCount: context
                  .watch<DeviceProvider>()
                  .mdevices
                  .length, //_items.length,
            ),
          ),
        ],
        onRefresh: () async {
          print("refresh");
          // _loadDatas();
          context.read<DeviceProvider>().loadMyDevices(completed: () {
            _controller.finishRefresh();
          });
        },
      ),
    );

    /*
    return ListView.builder(
      itemCount: devices.length,
      itemBuilder: (context, index) {
        HomeDeviceModel model = devices[index];

        return DeviceItem(
            title: model.name!,
            top: 10.h,
            subTitle: "流量套餐: ${model.getExpTime} 到期",
            onTap: () {
              TuyaHome? _home = tagHomes[model.id!];
              if (_home != null) {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (ctx) => DeviceManageView(arguments: {
                              "id": model.id,
                              'homeId': _home.homeId
                            })));
              }
            });
      },
    );
    // return Center(
    //   child: Column(
    //     children: items.map((m) {
    //       return DeviceItem(
    //           title: m.name!,
    //           top: 10.h,
    //           subTitle: "流量套餐: 2022-10-23 到期",
    //           onTap: () {
    //             Navigator.push(
    //                 context,
    //                 MaterialPageRoute(
    //                     builder: (ctx) =>
    //                         DeviceManageView(arguments: {"id": m.id})));
    //           });
    //     }).toList(),
    //   ),
    // );
    */
  }

  /// 列表元素
  Widget _itemBuilder(_, index) {
    // DeviceModel model = _items[index];
    // DeviceModel model = Provider.of<DeviceProvider>(context).mdevices[index];
    DeviceModel model = context.watch<DeviceProvider>().mdevices[index];

    final _date = DateTime.fromMillisecondsSinceEpoch(model.activeTime.toInt());
    DateTime endDate = DateTime(_date.year + 2, _date.month, _date.day);
    return DeviceItem(
        icon: model.icon,
        title: model.name!,
        top: 10.h,
        subTitle: "流量套餐: ${formatDate(endDate, [yyyy, '-', mm, '-', dd])} 到期",
        onTap: () {
          // Provider.of<DeviceProvider>(context).setEditorDevice(model);
          context.read<DeviceProvider>().setEditorDevice(model);

          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (ctx) =>
                      DeviceManageView(arguments: {"id": model.id})));
        });
  }

  /// 加载我的设备列表数据
/*
  void _loadDatas() {
    try {
      String homeId = '56165667';

      EasyLoading.show();
      final data = Http.get('/api/deviceShare/list/' + homeId);
      EasyLoading.dismiss();
      final result = ResultModel<ListModel?>.fromJson(
          data as Map<String, dynamic>,
          (json) => ListModel<DeviceModel>.fromJson(
              json as Map<String, dynamic>,
              (content) =>
                  DeviceModel.fromJson(content as Map<String, dynamic>)));
      if (result.code == 200) {
        if (result.data != null) {
          // items = result.data!.content as List<DeviceModel>;
          if (mounted) setState(() {});
        }
      } else {
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.message ?? '网络请求失败！');
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
  */

  /// 加载数据
/*
  void _loadDatas() async {
    try {
      // 清空数据
      tuyaHomes = [];
      devices = [];
      tagHomes = {};

      EasyLoading.show();

      // 获取home列表
      final _homeDatas = await Tuya.getHomeList;
      if (_homeDatas != null) {
        // 反序列化 home 数据
        List? _homeJson = json.decode(_homeDatas);
        if (_homeJson != null && _homeJson.isNotEmpty) {
          // 循环解析数据
          for (var item in _homeJson) {
            // 解析home
            TuyaHome _home = TuyaHome.fromJson(item);
            // 获取详情
            final data = await Http.get('/api/device/homeDevice/${_home.homeId}');

            final result = ResultModel<HomeDeviceModel?>.fromJson(
              data,//_testJson(),
                  (json) {
                return (json != null
                    ? HomeDeviceModel.fromJson(json as Map<String, dynamic>)
                    : null);
              },
            );

            if (result.code == 200) {
              if (result.data != null) {
                // 刷新页面
                HomeDeviceModel _device = result.data as HomeDeviceModel;
                _home.devices.add(_device);
                devices.add(_device);
                tagHomes.addAll({_device.id! : _home});
              }
            }
            // 添加到列表
            tuyaHomes.add(_home);
            Global.profile.devices = devices;
            Global.saveProfile();
          }
        }
        else {
          EasyLoading.showToast('数据加载失败！');
        }
      }
      EasyLoading.dismiss();
      // 刷新页面
      if (mounted) setState(() {});
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
   */
//
// /// 加载数据
// void _loadDatas() async {
//   try {
//     final data = await Http.get("/api/device/appList");
//     final result = ResultModel<List<DeviceModel>>.fromJson(data, (json) {
//       if (json != null && (json is List<dynamic>)) {
//         return (json as List<dynamic>).map((e) => DeviceModel.fromJson(e as Map<String, dynamic>)).toList();
//       } else {
//         return [];
//       }
//     });
//     if (result.code == 200) {
//       if (result.data != null) {
//         _items = result.data;
//         // 刷新结束
//         _controller.finishRefresh();
//       }
//       if (mounted) setState(() {});
//     } else {
//       _controller.finishRefresh();
//       EasyLoading.showToast(result.message);
//     }
//   } on MyError catch (e) {
//     print('>>> Error:$e');
//     _controller.finishRefresh();
//     EasyLoading.showToast(e.message ?? "数据解析失败！");
//   } catch (e) {
//     print('>>> Error:$e');
//     _controller.finishRefresh();
//     EasyLoading.showToast(e.toString());
//   }
// }
}
