import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/utils/global.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/utils/signal.dart';
import 'package:rabbit_seat_app/utils/utils.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';

import '../../widgets/buttom/ripple_button.dart';

/**
 * 添加设备共享
 */
class ShareAddView extends StatefulWidget {
  final Map<String, dynamic>? arguments;
  const ShareAddView({Key? key, this.arguments}) : super(key: key);

  @override
  State<ShareAddView> createState() => _ShareAddViewState();
}

class _ShareAddViewState extends State<ShareAddView> {
  // home Id
  int _homeId = -1;
  // 设备 Id
  String? _deviceId;
  // 过期时间
  int _expiredTime = 0;
  // 共享手机号码
  String? _phone;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (widget.arguments != null &&
        widget.arguments!.containsKey("homeId") &&
        widget.arguments!.containsKey("deviceId") &&
        widget.arguments!.containsKey("expTime")) {
      _homeId = widget.arguments!['homeId'];
      _deviceId = widget.arguments!['deviceId'];
      _expiredTime = widget.arguments!["expTime"];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: '添加共享',
      ),
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          Util.removePrimaryFocus(context);
        },
        child: Container(
          padding: EdgeInsets.only(left: 12, top: 12, right: 12),
          child: Column(
            children: [
              Container(
                height: 50,
                padding: EdgeInsets.only(left: 15, right: 15),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x17828282),
                      offset: Offset(0, 0),
                      blurRadius: 10,
                      spreadRadius: 10,
                    )
                  ],
                ),
                child: TextField(
                  onChanged: (value) {
                    _phone = value;
                  },
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF333333),
                  ),
                  maxLines: 1,
                  autofocus: true,
                  cursorColor: Business.mainColor,
                  decoration: InputDecoration(
                    isDense: true,
                    hintText: "请输入手机号",
                    hintStyle:
                        TextStyle(fontSize: 15, color: Color(0xFFA5A5A5)),
                    border: InputBorder.none,
                  ),
                ),
              ),
              SizedBox(
                height: 15,
              ),
              RippleButton(
                onTap: () {
                  Util.removePrimaryFocus(context);
                  _submitAddShareDatas();
                },
                radius: 25,
                color: Business.mainColor,
                child: Container(
                  height: 50,
                  alignment: Alignment.center,
                  child: Text(
                    '确定',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 提交共享数据
  void _submitAddShareDatas() async {
    try {
      if (_phone == null || _phone!.isEmpty) {
        EasyLoading.showToast('请输入手机号!');
        return;
      }
      EasyLoading.show();
      Map<String, dynamic> params = {
        "expiredTime": _expiredTime,
        "homeId": _homeId,
        "shareStatus": "Init",
        "shareTelephone": _phone,
        "deviceId": _deviceId,
      };
      final data = await Http.post('/api/deviceShare', data: params);
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        EasyLoading.showToast("邀请成功！");
        Navigator.pop(context);
      } else {
        EasyLoading.showToast(result.message);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}
