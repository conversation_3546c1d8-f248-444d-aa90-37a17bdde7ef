import 'package:bloc/bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/utils/rx.dart';

import '../../models/device/device_model.dart';
import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import 'dev_list_state.dart';

class DevListCubit extends Cubit<DevListState> {
  DevListCubit() : super(DevListState().init()) {
    _init();
  }

  // 自定义初始化
  void _init() async {
    // 删除设备
    Rx.removeDeviceByIdSubject.listen((value) {
      Map<String, dynamic>? map = value as Map<String, dynamic>?;
      if (map != null && map.containsKey('id')) {
        state.datas?.remove(value!['id']);
        emit(state.clone());
      }
    });
    // 修改设备名称
    Rx.modifyDeviceNameByIdSubject.listen((event) {
      Map<String, dynamic>? datas = event as Map<String, dynamic>?;
      if (datas != null && datas.containsKey('id') && datas.containsKey('val')) {
        String id = datas['id'];
        String name = datas['val'];
        if (state.datas != null && state.datas!.containsKey(id)) {
          DeviceModel? model = state.datas![id];
          if (model != null) {
            model.name = name;
            state.datas![id] = model;
          }
        }
        emit(state.clone());
      }
    });
  }

  /// 加载我的设备列表
  Future<void> loadMyDevices() async {
    try {
      final data = await Http.get("/api/device/appList");
      final result = ResultModel<List<DeviceModel>?>.fromJson(data, (json) {
        if (json != null && (json is List<dynamic>)) {
          return (json)
              .map((e) => DeviceModel.fromJson(e as Map<String, dynamic>))
              .toList();
        } else {
          return [];
        }
      });
      if (result.code == 200) {
        List<DeviceModel>? _devs = result.data;
        if (_devs != null && _devs.isNotEmpty) {
          Map<String, DeviceModel> _map = {};
          _devs.forEach((element) {
            _map.addAll({element.id!: element});
          });
          emit(state.clone()..datas = _map);
        } else {
          emit(state.clone()..datas = null);
        }
      } else {
        emit(state.clone()..datas = null);
        EasyLoading.showToast(result.message);
      }
      return;
    } catch (e) {
      print('>>> Error:$e');
      emit(state.clone()..datas = null);
      EasyLoading.showToast(e.toString());
    }
  }
}
