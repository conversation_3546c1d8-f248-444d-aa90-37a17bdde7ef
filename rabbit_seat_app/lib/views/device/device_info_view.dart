import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/models/device/device_model.dart';
import 'package:rabbit_seat_app/provider/device_provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../widgets/buttom/ripple_button.dart';
import '../../../widgets/common/appbar.dart';

/**
 * 我的设置
 */
class DeviceInfoView extends StatefulWidget {
  const DeviceInfoView({Key? key}) : super(key: key);

  @override
  State<DeviceInfoView> createState() => _DeviceInfoViewState();
}

class _DeviceInfoViewState extends State<DeviceInfoView> {
  String _localVersion = '1.0';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    // 初始化版本号
    _init();
  }

  void _init() async {
    /// 加载本地版本信息
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    _localVersion = packageInfo.version;
    print("localVersion:$_localVersion");
    setState(() {});
  }

  DeviceModel? deviceInfo;

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于375dp * 812dp的屏幕
    

    // 加载宝贝信息
    deviceInfo = context.watch<DeviceProvider>().displayDevice;

    return Scaffold(
      appBar: DefAppBar(context: context, title: "设备信息"),
      // appBar: AppBar(title: Text("技术支持"),),
      body: Container(
        padding: EdgeInsets.only(left: 12.w, top: 12.w, right: 12.w),
        child: Column(
          children: [
            _itemBoxWidget(
              context: context,
              title: "设备ID",
              extra: Text(
                deviceInfo!.originalId!,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF666666),
                ),
              ),
              onTap: () {
                // TODO
              },
            ),
            _itemBoxWidget(
              context: context,
              title: "UUID",
              extra: Text(
                deviceInfo!.uuid!,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF666666),
                ),
              ),
              onTap: () {
                // TODO
              },
            ),
            _itemBoxWidget(
              context: context,
              title: "产品ID",
              extra: Text(
                deviceInfo!.productId!,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF666666),
                ),
              ),
              onTap: () {
                // TODO
              },
            ),
            _itemBoxWidget(
              context: context,
              title: "产品名称",
              extra: Text(
                '两只兔子求知2Pro儿童安全座椅', // deviceInfo!.productName!, // 需要（iot平台修改，代码不做修改）暂时先写死
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF666666),
                ),
              ),
              onTap: () {
                // TODO
              },
            ),
            _itemBoxWidget(
              context: context,
              title: "型号",
              extra: Text(
                'B107', // deviceInfo!.model!, // 需要（iot平台修改，代码不做修改）暂时先写死
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF666666),
                ),
              ),
              onTap: () {
                // TODO
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 圆角容器
  Widget _itemBoxWidget(
      {required BuildContext context,
      required String title,
      required Widget extra,
      required Function() onTap}) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12.w)),
        boxShadow: [
          BoxShadow(
            color: Color(0x17828282),
            offset: Offset(0, 0),
            blurRadius: 10.5,
            spreadRadius: 5,
          )
        ],
      ),
      child: RippleButton(
        child: Container(
          height: 55.h,
          padding: EdgeInsets.only(left: 18.w, right: 18.w),
          child: Row(
            children: [
              Expanded(
                  child: Text(
                title,
                style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF000000),
                ),
              )),
              SizedBox(
                width: 10.w,
              ),
              extra,
            ],
          ),
        ),
        radius: 12.w,
        color: Colors.white,
        onTap: onTap,
      ),
    );
  }

  /// 拨号事件
  void _makePhoneCall() async {
    final String phoneNumber = '';
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    // 拨号
    await launchUrl(launchUri);
  }
}
