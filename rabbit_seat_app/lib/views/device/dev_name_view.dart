import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';

import '../../utils/utils.dart';
import '../../widgets/buttom/ripple_button.dart';
import '../../widgets/index.dart';
import 'dev_name_cubit.dart';
import 'dev_name_state.dart';

/**
 * 设备名称修改
 */
class DevNamePage extends StatelessWidget {
  final Map<String, dynamic>? arguments;
  DevNamePage({Key? key, this.arguments}) : super(key: key);

  // 输入控制器
  TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {

    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    return BlocProvider(
      create: (BuildContext context) => DevNameCubit(arguments: arguments),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<DevNameCubit>(context);

    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: '修改设备名称',
      ),
      body: BlocBuilder<DevNameCubit, DevNameState>(builder: (context, state) {
        if (cubit.state.id != null) {
          _controller.text = cubit.state.name??'';
          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              Util.removePrimaryFocus(context);
            },
            child: Container(
              padding: EdgeInsets.only(left: 12, top: 12, right: 12),
              child: Column(
                children: [
                  Container(
                    height: 50,
                    padding: EdgeInsets.only(left: 15, right: 15),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: const [
                        BoxShadow(
                          color: Color(0x17828282),
                          offset: Offset(0, 0),
                          blurRadius: 10,
                          spreadRadius: 10,
                        )
                      ],
                    ),
                    child: TextField(
                      controller: _controller,
                      onChanged: (value) {
                        cubit.state.name = value;
                      },
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF333333),
                      ),
                      maxLines: 1,
                      autofocus: true,
                      cursorColor: Business.mainColor,
                      decoration: const InputDecoration(
                        isDense: true,
                        hintText: "请输入设备名称",
                        hintStyle:
                        TextStyle(fontSize: 15, color: Color(0xFFA5A5A5)),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  RippleButton(
                    onTap: () async {
                      Util.removePrimaryFocus(context);
                      bool res = await cubit.modifyDeviceName();
                      if (res) {
                        Navigator.pop(context);
                      }
                    },
                    radius: 25,
                    color: Business.mainColor,
                    child: Container(
                      height: 50,
                      alignment: Alignment.center,
                      child: const Text(
                        '保存',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        } else {
          return Container(child: Center(child: Text("无可编辑对象！")));
        }
      }),
    );
  }
}


