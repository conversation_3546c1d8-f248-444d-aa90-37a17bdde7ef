import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../widgets/buttom/ripple_button.dart';
import '../../widgets/common/appbar.dart';
import 'dev_info_cubit.dart';
import 'dev_info_state.dart';

/**
 * 设备信息
 */
class DevInfoPage extends StatelessWidget {
  final Map<String, dynamic>? arguments;
  const DevInfoPage({Key? key, this.arguments}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于375dp * 812dp的屏幕
    

    return BlocProvider(
      create: (BuildContext context) => DevInfoCubit(arguments: arguments),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<DevInfoCubit>(context);

    return Scaffold(
      appBar: DefAppBar(context: context, title: "设备信息"),
      body: BlocBuilder<DevInfoCubit, DevInfoState>(builder: (context, state) {
        if (cubit.state.device != null) {
          return Container(
            padding: EdgeInsets.only(left: 12.w, top: 12.w, right: 12.w),
            child: Column(
              children: [
                _itemBoxWidget(
                  context: context,
                  title: "设备ID",
                  extra: Text(
                    cubit.state.device!.originalId!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  onTap: () {
                    // TODO
                  },
                ),
                _itemBoxWidget(
                  context: context,
                  title: "UUID",
                  extra: Text(
                    cubit.state.device!.uuid!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  onTap: () {
                    // TODO
                  },
                ),
                _itemBoxWidget(
                  context: context,
                  title: "产品ID",
                  extra: Text(
                    cubit.state.device!.productId!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  onTap: () {
                    // TODO
                  },
                ),
                _itemBoxWidget(
                  context: context,
                  title: "产品名称",
                  extra: Text(
                    '两只兔子求知2Pro儿童安全座椅', // deviceInfo!.productName!, // 需要（iot平台修改，代码不做修改）暂时先写死
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  onTap: () {
                    // TODO
                  },
                ),
                _itemBoxWidget(
                  context: context,
                  title: "型号",
                  extra: Text(
                    'B107', // deviceInfo!.model!, // 需要（iot平台修改，代码不做修改）暂时先写死
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  onTap: () {
                    // TODO
                  },
                ),
                _itemBoxWidget(
                  context: context,
                  title: "联网模组版本",
                  extra: Text(
                    cubit.state.device!.networkVersion ??
                        "",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  onTap: () {
                    // TODO
                  },
                ),
                _itemBoxWidget(
                  context: context,
                  title: "MCU版本",
                  extra: Text(
                    cubit.state.device!.mcuVersion ??
                        "",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  onTap: () {
                    // TODO
                  },
                ),
              ],
            ),
          );
        } else if (cubit.state.loadError != null) {
          return Container(
            alignment: Alignment.center,
            child: Text(cubit.state.loadError ?? '加载失败！'),
          );
        } else {
          return Container();
        }
      }),
    );
  }

  /// 圆角容器
  Widget _itemBoxWidget(
      {required BuildContext context,
      required String title,
      required Widget extra,
      required Function() onTap}) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12.w)),
        boxShadow: [
          BoxShadow(
            color: Color(0x17828282),
            offset: Offset(0, 0),
            blurRadius: 10.5,
            spreadRadius: 5,
          )
        ],
      ),
      child: RippleButton(
        child: Container(
          height: 55.h,
          padding: EdgeInsets.only(left: 18.w, right: 18.w),
          child: Row(
            children: [
              Expanded(
                  child: Text(
                title,
                style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF000000),
                ),
              )),
              SizedBox(
                width: 10.w,
              ),
              extra,
            ],
          ),
        ),
        radius: 12.w,
        color: Colors.white,
        onTap: onTap,
      ),
    );
  }
}
