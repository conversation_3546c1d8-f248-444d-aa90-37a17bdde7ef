import 'package:bloc/bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:rabbit_seat_app/utils/rx.dart';

import '../../models/result/result_model.dart';
import '../../utils/http.dart';
import 'dev_name_state.dart';

class DevNameCubit extends Cubit<DevNameState> {
  DevNameCubit({Map<String, dynamic>? arguments}) : super(DevNameState().init()) {
    if (arguments != null) {
      if (arguments.containsKey('id')) {
        state.id = arguments['id'];
      }
      if (arguments.containsKey('name')) {
        state.name = arguments['name'];
      }
    }
  }

  /// 修改名称
  Future<bool> modifyDeviceName() async {
    try {
      if (state.name == null || state.name!.isEmpty) {
        EasyLoading.showToast('请输入设备名称!');
        return false;
      }
      EasyLoading.show();
      Map<String, dynamic> params = {
        "id": state.id,
        "name": state.name,
      };
      final data = await Http.put('/api/device/config', data: params);
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        EasyLoading.showToast("保存成功！");
        Rx.modifyDeviceNameByIdSubject.add({'id' : state.id, 'val' : state.name});
        return true;
      } else {
        EasyLoading.showToast(result.message);
      }
      return false;
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
      return false;
    }
  }
}
