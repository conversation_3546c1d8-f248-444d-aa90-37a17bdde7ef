import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/user/contacts_model.dart';
import 'package:rabbit_seat_app/models/user/index.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';

import '../../services/user_service.dart';
import '../../utils/global.dart';
import '../../utils/utils.dart';
import '../../widgets/contacts/my_button.dart';

/**
 * 编辑紧急联系人
 */
class ContactsModifyView extends StatefulWidget {
  final Map<String, dynamic>? arguments;

  const ContactsModifyView({Key? key, this.arguments}) : super(key: key);

  @override
  State<ContactsModifyView> createState() => _ContactsModifyViewState();
}

class _ContactsModifyViewState extends State<ContactsModifyView> {
  bool isInit = false;

  ContactsModel? info;

  String name = "";
  String telephone = "";
  TextEditingController? nameText;
  TextEditingController? telephoneText;

  @override
  void initState() {
    super.initState();
    nameText = TextEditingController();
    telephoneText = TextEditingController();
  }

  void initData(ContactsModel _info) {
    telephone = _info.telephone!;
    name = _info.name!;
    telephoneText?.text = telephone;
    nameText?.text = name;
  }

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    if (!isInit) {
      dynamic passValue = ModalRoute.of(context)?.settings.arguments;
      ContactsModel _info = passValue["info"];
      info = _info;
      initData(_info);
      isInit = true;
    }
    return Scaffold(
      appBar: DefAppBar(context: context, title: "修改紧急联系人"),
      body: _buildBody(context),
      resizeToAvoidBottomInset: false,
    );
  }

  Widget _buildBody(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Util.removePrimaryFocus(context);
      },
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(left: 12.sp, top: 12.sp, right: 12.sp),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: const [
                BoxShadow(
                  color: Color(0x17828282), // 阴影的颜色
                  offset: Offset(0, 0), // 阴影与容器的距离
                  blurRadius: 10.0, // 高斯的标准偏差与盒子的形状卷积。
                  spreadRadius: 5.0, // 在应用模糊之前，框应该膨胀的量。
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _inputWidgt(
                    context: context,
                    title: "姓名",
                    controller: nameText,
                    hintText: "请输入紧急联系人姓名",
                    onChanged: (value) {
                      name = value;
                    }),
                const Divider(
                  indent: 18,
                  endIndent: 18,
                  height: 1,
                  color: Color(0xFFEAEAEA),
                ),
                _inputWidgt(
                    context: context,
                    title: "电话",
                    controller: telephoneText,
                    hintText: "请输入电话",
                    onChanged: (value) {
                      telephone = value;
                    },
                    keyboardType: TextInputType.phone),
              ],
            ),
          ),
          Expanded(child: Container()),
          Container(
            padding: EdgeInsets.only(
                left: 12.w,
                right: 12.w,
                bottom: MediaQuery.of(context).padding.bottom),
            alignment: Alignment.bottomCenter,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: EdgeInsets.only(bottom: 13),
                  child: Ink(
                    decoration: BoxDecoration(
                      color: Business.mainColor,
                      borderRadius: BorderRadius.circular(50 / 2),
                    ),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(50 / 2),
                      child: Container(
                        height: 50,
                        alignment: Alignment.center,
                        child: const Text(
                          "保存",
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      onTap: () async {
                        info?.name = name;
                        info?.telephone = telephone;
                        var res = await UserService.modifyEmergencyContact(
                            Global.profile.user!.id!, info!);
                        if (res) {
                          Navigator.of(context).pop(true);
                        }
                      },
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(bottom: 17),
                  child: Ink(
                    decoration: BoxDecoration(
                      color: Color(0xFFFFFFFF),
                      borderRadius: BorderRadius.circular(50 / 2),
                      border: Border.all(
                        color: Business.mainColor,
                        width: 1,
                      ),
                    ),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(50 / 2),
                      child: Container(
                        height: 50,
                        alignment: Alignment.center,
                        child: Text(
                          "删除",
                          style: TextStyle(
                            fontSize: 18,
                            color: Business.mainColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      onTap: () async {
                        var res = await UserService.deleteEmergencyContact(
                            Global.profile.user!.id!, info!);
                        if (res) {
                          Navigator.of(context).pop(true);
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 输入部件
  Widget _inputWidgt({
    required BuildContext context,
    required String title,
    TextEditingController? controller,
    String? hintText,
    Function(String)? onChanged,
    TextInputType? keyboardType,
  }) {
    return Container(
      padding: EdgeInsets.only(left: 24.w, right: 30.w),
      alignment: Alignment.centerLeft,
      height: 55,
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 15.sp, color: const Color(0xFF2A2A2A)),
          ),
          SizedBox(
            width: 15.w,
          ),
          Expanded(
            child: TextField(
              keyboardType: keyboardType ?? TextInputType.text,
              controller: controller,
              textAlign: TextAlign.end,
              style: TextStyle(fontSize: 14.sp, color: const Color(0xFF666666)),
              onChanged: (value) {
                if (onChanged != null) onChanged(value);
              },
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: hintText,
                hintStyle: const TextStyle(color: Color(0xFFAFAFAF)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
