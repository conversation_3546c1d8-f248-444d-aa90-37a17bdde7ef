import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/models/error.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/models/user/contacts_model.dart';
import 'package:rabbit_seat_app/provider/user_provider.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';

import '../../models/user/user_model.dart';
import '../../utils/global.dart';
import '../../utils/utils.dart';
import '../../widgets/contacts/my_box_edit.dart';
import '../../widgets/contacts/my_button.dart';

/**
 * 紧急联系人列表
 */
class ContactsView extends StatefulWidget {
  const ContactsView({Key? key}) : super(key: key);

  @override
  State<ContactsView> createState() => _ContactsViewState();
}

class _ContactsViewState extends State<ContactsView> {
  List<ContactsModel> list = [];

  // StreamSubscription? subEvents;
  @override
  void dispose() {
    super.dispose();
    // subEvents?.cancel();
  }

  @override
  void initState() {
    super.initState();
    init();
  }

  init() {
    setState(() {
      list = Global.profile.user?.emergencyContacts as List<ContactsModel>;
    });
  }

  bool isInit = false;

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: "紧急联系人",
      ),
      // body: _buildBody(),
      body: _bodyWidget(context),
    );
  }

  Widget _buildBody() {
    return Stack(
      children: [
        Center(
          child: Column(
            children: list
                .map((m) => MyBoxEdit(
                      title: m.name!,
                      subTitle: m.telephone,
                      top: 10.h,
                      onTap: () {
                        Navigator.pushNamed(context, '/contacts-modify',
                            arguments: {"info": m}).then((value) {
                          if (value is bool && value == true) {
                            _loadDatas();
                          }
                        });
                      },
                    ))
                .toList(),
          ),
        ),
        Container(
          padding: EdgeInsets.only(bottom: 35.h),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  width: 280.w,
                  margin: EdgeInsets.only(bottom: 20.h),
                  child: Util.getText(
                    "设置紧急联系人可以提升宝宝安全，当宝宝独处在车内平台工作人员将以短信或电话方式通知您！（最多设置两个）",
                    color: Color(0xFF666666),
                  ),
                ),
                MyButton(
                    text: "添加",
                    width: 295.w,
                    height: 42.h,
                    icon: const Icon(
                      Icons.add,
                      color: Colors.white,
                    ),
                    onTap: () {
                      Navigator.pushNamed(context, '/contacts-add')
                          .then((value) {
                        if (value is bool && value == true) {
                          _loadDatas();
                        }
                      });
                    })
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget _bodyWidget(BuildContext context) {
    return Column(
      children: [
        Expanded(
            child: ListView.builder(
                itemCount: list.length, itemBuilder: _itemBuilder)),
        Container(
          padding: EdgeInsets.only(bottom: 35.h),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  width: 280.w,
                  margin: EdgeInsets.only(bottom: 20.h),
                  child: Util.getText(
                    "设置紧急联系人可以提升宝宝安全，当宝宝独处在车内平台工作人员将以短信或电话方式通知您！（最多设置两个）",
                    color: Color(0xFF666666),
                  ),
                ),
                Visibility(
                  child: MyButton(
                      text: "添加",
                      width: 295.w,
                      height: 42.h,
                      icon: const Icon(
                        Icons.add,
                        color: Colors.white,
                      ),
                      onTap: () {
                        Navigator.pushNamed(context, '/contacts-add')
                            .then((value) {
                          if (value is bool && value == true) {
                            _loadDatas();
                          }
                        });
                      }),
                  visible: list.length < 2,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _itemBuilder(context, index) {
    ContactsModel model = list[index];
    return Padding(
      padding: EdgeInsets.only(left: 12, right: 12),
      child: MyBoxEdit(
        title: model.name!,
        subTitle: model.telephone,
        top: 10.h,
        onTap: () {
          Navigator.pushNamed(context, '/contacts-modify',
              arguments: {"info": model}).then((value) {
            if (value is bool && value == true) {
              _loadDatas();
            }
          });
        },
      ),
    );
  }

  /// 加载联系人
  /// 获取用户信息
  void _loadDatas() async {
    print("加载数据");
    try {
      EasyLoading.show();
      String id = Global.profile.user!.id!;
      final data = await Http.get('/api/appuser/user/$id');
      EasyLoading.dismiss();
      final result = ResultModel<UserModel>.fromJson(
          data, (json) => UserModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        Global.profile.user = result.data;
        Global.saveProfile();
        UserProvider userProvider = context.read<UserProvider>();
        if (result.data != null && result.data!.emergencyContacts != null) {
          userProvider.setContacts(result.data!.emergencyContacts!);
        }
        // 刷新页面
        if (mounted) setState(() {});
        init();
      } else {
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.message ?? '网络请求失败！');
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}
