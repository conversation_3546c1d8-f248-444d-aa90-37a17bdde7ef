// import 'package:flutter/material.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:flutter_easyrefresh/easy_refresh.dart';
// import 'package:rabbit_seat_app/widgets/common/appbar.dart';
//
// import '../../models/circle/circleModel.dart';
// import '../../models/error.dart';
// import '../../models/list/list_model.dart';
// import '../../models/result/result_model.dart';
// import '../../utils/http.dart';
// import '../../utils/signal.dart';
// import '../../widgets/intercat/issue_cell.dart';
// import '../../widgets/refresh/index.dart';
// import '../circle/circle_details_view.dart';
//
// /**
//  * 用户文章主页
//  */
// class UserArticlesView extends StatefulWidget {
//   final String userId;
//   const UserArticlesView({Key? key, required this.userId}) : super(key: key);
//
//   @override
//   State<UserArticlesView> createState() => _UserArticlesViewState();
// }
//
// class _UserArticlesViewState extends State<UserArticlesView> {
//   /// 数据
//   List<CircleModel> _items = [];
//
//   /// 页面
//   int _pageIndex = 0;
//
//   /// 控制器
//   EasyRefreshController _controller = EasyRefreshController();
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//
//     // 加载数据
//     Future.delayed(Duration(milliseconds: 100), () {
//       if (mounted) _controller.callRefresh();
//     });
//
//     KitSignal().link("ThumbUp", 'interact', (arg) {
//       print(">>> 点赞信号处理");
//       // 刷新数据
//       if (arg is Map && arg.containsKey('id') && arg.containsKey('state')) {
//         String id = arg["id"] as String;
//         bool state = arg['state'] as bool;
//         for (CircleModel model in _items) {
//           if (model.id == id) {
//             model.isUserLike = state;
//             if (model.isUserLike == true) {
//               model.likeCount += 1;
//             } else {
//               model.likeCount -= 1;
//             }
//             break;
//           }
//         }
//         if (mounted) setState(() {});
//       }
//     });
//
//     KitSignal().link("Comments", 'interact', (arg) {
//       // 刷新数据
//       if (arg is Map && arg.containsKey('id') && arg.containsKey('state')) {
//         String id = arg["id"] as String;
//         bool state = arg['state'] as bool;
//         for (CircleModel model in _items) {
//           if (model.id == id) {
//             model.canComment = state;
//             break;
//           }
//         }
//         if (mounted) setState(() {});
//       }
//     });
//   }
//
//   @override
//   void dispose() {
//     KitSignal().off("ThumbUp", 'interact');
//     KitSignal().off("Comments", 'interact');
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: DefAppBar(context: context, title: '用户主页'),
//       body: Container(
//         child: EasyRefresh.custom(
//           // 没有数据组件
//           emptyWidget: _items.length == 0 ? EmptyWidget() : null,
//           controller: _controller,
//           header: CustomRefreshHeader(),
//           footer: CustomRefreshFooter(),
//           slivers: [
//             /// 列表数据
//             SliverList(
//               delegate: SliverChildBuilderDelegate(
//                 _itemBuilder,
//                 childCount: _items.length,
//               ),
//             ),
//           ],
//           shrinkWrap: true,
//           onRefresh: () async {
//             // 刷新
//             _loadDatas(0);
//           },
//           onLoad: () async {
//             // 加载更多
//             _loadDatas(_pageIndex + 1);
//           },
//         ),
//       ),
//     );
//   }
//
//   /// 列表元素项
//   Widget _itemBuilder(context, index) {
//     CircleModel model = _items[index];
//     return GestureDetector(
//       onTap: () {
//         // Navigator.pushNamed(context, '/circle-info',
//         //     arguments: {"id": model.id});
//         Navigator.push(context, MaterialPageRoute(builder: (ctx) => CircleDetailsView(arguments: {"id" : model.id},)));
//       },
//       child: IssueCell(
//         data: model,
//         onDeleteArticles: () {
//           _deleteArticlesOrComments(model);
//         },
//         onOnOrOffComments: () {
//           _openOrCloseComments(model);
//         },
//         onThumbUp: () {
//           if (model.isUserLike == true) {
//             _cancelThumbUp(model, context);
//           } else {
//             _submitThumbsUp(model, context);
//           }
//         },
//       ),
//     );
//   }
//
//   /// 加载数据
//   void _loadDatas(int index) async {
//     try {
//       Map<String, dynamic> params = {
//         "pageNo": index,
//         "pageSize": 10,
//         "userId": widget.userId,
//       };
//       final data = await Http.get('/api/apparticle/app/list', data: params);
//       final result = ResultModel<ListModel?>.fromJson(data, (json) {
//         return ListModel<CircleModel>.fromJson(json as Map<String, dynamic>,
//                 (jsonT) {
//               return CircleModel.fromJson(jsonT as Map<String, dynamic>);
//             });
//       });
//       if (result.code == 200) {
//         if (result.data != null) {
//           ListModel<CircleModel> listModel =
//           result.data as ListModel<CircleModel>;
//           if (index == 0) {
//             _items = listModel.content ?? [];
//             // 重置加载没有更多数据
//             _controller.resetLoadState();
//             // 刷新结束
//             _controller.finishRefresh();
//           } else {
//             _items.addAll(listModel.content ?? []);
//             // 刷新结束， 控制加载，如果当前的加载条目，大于总条目，显示没有更多数据
//             _controller.finishLoad(
//                 noMore: _items.length >= result.data!.totalElements);
//           }
//         }
//         _pageIndex = index;
//         if (mounted) setState(() {});
//       } else {
//         index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
//         EasyLoading.showToast(result.message);
//       }
//     } on MyError catch (e) {
//       index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
//       EasyLoading.showToast(e.message ?? '网络请求失败！');
//     } catch (e) {
//       index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
//       EasyLoading.showToast(e.toString());
//     }
//   }
//
//   /// 点赞
//   void _submitThumbsUp(CircleModel model, BuildContext context) async {
//     try {
//       EasyLoading.show();
//       final data = await Http.post('/api/apparticle/like/${model.id}');
//       EasyLoading.dismiss();
//       final res = ResultModel.fromJson(data, (json) => json);
//       if (res.code == 200) {
//         model.likeCount += 1;
//         model.isUserLike = true;
//         if (mounted) setState(() {});
//       } else {
//         print(res.message);
//         EasyLoading.showToast(res.message);
//       }
//     } catch (e) {
//       print(">>> error:$e");
//       EasyLoading.dismiss();
//       EasyLoading.showToast("网络加载失败！");
//     }
//   }
//
//   /// 取消点赞
//   void _cancelThumbUp(CircleModel model, BuildContext context) async {
//     try {
//       EasyLoading.show();
//       final data = await Http.post('/api/apparticle/unlike/${model.id}');
//       EasyLoading.dismiss();
//       final res = ResultModel.fromJson(data, (json) => json);
//       if (res.code == 200) {
//         model.likeCount -= 1;
//         model.isUserLike = false;
//         if (mounted) setState(() {});
//       } else {
//         print(res.message);
//         EasyLoading.showToast(res.message);
//       }
//     } catch (e) {
//       print(">>> error:$e");
//       EasyLoading.dismiss();
//       EasyLoading.showToast("网络加载失败！");
//     }
//   }
//
//   /// 删除文章/评论
//   void _deleteArticlesOrComments(CircleModel model) async {
//     try {
//       EasyLoading.show();
//       final data = await Http.del('/api/apparticle/${model.id}');
//       EasyLoading.dismiss();
//       final res = ResultModel.fromJson(data, (json) => json);
//       if (res.code == 200) {
//         EasyLoading.showToast("文章删除成功!");
//         _items.remove(model);
//         if (mounted) setState(() {});
//         // 刷新或从新加载
//         // _loadDatas();
//       } else {
//         print(res.message);
//         EasyLoading.showToast(res.message);
//       }
//     } catch (e) {
//       print(">>> error:$e");
//       EasyLoading.dismiss();
//       EasyLoading.showToast("网络加载失败！");
//     }
//   }
//
//   /// 开启或关闭评论
//   void _openOrCloseComments(CircleModel model) async {
//     try {
//       bool _status = !model.canComment;
//       Map<String, dynamic> params = {
//         "id": model.id,
//         "canComment": _status,
//       };
//       EasyLoading.show();
//       final data = await Http.post('/api/apparticle/canComment', data: params);
//       EasyLoading.dismiss();
//       final res = ResultModel.fromJson(data, (json) => json);
//       if (res.code == 200) {
//         if (_status == true) {
//           EasyLoading.showToast("已开启评论！");
//         } else {
//           EasyLoading.showToast("已关闭评论！");
//         }
//         model.canComment = _status;
//         if (mounted) setState(() {});
//       } else {
//         print(res.message);
//         EasyLoading.showToast(res.message);
//       }
//     } catch (e) {
//       print(">>> error:$e");
//       EasyLoading.dismiss();
//       EasyLoading.showToast("网络加载失败！");
//     }
//   }
// }
