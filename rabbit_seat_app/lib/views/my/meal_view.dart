import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/provider/device_provider.dart';
import 'package:rabbit_seat_app/views/device/sim_recharge_policy_view.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';

import '../../models/device/device_model.dart';
import '../../models/error.dart';
import '../../models/result/result_model.dart';
import '../../utils/http.dart';

/**
 * 套餐
 */
class MealView extends StatefulWidget {
  final Map<String, dynamic>? arguments;

  const MealView({Key? key, this.arguments}) : super(key: key);

  @override
  State<MealView> createState() => _MealViewState();
}

class _MealViewState extends State<MealView> {
  List<Map<String, dynamic>> _items = [
    {'text': '一年流量套餐', 'num': 20},
    {'text': '两年流量套餐', 'num': 40},
  ];

  String? id;

  DeviceModel? device;

  // 选中项
  int _index = -1;
  Map<String, dynamic>? _kit;

  @override
  void initState() {
    super.initState();

    if (widget.arguments != null) {
      if (widget.arguments!.containsKey('id')) {
        id = widget.arguments!["id"];
        _loadDatas();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefAppBar(context: context, title: "流量套餐"),
      body: _bodyWidget(context),
    );
  }

  /// 构建主体
  Widget _bodyWidget(BuildContext context) {
    // DeviceModel? _model = context.watch<DeviceProvider>().editorDevice;
    if (id == null || device == null)
      return Container(child: Center(child: Text("未选中设备！")));

    // Widget _iconWidget = Container();
    // if (_model.icon != null) {
    //   _iconWidget = Image.network(
    //     _model.icon!,
    //     fit: BoxFit.cover,
    //   );
    // }
    Widget _iconWidget = Image.asset('assets/images/meal/4.png');

    String _time = '';
    final _date =
        DateTime.fromMillisecondsSinceEpoch(device!.activeTime.toInt());
    DateTime endDate = DateTime(_date.year + 2, _date.month, _date.day);
    _time = formatDate(endDate, [yyyy, '-', mm, '-', dd]);

    return Stack(
      children: [
        Container(
          child: Image.asset('assets/images/meal/3.png'),
        ),
        Container(
          padding: EdgeInsets.only(left: 12, right: 12),
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.only(top: 32),
                padding:
                    EdgeInsets.only(left: 18, right: 15, bottom: 12, top: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    ClipOval(
                      child: SizedBox(
                        width: 40,
                        height: 40,
                        child: Container(
                          color: Color(0xFFE9E9E9),
                          child: _iconWidget,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          device!.name ?? '',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        Text(
                          '流量套餐:$_time 到期',
                          style: TextStyle(
                            fontSize: 11,
                            color: Color(0xFF666666),
                          ),
                        ),
                      ],
                    )),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 25),
                padding:
                    EdgeInsets.only(left: 15, right: 15, bottom: 85, top: 20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 10),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              '流量套餐',
                              style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              print("流量套餐协议");
                            },
                            child: Container(
                              child: Row(
                                children: [
                                  InkWell(
                                    child: Text(
                                      '查看流量套餐充值协议',
                                      style: TextStyle(
                                        fontSize: 11.sp,
                                        color: const Color(0xFF666666),
                                      ),
                                    ),
                                    onTap: () {
                                      Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  const SimRechargePolicyView()));
                                    },
                                  ),
                                  SizedBox(
                                    width: 6.w,
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios_outlined,
                                    color: const Color(0xFF666666),
                                    size: 11.sp,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 30),
                      child: GridView.builder(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        scrollDirection: Axis.vertical,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 10,
                          mainAxisSpacing: 10,
                        ),
                        itemCount: _items.length,
                        itemBuilder: _itemBuilder,
                      ),
                    ),
                    // 按钮
                    Padding(
                      padding: EdgeInsets.only(top: 35),
                      child: GestureDetector(
                        onTap: () {
                          print("确认支付");
                        },
                        child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            color: Business.mainColor,
                            borderRadius: BorderRadius.circular(25),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            "确认开通并支付${_kit != null ? " ￥" + (_kit!['num'] as num).toStringAsFixed(2) : ''}",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 35),
                      child: Center(
                        child: Text(
                          "购买即视为同意《流量套餐充值协议》",
                          style: TextStyle(
                            fontSize: 11,
                            color: Color(0xFF666666),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 列表元素
  Widget _itemBuilder(context, index) {
    // 是否是选中项
    bool _isSelected = (_index == index) ? true : false;

    Map<String, dynamic> data = _items[index];

    return GestureDetector(
        onTap: () {
          _index = index;
          _kit = data;
          setState(() {});
        },
        child: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                color: _isSelected ? Color(0xFFFFFAF6) : Color(0xFFFFFFFF),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _isSelected ? Business.mainColor : Color(0xFFDFDFDF),
                  width: 1,
                ),
              ),
              padding:
                  EdgeInsets.only(left: 12, right: 12, top: 21, bottom: 21),
              alignment: Alignment.center,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        RichText(
                          text: TextSpan(
                              text: "￥",
                              style: TextStyle(
                                fontSize: 18,
                                color: _isSelected
                                    ? Business.mainColor
                                    : Color(0xFF3E414B),
                              ),
                              children: [
                                TextSpan(
                                  text: '${data['num']}',
                                  style: TextStyle(
                                    fontSize: 40,
                                    fontWeight: FontWeight.bold,
                                    color: _isSelected
                                        ? Business.mainColor
                                        : Color(0xFF3E414B),
                                  ),
                                ),
                              ]),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 21),
                    height: 33,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: _isSelected
                          ? Color(0x1AFF7500)
                          : Color(0xFFDFDFDF), // Color(0xFFFFFAF6)
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Text(
                      '${data['text']}',
                      style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: _isSelected
                              ? Business.mainColor
                              : Color(0xFF747474)),
                    ),
                  ),
                ],
              ),
            ),
            _isSelected
                ? Positioned(
                    left: 0,
                    top: 0,
                    child: SizedBox(
                      width: 38,
                      height: 38,
                      child: Image.asset('assets/images/meal/1.png'),
                    ))
                : SizedBox(),
          ],
        ));
  }

  /// 加载数据
  void _loadDatas() async {
    try {
      EasyLoading.show();
      final data = await Http.get('/api/device/$id');
      EasyLoading.dismiss();
      final result = ResultModel<DeviceModel>.fromJson(
          data, (json) => DeviceModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        device = result.data as DeviceModel;
        if (mounted) setState(() {});
      } else {
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.message ?? "网络加载失败！");
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}
