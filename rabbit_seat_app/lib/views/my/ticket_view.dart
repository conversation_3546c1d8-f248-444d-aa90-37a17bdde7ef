import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/list/list_model.dart';

import '../../models/ticket/ticket_model.dart';
import '../../services/ticket_service.dart';
import '../../widgets/buttom/ripple_button.dart';
import '../../widgets/common/appbar.dart';
import '../../widgets/refresh/index.dart';

import 'package:date_format/date_format.dart';
import 'package:url_launcher/url_launcher.dart';

/// 我的卡券
class TicketView extends StatefulWidget {
  const TicketView({Key? key}) : super(key: key);

  @override
  State<TicketView> createState() => _TicketViewState();
}

class _TicketViewState extends State<TicketView> {
  /// 页码
  int _pageIndex = 0;

  /// 数据项
  List<dynamic> _items = [];

  /// 属性控制器
  late EasyRefreshController _controller;

  @override
  void initState() {
    super.initState();
    // 初始化
    _controller = EasyRefreshController();
    // 请求数据
    Future.delayed(Duration(milliseconds: 100), () {
      _controller.callRefresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefAppBar(context: context, title: "我的卡券"),
      body: _bodyBuilder(context),
    );
  }

  /// 主体构建
  Widget _bodyBuilder(BuildContext context) {
    return EasyRefresh.custom(
      // 没有数据组件
      emptyWidget: _items.length == 0 ? EmptyWidget() : null,
      controller: _controller,
      header: CustomRefreshHeader(),
      footer: CustomRefreshFooter(),
      slivers: [
        /// 列表数据
        SliverPadding(
          padding: EdgeInsets.only(
              left: 12,
              right: 12,
              top: 12,
              bottom: MediaQuery.of(context).padding.bottom),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              _itemBuilder,
              childCount: _items.length,
            ),
          ),
        ),
      ],
      shrinkWrap: true,
      onRefresh: () async {
        // 刷新
        _loadDatas(0);
      },
      // onLoad: () async {
      //   // 加载更多
      //   _loadDatas(_pageIndex + 1);
      // },
    );
  }

  /// 列表元素
  Widget _itemBuilder(context, index) {
    return TicketItemWidget(
      data: _items[index],
    );
  }

  /// 加载数据
  void _loadDatas(int index) async {
    TicketService.loadDatas(
      pageIndex: index,
      success: (List<TicketModel>? data) {
        if (data != null) {
          if (index == 0) {
            _items = data;
            // 重置加载没有更多数据
            _controller.resetLoadState();
            // 刷新结束
            _controller.finishRefresh();
          }
          // else {
          //   _items.addAll(data ?? []);
          //   // 刷新结束， 控制加载，如果当前的加载条目，大于总条目，显示没有更多数据
          //   _controller.finishLoad(noMore: _items.length >= data.totalElements);
          // }
        }
        _pageIndex = index;
        if (mounted) setState(() {});
      },
      failure: (msg) {
        index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
        EasyLoading.showToast(msg);
      },
    );
  }
}

/// 我的卡券列表元素项部件
class TicketItemWidget extends StatefulWidget {
  final TicketModel data;

  const TicketItemWidget({Key? key, required this.data}) : super(key: key);

  @override
  State<TicketItemWidget> createState() => _TicketItemWidgetState();
}

class _TicketItemWidgetState extends State<TicketItemWidget> {
  // 展开
  bool _isExpand = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12)),
        boxShadow: [
          BoxShadow(
            color: Color(0x17828282),
            offset: Offset(0, 0),
            blurRadius: 10,
            spreadRadius: 10,
          )
        ],
      ),
      child: Column(
        children: [
          // 记录数据
          Container(
            height: 88,
            child: Stack(
              children: [
                Container(
                  child: Row(
                    children: [
                      _amountWidget("${widget.data.price}"),
                      _divider(),
                      Expanded(child: _contentWidget()),
                      _toUseButtonWidget(),
                    ],
                  ),
                ),
                _overdueWidget(),
              ],
            ),
          ),
          // 展开数据
          _isExpand ? _useRulesWidget() : Container(),
        ],
      ),
    );
  }

  /// 金额
  Widget _amountWidget(String amount) {
    return Container(
      width: 90,
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            amount,
            style: TextStyle(
              fontSize: 31,
              fontWeight: FontWeight.bold,
              color: Business.mainColor,
            ),
          ),
          SizedBox(
            height: 5,
          ),
          Text(
            "优惠券(元)",
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Color(0xFF595959),
            ),
          ),
        ],
      ),
    );
  }

  /// 分隔线条
  Widget _divider() {
    return Container(
      margin: EdgeInsets.only(left: 5, right: 17),
      padding: EdgeInsets.only(top: 10, bottom: 10),
      child: Image.asset(
        "assets/images/ticket/3.png",
        width: 2,
        fit: BoxFit.fill,
      ),
    );
  }

  /// 信息
  Widget _contentWidget() {
    // 过期时间
    // 根据毫秒来创建时间对象
    var _time = DateTime.fromMillisecondsSinceEpoch(
        widget.data.expiredTime.toInt(),
        isUtc: true);
    var _expTime = formatDate(_time, ['yyyy', '-', 'mm', '-', 'dd']);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "${widget.data.name}",
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Color(0xFF000000),
          ),
        ),
        SizedBox(
          height: 5,
        ),
        Text(
          "有效期：$_expTime",
          style: TextStyle(
            fontSize: 12,
            color: Color(0xFF595959),
          ),
        ),
        SizedBox(
          height: 5,
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              _isExpand = !_isExpand;
            });
          },
          child: Container(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "使用规则",
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF595959),
                  ),
                ),
                SizedBox(
                  width: 6,
                ),
                SizedBox(
                  width: 11,
                  height: 11,
                  child: Image.asset(
                    "assets/images/ticket/1.png",
                    fit: BoxFit.fill,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 使用按钮
  Widget _toUseButtonWidget() {
    return Padding(
      padding: EdgeInsets.only(left: 15, right: 15),
      child: RippleButton(
        color: Business.mainColor,
        radius: 28 / 2,
        child: Container(
          height: 28,
          width: 70,
          alignment: Alignment.center,
          child: Text(
            "去使用",
            style: TextStyle(fontSize: 13, color: Colors.white),
          ),
        ),
        onTap: () {
          print('去使用');
          if (widget.data.taobaoLink != null &&
              widget.data.taobaoLink!.isNotEmpty) {
            // launchUrl(Uri.parse(widget.data.taobaoLink!));
            launchUrl(Uri.parse('https://www.taobao.com'));
          } else {
            EasyLoading.showToast("没有可访问淘宝链接");
          }
        },
      ),
    );
  }

  /// 到期角标
  Widget _overdueWidget() {
    String _text = "将到期";
    // 判断是否到期
    final _timeNow = DateTime.now().microsecondsSinceEpoch;
    // 如果当前时间小于过期时间，不显示
    if (_timeNow > widget.data.expiredTime) _text = '已过期';//return Container();
    return Positioned(
      top: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          color: Business.mainColor.withOpacity(0.1),
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(12),
            bottomLeft: Radius.circular(12),
          ),
        ),
        alignment: Alignment.center,
        padding: EdgeInsets.only(left: 8, top: 3.5, bottom: 3.5, right: 8),
        child: Text(
          _text, //"将到期",
          style: TextStyle(
            fontSize: 10,
            color: Business.mainColor,
          ),
        ),
      ),
    );
  }

  /// 使用规则
  Widget _useRulesWidget() {
    return Container(
      margin: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 8),
      padding: EdgeInsets.only(left: 12, right: 12, top: 10, bottom: 10),
      decoration: BoxDecoration(
        color: Color(0xFFF8F8F8),
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      width: double.infinity,
      alignment: Alignment.topLeft,
      child: Text(
        "${widget.data.description}",
        style: TextStyle(
          fontSize: 11,
          color: Color(0xFF595959),
        ),
      ),
    );
  }
}
