import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:rabbit_seat_app/widgets/dialog/dialog.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../widgets/buttom/ripple_button.dart';
import '../../widgets/common/appbar.dart';

/**
 * 技术支持
 */
class SupportView extends StatefulWidget {
  const SupportView({Key? key}) : super(key: key);

  @override
  State<SupportView> createState() => _SupportViewState();
}

class _SupportViewState extends State<SupportView> {
  String _localVersion = '1.0';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    // 初始化版本号
    _init();
  }

  void _init() async {
    /// 加载本地版本信息
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    _localVersion = packageInfo.version;
    print("localVersion:$_localVersion");
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于375dp * 812dp的屏幕
    

    return Scaffold(
      appBar: DefAppBar(context: context, title: "技术支持"),
      // appBar: AppBar(title: Text("技术支持"),),
      body: Container(
        padding: EdgeInsets.only(left: 12.w, top: 12.w, right: 12.w),
        child: Column(
          children: [
            _itemBoxWidget(
              context: context,
              title: "常见问题",
              extra: Icon(
                Icons.arrow_forward_ios,
                color: Color(0xFFA0A0A0),
                size: 14.w,
              ),
              onTap: () {
                // Navigator.push(
                //     context, MaterialPageRoute(builder: (ctx) => FAQPage()));
                Navigator.pushNamed(context, '/faq');
              },
            ),
            _itemBoxWidget(
              context: context,
              title: "安装教程",
              extra: Icon(
                Icons.arrow_forward_ios,
                color: Color(0xFFA0A0A0),
                size: 14.w,
              ),
              onTap: () {
                // Navigator.push(
                //     context, MaterialPageRoute(builder: (ctx) => CoursePage()));
                Navigator.pushNamed(context, '/course');
              },
            ),
            _itemBoxWidget(
              context: context,
              title: "服务电话",
              extra: Text(
                "************",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Color(0xFF666666),
                ),
              ),
              onTap: () {
                print("拨号");
                // 拨号事件
                DefDialog.showDialog1(context: context, message: "************", confirmText: '呼叫', confirm: () {
                  print("确定拨号");
                  _makePhoneCall();
                });
              },
            ),
            // _itemBoxWidget(
            //   context: context,
            //   title: "当前版本号",
            //   extra: Text(
            //     "V$_localVersion",
            //     style: TextStyle(
            //       fontSize: 14.sp,
            //       color: Color(0xFF666666),
            //     ),
            //   ),
            //   onTap: () {
            //     String _v1 = "1.0.9.1";
            //     String _v2 = "1";
            //     bool _isUpdate = false;
            //     List<String> sp1 = _v1.split('.'); // 网络
            //     List<String> sp2 = _v2.split('.'); // 本地
            //     for (int i = 0; i < max(sp1.length, sp2.length); i++) {
            //       int _m1 = 0;
            //       int _m2 = 0;
            //       if (i < sp1.length) _m1 = int.tryParse(sp1[i] as String) ?? 0;
            //       if (i < sp2.length) _m2 = int.tryParse(sp2[i] as String) ?? 0;
            //       if (_m1 > _m2) _isUpdate = true;
            //     }
            //     if (_isUpdate) {
            //       print("版本更新");
            //     } else {
            //       print("没有新版本");
            //     }
            //   },
            // ),
          ],
        ),
      ),
    );
  }

  /// 圆角容器
  Widget _itemBoxWidget(
      {required BuildContext context,
      required String title,
      required Widget extra,
      required Function() onTap}) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12.w)),
        boxShadow: [
          BoxShadow(
            color: Color(0x17828282),
            offset: Offset(0, 0),
            blurRadius: 10.5,
            spreadRadius: 5,
          )
        ],
      ),
      child: RippleButton(
        child: Container(
          height: 55.h,
          padding: EdgeInsets.only(left: 18.w, right: 18.w),
          child: Row(
            children: [
              Expanded(
                  child: Text(
                title,
                style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF000000),
                ),
              )),
              SizedBox(
                width: 10.w,
              ),
              extra,
            ],
          ),
        ),
        radius: 12.w,
        color: Colors.white,
        onTap: onTap,
      ),
    );
  }

  /// 拨号事件
  void _makePhoneCall() async {
    final String phoneNumber = '';
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    // 拨号
    await launchUrl(launchUri);
  }
}
