import 'package:city_pickers/city_pickers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/models/user/index.dart';
import 'package:rabbit_seat_app/provider/user_provider.dart';
import 'package:rabbit_seat_app/utils/signal.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';
import 'package:rabbit_seat_app/widgets/dialog/dialog.dart';

import '../../services/help_service.dart';
import '../../services/user_service.dart';
import '../../utils/global.dart';
import '../../utils/http.dart';
import '../../utils/utils.dart';
import '../../widgets/user_info/my_box.dart';
import '../../widgets/user_info/my_box_items.dart';

import 'package:city_pickers/meta/province.dart' as meta;
import 'package:city_pickers/modal/result.dart';

class UserInfoView extends StatefulWidget {
  const UserInfoView({Key? key}) : super(key: key);

  @override
  State<UserInfoView> createState() => _UserInfoViewState();
}

class _UserInfoViewState extends State<UserInfoView>
    with SingleTickerProviderStateMixin {
  // StreamSubscription? subEvents;

  UserModel? user;

  // String? cityName;

  /// 图片选择器
  ImagePicker _imagePicker = ImagePicker();

  /// 头像文件路径
  XFile? _portraitFile;

  /// 头像上传id
  String? _portraitUploadId;

  @override
  void initState() {
    super.initState();

    // init();
    // subEvents = eventBus.on<getUserEvent>().listen((event) {
    //   init();
    // });
  }

  void init() async {
    // setState(() {
    // user = Global.profile.user!;
    // if (user?.city != null && user?.city != "") {
    /// 放开注释
    // String? provinceCode = user?.city!.substring(0, 2);
    // var findKey = meta.provincesData.keys
    //     .firstWhere((element) => element.substring(0, 2) == provinceCode!);
    // String? code4 = user?.city!.substring(0, 4);

    // var findCounty = meta.citiesData.keys
    //     .firstWhere((element) => element.contains(code4!));
    // var findCityItem = meta.citiesData[findCounty].keys
    //     .firstWhere((element) => element == user?.city);

    /// 放开注释
    // cityName = meta.provincesData[findKey]! +
    //     "" +
    //     meta.citiesData[provinceCode! + "0000"][code4! + "00"]["name"];
    // }
    // if (user?.babyInfos != null) {
    //   var length = user!.babyInfos!.length;
    //   babyInfo = "已添加$length个宝宝";
    // }
    // });
  }

  @override
  void dispose() {
    super.dispose();
    // subEvents?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    /// 加载用户信息
    user = context.watch<UserProvider>().user;

    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: "个人信息",
      ),
      body: _builderBody(context),
      backgroundColor: Color.fromARGB(255, 249, 249, 249),
    );
  }

  // String babyInfo = "未添加宝宝";

  /*
  Widget _buildBody() {
    return Center(
      child: Column(
        children: [
          MyBoxItems(
            items: [
              MyBoxItem("头像", null, () {
                print(">>> 选择头像");

                DefDialog.showPixBottomSheet(context, (type) {
                  if (type == 1) {
                    _takePhotos();
                  } else if (type == 2) {
                    _galleryPhotos();
                  }
                });

              }, subImage: user?.avatar),
              MyBoxItem(
                "昵称",
                user?.nickName,
                    () {
                  Navigator.pushNamed(context, '/modify-name',
                      arguments: {"nickName": user?.nickName}).then((value) {
                        setState(() {});
                  });
                },
                isEnd: true,
              )
            ],
          ),
          MyBox(
            title: "地区",
            subTitle: cityName,
            top: 11.h,
            onTap: () async {
              /// 放开注释
              // ResultModel? result;
              // if (user?.city == null) {
              //   result =
              //   await CityPickers.showFullPageCityPicker(context: context);
              // } else {
              //   result = await CityPickers.showFullPageCityPicker(
              //       context: context, locationCode: user?.city as String);
              // }
              // if (result?.areaId != null) {
              //   var user = User();
              //   user.id = Global.profile.user?.id;
              //   user.city = result?.areaId;
              //   var res = await UserService.updateUser(user);
              //   if (res == true) {
              //     await UserService.getUser(user.id);
              //     User myUser = User();
              //     eventBus.fire(getUserEvent(myUser));
              //   }
              // }
            },
          ),
          MyBox(
            title: "手机绑定",
            subTitle:
            user?.telephone?.replaceFirst(RegExp(r'\d{4}'), '****', 3),
            top: 11.h,
            onTap: () {
              DefDialog.showDialog1(context: context, title: "提示", message: "更换绑定的手机号？", confirm: () {
                print(">>> 更换手机号！");
                Navigator.pushNamed(context, '/tel-step1');
              }, cancel: () {});
              // Util.showMessage(context, "提示", "更换绑定的手机号？", () {
              //   Navigator.pushNamed(context, 'telephonStep1');
              // });
            },
          ),
          MyBox(
            title: "宝宝信息",
            subTitle: babyInfo,
            top: 11.h,
            onTap: () {
              if (user != null &&
                  user!.babyInfos != null &&
                  user!.babyInfos!.isEmpty) {
                Navigator.pushNamed(context, '/bady-add');
                return;
              }
              Navigator.pushNamed(context, '/bady-list');
            },
          )
        ],
      ),
    );
  }
  */

  Widget _builderBody(BuildContext context) {
    /// 默认头像
    Widget? _portraitWidget =
        Image(image: AssetImage('assets/images/logo.png'), fit: BoxFit.cover);
    ;

    /// 从网络中加载头像
    if (user != null && user!.avatar != null && user!.avatar!.isNotEmpty) {
      _portraitWidget =
          Image.network(kReleaseBaseUrl + user!.avatar!, fit: BoxFit.cover);
    }

    // 用户昵称
    String _nickName = '两只兔子';
    if (user != null) {
      if (user!.nickName != null && user!.nickName!.isNotEmpty) {
        _nickName = user!.nickName!;
      } else {
        if (user!.id != null && user!.id!.isNotEmpty && user!.id!.length >= 6) {
          _nickName = '两只兔子' + user!.id!.substring((user!.id!.length - 6));
        }
      }
    }
    // 地址信息
    String _city = '';
    if (user != null && user!.city != null && user!.city!.isNotEmpty) {
      /// 放开注释
      String? provinceCode = user?.city!.substring(0, 2);
      var findKey = meta.provincesData.keys
          .firstWhere((element) => element.substring(0, 2) == provinceCode!);
      String? code4 = user?.city!.substring(0, 4);

      var findCounty = meta.citiesData.keys
          .firstWhere((element) => element.contains(code4!));
      var findCityItem = meta.citiesData[findCounty].keys
          .firstWhere((element) => element == user?.city);

      /// 放开注释
      _city = meta.provincesData[findKey]! +
          "" +
          meta.citiesData[provinceCode! + "0000"][code4! + "00"]["name"];
    }

    // 手机号码
    String _phone =
        user?.telephone?.replaceFirst(RegExp(r'\d{4}'), '****', 3) ?? '';

    // 宝宝信息
    String _babyInfo = "未添加宝宝";
    if (user != null &&
        user!.babyInfos != null &&
        user!.babyInfos!.length > 0) {
      _babyInfo = "已添加${user!.babyInfos!.length}个宝宝";
    }

    return Column(
      children: [
        _boxWidget(
            child: Column(
          children: [
            _itemWidget('头像', child: _portraitWidget, onTap: () {
              // 弹框
              DefDialog.showPixBottomSheet(context, (type) {
                if (type == 1) {
                  _takePhotos();
                } else if (type == 2) {
                  _galleryPhotos();
                }
              }, false);
            }),
            Divider(
              indent: 18,
              endIndent: 18,
              height: 1,
              color: Color(0xFFEAEAEA),
            ),
            _itemWidget('昵称', text: _nickName, onTap: () {
              Navigator.pushNamed(context, '/modify-name',
                  arguments: {"nickName": user?.nickName});
            }),
          ],
        )),
        _boxWidget(
            child: _itemWidget('地区', text: _city, onTap: () async {
          print(">>> aaaaa");

          /// 放开注释
          Result? result;
          if (user?.city == null) {
            result = await CityPickers.showFullPageCityPicker(context: context);
          } else {
            result = await CityPickers.showFullPageCityPicker(
                context: context, locationCode: user?.city as String);
          }
          if (result?.areaId != null) {
            // 跟新
            _updateUserRegion(result!.areaId!);
          }
        })),
        _boxWidget(
            child: _itemWidget('手机绑定', text: _phone, onTap: () {
          DefDialog.showDialog1(
              context: context,
              title: "提示",
              message: "更换绑定的手机号？",
              confirm: () {
                print(">>> 更换手机号！");
                Navigator.pushNamed(context, '/tel-step1');
              },
              cancel: () {});
        })),
        _boxWidget(
            child: _itemWidget('宝宝信息', text: _babyInfo, onTap: () {
          if (user != null &&
              user!.babyInfos != null &&
              user!.babyInfos!.isEmpty) {
            Navigator.pushNamed(context, '/bady-add');
            return;
          }
          Navigator.pushNamed(context, '/bady-list');
        })),
      ],
    );
  }

  Widget _boxWidget({required Widget child}) {
    return Padding(
      padding: EdgeInsets.only(left: 12, top: 12, right: 12),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Color(0x17828282), // 阴影的颜色
              offset: Offset(0, 0), // 阴影与容器的距离
              blurRadius: 12.0, // 高斯的标准偏差与盒子的形状卷积。
              spreadRadius: 11.0, // 在应用模糊之前，框应该膨胀的量。
            ),
          ],
        ),
        child: child,
      ),
    );
  }

  Widget _itemWidget(String title,
      {String? text, Widget? child, Function()? onTap}) {
    Widget _content = Container();
    if (text != null) {
      _content = Expanded(
        child: Text(
          text,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.right,
          style: TextStyle(fontSize: 14, color: Color(0xFF666666)),
        ),
      );
    } else if (child != null) {
      _content = Expanded(
        child: Container(
          alignment: Alignment.centerRight,
          child: ClipOval(
            child: SizedBox(
              width: 54,
              height: 54,
              child: child,
            ),
          ),
        ),
      );
    }
    return Material(
      color: Colors.transparent,
      child: Ink(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Container(
            constraints: BoxConstraints(minHeight: 55),
            padding: EdgeInsets.only(left: 24, right: 22, top: 15, bottom: 15),
            alignment: Alignment.center,
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(right: 10),
                  child: Text(
                    title,
                    style: TextStyle(fontSize: 15, color: Color(0xFF2A2A2A)),
                  ),
                ),
                _content,
                Padding(
                  padding: EdgeInsets.only(left: 15),
                  child: Icon(
                    Icons.arrow_forward_ios_outlined,
                    color: Color(0xFFA0A0A0),
                    size: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 拍照
  void _takePhotos() async {
    Util.checkCameraPermissionStatus(context, '头像拍摄', () async {
      XFile? _imgFile = await _imagePicker.pickImage(
          source: ImageSource.camera, maxWidth: 1000);
      if (_imgFile != null) {
        // 显示图片
        _portraitFile = _imgFile;
        // 上传图片
        _uploadUserPortrait();
        // setState(() {
        //   _portraitFile = _imgFile;
        // });
        // // 上传图片
        // var upload = UploadFile();
        // upload.filePath = _imgFile.path;
        // var res = await HelpService.uploadFile(_imgFile.path);
        // setState(() {
        //   upload.id = res?.fileId;
        //   _portraitUploadId = res?.fileId;
        // });
      }
    });
    // // SmartDialog.dismiss();
    // // 相机授权
    // bool _camera = await Permission.camera
    //     .request()
    //     .isGranted;
    // // 存储授权
    // bool _storage = await Permission.storage
    //     .request()
    //     .isGranted;

    // if (_camera && _storage) {

    // } else {
    //   EasyLoading.showToast('权限申请失败');
    //   // SmartDialog.showToast("权限申请失败");
    // }
  }

  /// 相册获取图片
  void _galleryPhotos() async {
    Util.checkCameraPermissionStatus(context, '头像文件选取', () async {
      XFile? _imgFile = await _imagePicker.pickImage(
          source: ImageSource.gallery, maxWidth: 1000);
      if (_imgFile != null) {
        // 裁剪图片
        _portraitFile = XFile(_imgFile.path);

        // 上传图片
        _uploadUserPortrait();
        // CroppedFile? _cFile = await _cropImage(_imgFile);
        // if (_cFile != null) {
        //   // 显示图片
        //   setState(() {
        //     _portraitFile = XFile(_cFile.path);
        //   });
        //
        //   // 上传图片
        //   // var upload = UploadFile();
        //   // upload.filePath = _imgFile.path;
        //   // var res = await CommonService.uploadFile(_imgFile.path);
        //   // setState(() {
        //   //   upload.id = res?.fileId;
        //   //   _portraitUploadId = res?.fileId;
        //   // });
        // }
      }
    });
  }

  /// 图片裁剪
  // Future<CroppedFile?> _cropImage(XFile xFile) async {
  //   CroppedFile? _file = await ImageCropper().cropImage(
  //     sourcePath: xFile.path,
  //     cropStyle: CropStyle.circle,
  //     aspectRatioPresets: [
  //       CropAspectRatioPreset.ratio3x2,
  //       CropAspectRatioPreset.original,
  //       CropAspectRatioPreset.ratio4x3,
  //       CropAspectRatioPreset.ratio16x9,
  //     ],
  //     uiSettings: [
  //       AndroidUiSettings(
  //         toolbarTitle: "编辑图片",
  //         toolbarColor: Colors.white,
  //         toolbarWidgetColor: Colors.grey,
  //         initAspectRatio: CropAspectRatioPreset.original,
  //         lockAspectRatio: false,
  //       ),
  //       IOSUiSettings(minimumAspectRatio: 1.0),
  //     ],
  //   );
  //   return _file;
  // }

  /// 上传用户头像
  void _uploadUserPortrait() async {
    EasyLoading.show();
    // 上传图片
    final _fleRes = await HelpService.uploadFile(_portraitFile!.path);
    if (_fleRes == null) {
      EasyLoading.dismiss();
      EasyLoading.showToast("图片上传失败!");
      return;
    }
    UserService.updateUserInfo(
        uid: Global.profile.user!.id!,
        avatar: _fleRes.fileUrl,
        success: (data) {
          EasyLoading.dismiss();
          EasyLoading.showToast("上传成功！");
          // user!.avatar = _fleRes.fileUrl;
          context.read<UserProvider>().setAvatar(_fleRes.fileUrl!);
          // KitSignal().send('user-refresh');
          if (mounted) setState(() {});
        },
        failure: (msg) {
          EasyLoading.dismiss();
          EasyLoading.showToast(msg);
        });
  }

  /// 更新地址数据
  void _updateUserRegion(String areaId) async {
    try {
      EasyLoading.show();
      UserService.updateUserInfo(
          uid: Global.profile.user!.id!,
          city: areaId,
          success: (data) {
            EasyLoading.dismiss();
            EasyLoading.showToast("保存成功");
            context.read<UserProvider>().setCity(areaId);
            if (mounted) setState(() {});
          },
          failure: (msg) {
            EasyLoading.dismiss();
            EasyLoading.showToast(msg);
          });
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}
