import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/error.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/provider/user_provider.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/widgets/common/appbar.dart';

import '../../utils/global.dart';
import '../../utils/utils.dart';
import '../../widgets/buttom/ripple_button.dart';

/**
 * 添加紧急联系人
 */
class ContactsAddView extends StatefulWidget {
  const ContactsAddView({Key? key}) : super(key: key);

  @override
  State<ContactsAddView> createState() => _ContactsAddViewState();
}

class _ContactsAddViewState extends State<ContactsAddView> {
  // 姓名
  String? _name;

  // 电话
  String? _phone;

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于360dp * 690dp的屏幕
    

    return Scaffold(
      appBar: DefAppBar(context: context, title: "紧急联系人"),
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          Util.removePrimaryFocus(context);
        },
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(left: 12.w, top: 12.h, right: 12.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x17828282), // 阴影的颜色
                    offset: Offset(0, 0), // 阴影与容器的距离
                    blurRadius: 10.0, // 高斯的标准偏差与盒子的形状卷积。
                    spreadRadius: 5.0, // 在应用模糊之前，框应该膨胀的量。
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _inputWidgt(
                      context: context,
                      title: "姓名",
                      hintText: "请输入紧急联系人姓名",
                      onChanged: (value) {
                        _name = value;
                      }),
                  const Divider(
                    indent: 18,
                    endIndent: 18,
                    height: 1,
                    color: Color(0xFFEAEAEA),
                  ),
                  _inputWidgt(
                      context: context,
                      title: "电话",
                      hintText: "请输入电话",
                      onChanged: (value) {
                        _phone = value;
                      },
                      keyboardType: TextInputType.phone),
                ],
              ),
            ),
            Expanded(child: Container()),
            const Padding(
              padding: EdgeInsets.only(left: 20, right: 20, bottom: 36),
              child: Text(
                "设置紧急联系人可以提升宝宝安全，当宝宝独处在车内平台工作人员将以短信或电话方式通知您！",
                style: TextStyle(fontSize: 13, color: Color(0xFF666666)),
              ),
            ),
            Padding(
                padding: EdgeInsets.only(
                    left: 12,
                    right: 12,
                    bottom: 12 + MediaQuery.of(context).padding.bottom),
                child: RippleButton(
                  color: Business.mainColor,
                  radius: 25,
                  child: Container(
                    height: 50,
                    alignment: Alignment.center,
                    child: Text(
                      "添加",
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  onTap: () {
                    _addContacts();
                  },
                )),
          ],
        ),
      ),
      resizeToAvoidBottomInset: false,
    );
  }

  // 输入部件
  Widget _inputWidgt(
      {required BuildContext context,
      required String title,
      String? hintText,
      Function(String)? onChanged,
      TextInputType? keyboardType}) {
    return Container(
      padding: EdgeInsets.only(left: 24.w, right: 30.w),
      alignment: Alignment.centerLeft,
      height: 55,
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 15.sp, color: const Color(0xFF2A2A2A)),
          ),
          SizedBox(
            width: 15.w,
          ),
          Expanded(
            child: TextField(
              keyboardType: keyboardType ?? TextInputType.text,
              textAlign: TextAlign.end,
              style: TextStyle(fontSize: 14.sp, color: const Color(0xFF666666)),
              onChanged: (value) {
                if (onChanged != null) onChanged(value);
              },
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: hintText,
                hintStyle: const TextStyle(color: Color(0xFFAFAFAF)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 添加联系人
  void _addContacts() async {
    if (_name == null || _name!.isEmpty) {
      EasyLoading.showToast("请输入联系人姓名！");
      return;
    }
    if (_phone == null || _phone!.isEmpty) {
      EasyLoading.showToast("请输入电话！");
      return;
    }
    try {
      EasyLoading.show();
      Map<String, dynamic> params = {
        "name": _name,
        "telephone": _phone,
      };
      final data = await Http.post(
          "/api/appuser/emergencyContact/${Global.profile.user!.id}",
          data: params);
      EasyLoading.dismiss();
      final result = ResultModel.fromJson(data, (json) => json);
      if (result.code == 200) {
        Navigator.of(context).pop(true);

        /// 放开注释
        // await UserService.getUser(Global.profile.user?.id);
        // User myUser = User();
        // eventBus.fire(getUserEvent(myUser));
      } else {
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.message ?? "网络加载失败！");
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
}
