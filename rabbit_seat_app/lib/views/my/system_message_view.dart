import 'package:date_format/date_format.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:rabbit_seat_app/models/message/inform_model.dart';
import 'package:rabbit_seat_app/models/share/share_model.dart';
import 'package:rabbit_seat_app/utils/global.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:rabbit_seat_app/utils/rx.dart';
import 'package:rabbit_seat_app/widgets/dialog/dialog.dart';

import '../../services/device_service.dart';
import '../../services/notice_service.dart';
import '../../widgets/buttom/ripple_button.dart';
import '../../widgets/common/appbar.dart';
import '../../widgets/refresh/index.dart';

/// 系统消息列表
class SystemMsgView extends StatefulWidget {
  const SystemMsgView({Key? key}) : super(key: key);

  @override
  State<SystemMsgView> createState() => _SystemMsgViewState();
}

class _SystemMsgViewState extends State<SystemMsgView> {
  // 刷新控制
  EasyRefreshController _controller = EasyRefreshController();

  // 页码
  int _pageIndex = 0;

  // 数据
  List<InformModel> _items = [];

  @override
  void initState() {
    super.initState();
    // 初始化刷新
    Future.delayed(Duration(milliseconds: 100), () {
      if (mounted) _controller.callRefresh();
    });

    //
    Rx.noticeAcceptSharedSubject
        .where((event) => (event as String).isNotEmpty)
        .listen((event) {
      InformModel? _temp;
      _items.forEach((element) {
        if (element.id == event) {
          _temp = element;
        }
      });
      setState(() {
        _items.remove(_temp);
      });
    });
  }

  sendReadAll() {
    try {
      for (var m in _items) {
        NoticeService.sendReadTag(
            id: m.id!,
            success: (data) {},
            failure: (msg) {
              EasyLoading.showToast(msg);
            });
      }
    } catch (ex) {
      print(ex.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: "系统消息",
        actions: [
          TextButton(
            onPressed: () {
              DefDialog.showDialog1(
                  context: context,
                  message: '清空所有系统消息？',
                  confirm: () {
                    sendReadAll();
                    setState(() {
                      _items = [];
                    });
                  });
            },
            child: Text(
              "清空",
              style: TextStyle(fontSize: 15, color: Color(0xFF000000)),
            ),
          ),
        ],
      ),
      // body: Container(
      //   child: ListView.builder(
      //       padding: EdgeInsets.zero, itemCount: 3, itemBuilder: _itemBuilder),
      // ),
      body: _bodyBuilder(context),
    );
  }

  /// 主体构建
  Widget _bodyBuilder(BuildContext context) {
    return EasyRefresh.custom(
      // 没有数据组件
      emptyWidget: _items.length == 0 ? EmptyWidget() : null,
      controller: _controller,
      header: CustomRefreshHeader(),
      footer: CustomRefreshFooter(),
      slivers: [
        /// 列表数据
        SliverPadding(
          padding: EdgeInsets.zero,
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              _itemBuilder,
              childCount: _items.length,
            ),
          ),
        ),
      ],
      shrinkWrap: true,
      onRefresh: () async {
        // 刷新
        _loadDatas(0);
      },
      onLoad: () async {
        // 加载更多
        _loadDatas(_pageIndex++);
      },
    );
  }

  // 列表元素
  Widget _itemBuilder(context, index) {
    InformModel _info = _items[index];
    String _asset = "";
    // Normal普通消息 DeviceShare设备共享DeviceEvent设备消息
    if (_info.messageType == 'DeviceShare') {
      _asset = "assets/images/my/22.png";
    } else {
      _asset = "assets/images/my/21.png";
    }
    final _time = DateTime.fromMillisecondsSinceEpoch(_info.createOn.toInt());
    var _timeText = formatDate(
        _time, ['yyyy', '-', 'mm', '-', 'dd', ' ', 'HH', ':', "mm", ":", "ss"]);
    return Column(
      children: [
        RippleButton(
          color: Colors.white,
          child: Container(
            padding: EdgeInsets.only(left: 15, right: 15, top: 20, bottom: 20),
            child: Row(
              children: [
                Stack(
                  children: [
                    // 图标
                    SizedBox(
                      width: 44,
                      height: 44,
                      child: Image.asset(
                        _asset,
                        fit: BoxFit.fill,
                      ),
                    ),
                    // 角标
                    _info.isRead == false
                        ? Positioned(
                            left: 2.5,
                            top: 2.5,
                            child: Container(
                              height: 10,
                              width: 10,
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(5)),
                                border:
                                    Border.all(color: Colors.white, width: 2),
                              ),
                            ),
                          )
                        : SizedBox(),
                  ],
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(left: 12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Expanded(
                              child: Text(
                                _info.title ?? '',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF2A2A2A),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            Text(
                              _timeText,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(0xFFB5B5B9),
                              ),
                            )
                          ],
                        ),
                        SizedBox(
                          height: 7,
                        ),
                        Text(
                          _info.content ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 13,
                            color: Color(0xFF595959),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          onTap: () {
            // Rx.noticeCountSubject.add(_items.length - 1);
            // 页面跳转
            Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => SystemMsgDetailsView(data: _info),
                )).then((value) {
              setState(() {
                _info.isRead = true;
              });
            });
          },
        ),
        Divider(
          height: 1,
          color: Color(0xFFF5F5F5),
        ),
      ],
    );
  }

  // 加载数据
  void _loadDatas(int index) {
    NoticeService.loadDatas(
      pageIndex: index,
      success: (datas) {
        if (datas != null) {
          if (index == 0) {
            _items = datas.content as List<InformModel>? ?? [];
            // 重置加载没有更多数据
            _controller.resetLoadState();
            // 刷新结束
            _controller.finishRefresh();
          } else {
            _items.addAll(datas.content as List<InformModel>? ?? []);
            // 刷新结束， 控制加载，如果当前的加载条目，大于总条目，显示没有更多数据
            _controller.finishLoad(
                noMore: _items.length >= datas.totalElements);
          }
        }
        // Rx.noticeCountSubject.add(_items.length);
        if (mounted) setState(() {});
      },
      failure: (msg) {
        index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
        EasyLoading.showToast(msg);
      },
    );
  }
}

/// ==================================================================
/// 系统通知详情
class SystemMsgDetailsView extends StatefulWidget {
  final InformModel data;

  SystemMsgDetailsView({Key? key, required this.data}) : super(key: key);

  // final String? title;
  // final String? details;
  // final String? time;

  // SystemMsgDetailsView({Key? key, this.title, this.details, this.time})
  //     : super(key: key);

  @override
  State<SystemMsgDetailsView> createState() => _SystemMsgDetailsViewState();
}

class _SystemMsgDetailsViewState extends State<SystemMsgDetailsView> {
  // 共享数据
  ShareModel? _shareInfo = null;

  @override
  void initState() {
    super.initState();

    if (widget.data.messageType != 'DeviceShare') {
      // 标记已读
      _sendReadTag(widget.data.id!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final _time =
        DateTime.fromMillisecondsSinceEpoch(widget.data.createOn.toInt());
    var _timeText = formatDate(
        _time, ['yyyy', '-', 'mm', '-', 'dd', ' ', 'HH', ':', "mm", ":", "ss"]);

    Widget _infoWidget = Container();
    // Normal普通消息 DeviceShare设备共享DeviceEvent设备消息
    if (widget.data.messageType == 'DeviceShare') {
      // 共享消息
      if (_shareInfo == null) {
        // 更具id 加载共享数据
        _loadShareDatas(widget.data.businessId);
      } else {
        // 显示页面
        _infoWidget = _ShareMsgInfoWidget(
          id: widget.data.businessId!,
          details: _shareInfo!,
          content: widget.data.content,
          time: _timeText,
          acceptTap: () {
            // 标记已读
            _sendReadTag(widget.data.id!);
          },
        );
      }
    } else {
      // 系统消息
      _infoWidget = _SystemMsgInfoWidget(
        title: widget.data.title ?? '',
        details: widget.data.content ?? '',
        time: _timeText,
      );
    }
    return Scaffold(
      appBar: DefAppBar(
        context: context,
        title: "系统消息",
      ),
      body: ListView(
        padding: EdgeInsets.only(
            left: 12,
            right: 12,
            top: 18,
            bottom: MediaQuery.of(context).padding.bottom),
        children: [
          _infoWidget,
        ],
      ),
    );
  }

  // 加载共享设备数据
  void _loadShareDatas(String? businessId) async {
    if (businessId == null || businessId.isEmpty) {
      EasyLoading.showToast("共享设备数据id不存在！");
      return;
    }
    EasyLoading.show();
    DeviceService.loadShareDetails(
        id: businessId,
        success: (data) {
          EasyLoading.dismiss();
          _shareInfo = data;
          if (mounted) setState(() {});
        },
        failure: (msg) {
          EasyLoading.dismiss();
          EasyLoading.showToast(msg);
        });
  }

  // 标记已读
  void _sendReadTag(String id) async {
    NoticeService.sendReadTag(
        id: id,
        success: (data) {},
        failure: (msg) {
          EasyLoading.showToast(msg);
        });
  }
}

/// ==================================================================
/// 系统通知信息部件
class _SystemMsgInfoWidget extends StatelessWidget {
  // 标题
  String? title;

  // 详情
  String? details;

  // 日期
  String? time;

  _SystemMsgInfoWidget({Key? key, this.title, this.details, this.time})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 16, right: 16, top: 18, bottom: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12)),
        boxShadow: [
          BoxShadow(
            color: Color(0x17828282),
            offset: Offset(0, 0),
            blurRadius: 10,
            spreadRadius: 10,
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title ?? '',
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2A2A2A),
            ),
          ),
          SizedBox(
            height: 15,
          ),
          Text(
            details ?? '',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF595959),
            ),
          ),
          SizedBox(
            height: 19,
          ),
          Text(
            time ?? '',
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFFB5B5B9),
            ),
          ),
        ],
      ),
    );
  }
}

/// ==================================================================
/// 共享通知信息部件
class _ShareMsgInfoWidget extends StatelessWidget {
  // 设备ID
  final String id;

  // 共享设备信息
  final ShareModel details;

  // 详情
  String? content;

  // 日期
  String? time;

  // 接收回调
  Function()? acceptTap;

  _ShareMsgInfoWidget({
    Key? key,
    required this.id,
    required this.details,
    this.content,
    this.time,
    this.acceptTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String? _url = details.user?.avatar != null
        ? kReleaseBaseUrl + details.user!.avatar!
        : null;

    return Container(
      padding: EdgeInsets.only(left: 16, right: 16, top: 18),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12)),
        boxShadow: [
          BoxShadow(
            color: Color(0x17828282),
            offset: Offset(0, 0),
            blurRadius: 10,
            spreadRadius: 10,
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 账户信息
          Row(
            children: [
              SizedBox(
                width: 30,
                height: 30,
                child: ClipOval(
                  child: details.user?.avatar != null
                      ? Image.network(
                          kReleaseBaseUrl + details.user!.avatar!,
                          fit: BoxFit.cover,
                        )
                      : Container(color: Colors.grey),
                ), //Image.asset(""),
              ),
              SizedBox(
                width: 10,
              ),
              Expanded(
                child: Text(
                  details.user!.name ?? '',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF212121),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 11,
          ),
          Text(
            content ?? '',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF595959),
            ),
          ),
          SizedBox(
            height: 13,
          ),
          Text(
            time ?? '',
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFFB5B5B9),
            ),
          ),
          SizedBox(
            height: 13,
          ),
          Divider(
            height: 1,
            color: Color(0xFFF5F5F5),
          ),
          Container(
            padding: EdgeInsets.only(top: 13, bottom: 17),
            alignment: Alignment.center,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                RippleButton(
                  color: Color(0xFFFF7200),
                  radius: 32 / 2,
                  child: Container(
                    height: 32,
                    width: 108,
                    alignment: Alignment.center,
                    child: Text(
                      "接受",
                      style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          color: Colors.white),
                    ),
                  ),
                  onTap: () {
                    print("接受");
                    if (details.createBy == Global.profile.user?.id) {
                      EasyLoading.showToast("自己的设备无需接受共享");
                      return;
                    }
                    _acceptShareDevices(context);
                  },
                ),
                SizedBox(
                  width: 18,
                ),
                RippleButton(
                  color: Color(0xFFF9F9F9),
                  radius: 32 / 2,
                  child: Container(
                    height: 32,
                    width: 108,
                    alignment: Alignment.center,
                    child: Text(
                      "忽略",
                      style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          color: Colors.black),
                    ),
                  ),
                  onTap: () {
                    print("忽略");
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 接收共享
  void _acceptShareDevices(BuildContext context) {
    EasyLoading.show();
    DeviceService.acceptShareDevices(
        id: id,
        success: (data) {
          EasyLoading.dismiss();
          Rx.noticeAcceptSharedSubject.add(id);
          if (acceptTap != null) acceptTap!();
          Navigator.pop(context);
        },
        failure: (msg) {
          EasyLoading.dismiss();
          EasyLoading.showToast(msg);
        });
  }
}
