import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/models/course/course_model.dart';
import 'package:rabbit_seat_app/models/error.dart';
import 'package:rabbit_seat_app/models/list/list_model.dart';
import 'package:rabbit_seat_app/models/result/result_model.dart';
import 'package:rabbit_seat_app/utils/http.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../widgets/buttom/ripple_button.dart';
import '../../widgets/common/appbar.dart';
import '../../widgets/refresh/index.dart';

/**
 * 安装教程
 */
class CourseView extends StatefulWidget {
  const CourseView({Key? key}) : super(key: key);

  @override
  State<CourseView> createState() => _CourseViewState();
}

class _CourseViewState extends State<CourseView> {
  /// 数据
  List<dynamic> _items = [];

  /// 页面
  int _pageIndex = 0;

  /// 控制器
  EasyRefreshController _controller = EasyRefreshController();

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration(milliseconds: 100), () {
      if (mounted) _controller.callRefresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于375dp * 812dp的屏幕

    return Scaffold(
      appBar: DefAppBar(context: context, title: "安全教程"),
      // appBar: AppBar(title: Text("常见问题")),
      // body: Container(
      //   padding: EdgeInsets.only(left: 12.w, top: 12.w, right: 12.w),
      //   child: ListView.builder(
      //     itemCount: _titles.length,
      //     itemBuilder: (context, index) {
      //       return _itemBoxWidget(
      //         context: context,
      //         title: _titles[index],
      //         onTap: () {},
      //       );
      //     },
      //   ),
      // ),
      body: _bodyBuilder(context),
    );
  }

  /// 主体构建
  Widget _bodyBuilder(BuildContext context) {
    return EasyRefresh.custom(
      // 没有数据组件
      emptyWidget: _items.length == 0 ? EmptyWidget() : null,
      controller: _controller,
      header: CustomRefreshHeader(),
      footer: CustomRefreshFooter(),
      slivers: [
        /// 列表数据
        SliverPadding(
          padding: EdgeInsets.only(
            left: 12,
            right: 12,
            top: 12,
            bottom: MediaQuery.of(context).padding.bottom,
          ),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              _itemBuilder,
              childCount: _items.length,
            ),
          ),
        ),
      ],
      shrinkWrap: true,
      onRefresh: () async {
        // 刷新
        _loadDatas(0);
      },
      onLoad: () async {
        // 加载更多
        _loadDatas(_pageIndex + 1);
      },
    );
  }

  /// 列表元素项
  Widget _itemBuilder(context, index) {
    CourseModel _model = _items[index];
    return _itemBoxWidget(
      context: context,
      title: _model.title ?? '',
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (ctx) => CourseDetailsView(
                      data: _model,
                    )));
      },
    );
  }

  /// 圆角容器
  Widget _itemBoxWidget(
      {required BuildContext context,
      required String title,
      required Function() onTap}) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12.w)),
        boxShadow: [
          BoxShadow(
            color: Color(0x17828282),
            offset: Offset(0, 0),
            blurRadius: 10.5,
            spreadRadius: 5,
          )
        ],
      ),
      child: RippleButton(
        child: Container(
          height: 55.h,
          padding: EdgeInsets.only(left: 18.w, right: 18.w),
          child: Row(
            children: [
              Expanded(
                  child: Text(
                title,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF000000),
                ),
              )),
              SizedBox(
                width: 10.w,
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Color(0xFFA0A0A0),
                size: 14.w,
              ),
            ],
          ),
        ),
        radius: 12.w,
        color: Colors.white,
        onTap: onTap,
      ),
    );
  }

  /// 加载数据
  void _loadDatas(int index) async {
    try {
      Map<String, dynamic> params = {
        'pageNo': index,
        'pageSize': 10,
      };
      final data = await Http.get('/api/tutorial/list', data: params);
      final result = ResultModel<ListModel>.fromJson(
          data,
          (json) => ListModel.fromJson(json as Map<String, dynamic>,
              (jsonT) => CourseModel.fromJson(jsonT as Map<String, dynamic>)));
      if (result.code == 200) {
        if (result.data != null) {
          if (index == 0) {
            _items = result.data!.content ?? [];
            // 重置加载没有更多数据
            _controller.resetLoadState();
            // 刷新结束
            _controller.finishRefresh();
          } else {
            _items.addAll(result.data!.content ?? []);
            // 刷新结束， 控制加载，如果当前的加载条目，大于总条目，显示没有更多数据
            _controller.finishLoad(
                noMore: _items.length >= result.data!.totalElements);
          }
        }
        _pageIndex = index;
        if (mounted) setState(() {});
      } else {
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
      EasyLoading.showToast(e.message ?? '网络请求失败！');
    } catch (e) {
      index == 0 ? _controller.finishRefresh() : _controller.finishLoad();
      EasyLoading.showToast(e.toString());
    }
  }
}

/// ------------------------------------------------------------
/// ------------------------------------------------------------

/// 安装详情
class CourseDetailsView extends StatefulWidget {
  final CourseModel data;
  const CourseDetailsView({Key? key, required this.data}) : super(key: key);

  @override
  State<CourseDetailsView> createState() => _CourseDetailsViewState();
}

class _CourseDetailsViewState extends State<CourseDetailsView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefAppBar(context: context, title: widget.data.title ?? ''),
      body: Container(
        padding: EdgeInsets.only(
            left: 20,
            right: 20,
            top: 20,
            bottom: MediaQuery.of(context).padding.bottom + 20),
        // child: Text(widget.data.content??'', style: TextStyle(fontSize: 16, color: Colors.black),),
        // child: Html(data: widget.data.content),
        child: ListView(
          children: [
            Html(
              data: widget.data.content,
              onLinkTap: (url, a, b) => {launchUrl(Uri.parse(url!))},
              extensions: [
                ImageExtension(
                    networkSchemas: {'https'},
                    networkDomains: {'api.buddybuzzy.com'})
              ],

              // {
              //   // networkSourceMatcher(domains: ["flutter.dev"]):
              //   //     (context, attributes, element) {
              //   //   return FlutterLogo(size: 36);
              //   // },
              //   networkSourceMatcher(): networkImageRender(
              //     // headers: {"Custom-Header": "some-value"},
              //     altWidget: (alt) => Text(alt ?? ""),
              //     loadingWidget: () => Text("Loading..."),
              //   ),
              //   (attr, _) => attr["src"] != null: networkImageRender(
              //       mapUrl: (url) => "https://api.buddybuzzy.com" + url!),
              // },
            ),
          ],
        ),
      ),
    );
  }
}
