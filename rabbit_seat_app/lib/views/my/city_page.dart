import 'package:flutter/material.dart';
import 'package:rabbit_seat_app/business.dart';

import 'datas.dart';

/**
 * 呈现选择器
 */
class CityPage extends StatefulWidget {
  /// 参数
  final Map<String, dynamic>? arguments;

  const CityPage({Key? key, this.arguments}) : super(key: key);

  @override
  State<CityPage> createState() => _CityPageState();
}

class _CityPageState extends State<CityPage> {
  late TextEditingController _textEditingController;
  FocusNode _focusNode = FocusNode();

  /// 0省； 1市； 2区/县； 3乡/镇/街道
  int _layer = 0;
  String? _code;
  String? _cityText;
  String? _searchText;

  late Map<String, String> _datas;

  @override
  void initState() {
    super.initState();
    _textEditingController = TextEditingController();
    _datas = provinceDatas;
    if (widget.arguments != null && widget.arguments!.containsKey('code')) {
      _code = widget.arguments!['code'];
      if (_code != null) {
        String _key = _code!.replaceRange(_code!.length - 2, _code!.length, "00");
        Map<String, String>? _maps = cityDatas[_key];
        if (_maps != null) {
          _cityText = _maps[_code];
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "城市选择器",
          style: TextStyle(
            fontSize: 18,
            color: Colors.black,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          onPressed: () {
            if (_layer == 0) {
              Navigator.pop(context);
            } else {
              if (_layer == 1) {
                // 市 -> 省
                _datas = provinceDatas;
              } else if (_layer == 2) {
                // 区 -> 市
                String _key = _code!.replaceRange(_code!.length - 4, _code!.length, "0000");
                _datas = cityDatas[_key] as Map<String, String>;
              }
              _layer -= 1;
              setState(() {});
            }
          },
          icon: Icon(
            Icons.arrow_back_ios,
            color: Colors.black,
            size: 18,
          ),
        ),
      ),
      backgroundColor: Colors.white,
      body: GestureDetector(
        onTap: () {
          print("移除关键盘");
          _focusNode.unfocus();
        },
        behavior: HitTestBehavior.translucent,
        child: Column(
          children: [
            // 搜索框
            Container(
              margin: EdgeInsets.only(left: 12, right: 12, top: 11, bottom: 6),
              padding: EdgeInsets.only(left: 18, right: 18),
              height: 40,
              decoration: BoxDecoration(
                color: Color(0xFFF2F2F2),
                borderRadius: BorderRadius.circular(40 / 2),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: Image.asset('assets/images/1.png'),
                  ),
                  SizedBox(
                    width: 6,
                  ),
                  Expanded(
                    child: TextField(
                      autofocus: false,
                      textAlign: TextAlign.left,
                      keyboardType: TextInputType.text,
                      obscureText: false,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF595959),
                      ),
                      controller: _textEditingController,
                      focusNode: _focusNode,
                      onChanged: (val) {
                        print(">>>> onChanged:$val");
                      },
                      maxLines: 1,
                      maxLength: 50,
                      textInputAction: TextInputAction.search,
                      onSubmitted: (val) {
                        print(">>>> onSubmitted:$val");
                        searchEvents(val);
                      },
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: '搜索城市',
                        counterText: '',
                        hintStyle: TextStyle(color: Color(0xFFCCCCCC)),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 当前定位
            Column(
              children: [
                Container(
                  height: 70,
                  padding: EdgeInsets.only(left: 15, right: 15),
                  alignment: Alignment.centerLeft,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '当前定位',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF666666),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: Image.asset('assets/images/2.png'),
                          ),
                          const SizedBox(
                            width: 6,
                          ),
                          Expanded(
                            child: Text(
                              '当前定位',
                              style: TextStyle(
                                fontSize: 14,
                                color: Business.mainColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          /*
                          InkWell(
                            onTap: () {
                              print(">>>> 重新定位");
                            },
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width: 12,
                                  height: 12,
                                  child: Image.asset('assets/images/3.png'),
                                ),
                                SizedBox(
                                  width: 6,
                                ),
                                Text(
                                  '重新定位',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF666666),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                           */
                        ],
                      )
                    ],
                  ),
                ),
                Divider(
                  indent: 15,
                  endIndent: 15,
                  color: Color(0xFFE5E5E5),
                  height: 1,
                ),
              ],
            ),
            // 数据列表
            Expanded(
                child: ListView.builder(
                    itemCount: _datas.values.toList().length,
                    itemBuilder: _itemBuilder)),
          ],
        ),
      ),
    );
  }

  // 列表元素
  Widget _itemBuilder(BuildContext context, int index) {
    String _value = _datas.values.toList()[index];
    String _key = _datas.keys.toList()[index];

    return InkWell(
      onTap: () {
        _code = _key;
        if (_code != null) {
          if (_searchText != null && _searchText!.isNotEmpty) {
            if (_code!.endsWith("0000")) {
              _layer = 1;
            } else if (_code!.endsWith("00")) {
              _layer = 2;
            } else {
              _layer = 3;
            }
          } else {
            _layer += 1;
          }
          if (_layer < 3) {
            if (_layer == 1) {
              _datas = cityDatas[_code] as Map<String, String>;
            } else if (_layer == 2) {
              _datas = cityDatas[_code] as Map<String, String>;
            }
            setState(() {});
          } else {
            Navigator.pop(context, {_key: _value});
          }
        }
      },
      child: Column(
        children: [
          Container(
            height: 58,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(left: 15, right: 15),
            child: Text(
              _value,
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF212121),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Divider(
            indent: 15,
            endIndent: 15,
            color: Color(0xFFE5E5E5),
            height: 1,
          ),
        ],
      ),
    );
  }

  // 数据检索
  void searchEvents(String text) {
    _searchText = text;
    if (text.isEmpty) {
      _datas = provinceDatas;
      return ;
    }
    // 检索省
    Map<String, String> _ds = {};
    provinceDatas.forEach((key, value) {
      if (value.contains(text)) {
        _ds.addAll({key : value});
      }
    });
    // 检索市区
    cityDatas.forEach((key, value) {
      if (value is String) {
        if (value.contains(text)) {
          _ds.addAll({key : value});
        }
      } else if (value is Map) {
        value.forEach((key, value) {
          if (value.contains(text)) {
            _ds.addAll({key : value});
          }
        });
      }
    });
    _datas = _ds;
    setState(() {});
  }
}
