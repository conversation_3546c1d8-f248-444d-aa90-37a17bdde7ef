import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:rabbit_seat_app/utils/http.dart';

class BarcodeScannerSimple extends StatefulWidget {
  const BarcodeScannerSimple({super.key});

  @override
  State<BarcodeScannerSimple> createState() => _BarcodeScannerSimpleState();
}

class _BarcodeScannerSimpleState extends State<BarcodeScannerSimple> {
  Barcode? _barcode;

  Widget _buildBarcode(Barcode? value) {
    if (value == null) {
      return const Text(
        '请扫码二维码',
        overflow: TextOverflow.fade,
        style: TextStyle(color: Colors.white),
      );
    }

    return Text(
      value.displayValue ?? 'No display value.',
      overflow: TextOverflow.fade,
      style: const TextStyle(color: Colors.white),
    );
  }

  bool isExit = false;
  void _handleBarcode(BarcodeCapture barcodes, BuildContext _context) {
    if (barcodes.barcodes.firstOrNull != null && !isExit) {
      isExit = true;
      print(barcodes.barcodes.firstOrNull?.displayValue);
      EasyLoading.showToast('扫码成功，登录中...');
      Navigator.pop(_context);

      Http.post('/api/token/qrcodeLogin', data: {
        'qrcode': barcodes.barcodes.firstOrNull?.displayValue,
      }).then((value) {
        if (value["success"]) {
          EasyLoading.showToast('扫码登录成功');
        } else {
          EasyLoading.showToast('扫码登录失败');
        }
      }).catchError(() {
        EasyLoading.showToast('扫码登录错误');
      });
    }
    // print(barcodes.barcodes.firstOrNull?.displayValue);
    // if (mounted) {
    //   setState(() {
    //     _barcode = barcodes.barcodes.firstOrNull;
    //   });
    // }
  }

  bool isFlashOn = false;
  final MobileScannerController controller = MobileScannerController(
    torchEnabled: false,
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('扫码登录')),
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          MobileScanner(
            controller: controller,
            onDetect: (BarcodeCapture barcodes) {
              _handleBarcode(barcodes, context);
            },
          ),
          // Align(
          //   alignment: Alignment.bottomCenter,
          //   child: Container(
          //     margin: EdgeInsets.only(bottom: 200),
          //     child: InkWell(
          //       child: Row(
          //         mainAxisAlignment: MainAxisAlignment.center,
          //         children: [
          //           IconButton(
          //             style: ButtonStyle(
          //                 iconColor:
          //                     WidgetStateProperty.all<Color>(Colors.white),
          //                 iconSize: WidgetStateProperty.all(25.sp)),
          //             icon: const Icon(Icons.flash_on),
          //             onPressed: () {},
          //           ),
          //           Container(
          //             child: Text("打开闪光灯", style: TextStyle(fontSize: 25.sp,color: Colors.white),),
          //           )
          //         ],
          //       ),
          //     ),
          //   ),
          // ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              alignment: Alignment.center,
              height: 80.h,
              color: Colors.black.withOpacity(0.4),
              child: InkWell(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ToggleFlashlightButton(controller: controller),
                    // IconButton(
                    //   style: ButtonStyle(
                    //       iconColor:
                    //           WidgetStateProperty.all<Color>(Colors.white),
                    //       iconSize: WidgetStateProperty.all(20.sp)),
                    //   icon: const Icon(Icons.flash_on),
                    //   onPressed: () {},
                    // ),
                    // Container(
                    //   child: Text(
                    //     isFlashOn ? "关闭闪光灯" : "打开闪光灯",
                    //     style: TextStyle(fontSize: 20.sp, color: Colors.white),
                    //   ),
                    // )
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ToggleFlashlightButton extends StatelessWidget {
  ToggleFlashlightButton({required this.controller, super.key});

  final MobileScannerController controller;
  bool isFlahOpen = false;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: controller,
      builder: (context, state, child) {
        if (!state.isInitialized || !state.isRunning) {
          return const SizedBox.shrink();
        }
        var text = "打开闪光灯";
        var iconSize = 20.sp;
        var icon = null;

        switch (state.torchState) {
          case TorchState.auto:
            icon = IconButton(
              color: Colors.white,
              iconSize: iconSize,
              icon: const Icon(Icons.flash_auto),
              onPressed: () async {
                await controller.toggleTorch();
              },
            );
            break;
          case TorchState.off:
            icon = IconButton(
              color: Colors.white,
              iconSize: iconSize,
              icon: const Icon(Icons.flash_off),
              onPressed: () {},
            );
            text = "打开闪光灯";
            break;
          case TorchState.on:
            icon = IconButton(
              color: Colors.white,
              iconSize: iconSize,
              icon: const Icon(Icons.flash_on),
              onPressed: () {},
            );
            text = "关闭闪光灯";
            break;
          case TorchState.unavailable:
            icon = SizedBox.square(
              dimension: 48.0,
              child: Icon(
                Icons.no_flash,
                size: iconSize,
                color: Colors.grey,
              ),
            );
            break;
        }

        return InkWell(
          onTap: () async {
            await controller.toggleTorch();
          },
          child: Container(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Icon(
                  state.torchState == TorchState.off
                      ? Icons.flash_on
                      : Icons.flash_off,
                  size: iconSize,
                  color: Colors.white,
                ),
                const SizedBox(width: 5.0),
                Text(
                  text,
                  style: const TextStyle(color: Colors.white),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
