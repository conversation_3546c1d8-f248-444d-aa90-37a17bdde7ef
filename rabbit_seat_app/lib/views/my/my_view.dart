import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:media_asset_utils/media_asset_utils.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/models/profile.dart';
import 'package:rabbit_seat_app/provider/device_provider.dart';
import 'package:rabbit_seat_app/provider/user_provider.dart';
import 'package:rabbit_seat_app/utils/rx.dart';
import 'package:rabbit_seat_app/utils/utils.dart';
import 'package:rabbit_seat_app/views/device/dev_list_view.dart';
import 'package:rabbit_seat_app/views/my/BarcodeScannerSimple.dart';
import '../../models/user/user_model.dart';
import '../../services/help_service.dart';
import '../../services/notice_service.dart';
import '../../services/user_service.dart';
import '../../utils/global.dart';
import '../../utils/http.dart';
import '../../widgets/buttom/ripple_button.dart';
import '../../widgets/dialog/dialog.dart';
import '../../widgets/refresh/index.dart';

/**
 * 我的中心
 */
class MyView extends StatefulWidget {
  const MyView({Key? key}) : super(key: key);

  @override
  State<MyView> createState() => _MyViewState();
}

class _MyViewState extends State<MyView> with AutomaticKeepAliveClientMixin {
  // StreamSubscription? subEvents;

  // /// 图片选择器
  // ImagePicker _imagePicker = ImagePicker();
  //
  // /// 头像文件路径
  // XFile? _portraitFile;
  //
  // /// 头像上传id
  // String? _portraitUploadId;

  UserModel? userInfo;

  // 联系人
  String _contacts = '';

  // 系统消息数量
  int _noticeCount = 0;

  // UserModel user = UserModel();
  // bool isInit = false;

  // 需要在页面销毁时调用Stream的cancel方法取消监听
  StreamSubscription? _nativeSubscription;

  @override
  void initState() {
    super.initState();

    // Future.delayed(Duration(milliseconds: 100), () {
    //   _controller.callRefresh();
    // });

    // if (Global.profile.user == null) {
    /// 加载用户信息
    // _loadDatas();
    // }

    // subEvents = eventBus.on<getUserEvent>().listen((event) {
    //   init();
    // });

    // KitSignal().link('user-refresh', 'my', (arg) {
    //     _loadDatas();
    // });

    // UserProvider userProvider = context.read<UserProvider>();
    // userProvider.addListener(() {
    //   setState(() {
    //     _contacts = "已添加${userProvider.user?.emergencyContacts!.length}人";
    //   });
    // });

    // // 原生传回数据
    // _nativeSubscription = EventChannel('com.rabbit.seat.flutterEvent')
    //     .receiveBroadcastStream()
    //     .listen(receiveRemoteMsg, onError: (error) {
    //   print(">>> _main _view _Error:$error");
    // });
  }

  @override
  void dispose() {
    super.dispose();
    // subEvents?.cancel();

    // KitSignal().off('user-refresh', 'my');

    if (_nativeSubscription != null) {
      _nativeSubscription?.cancel();
      _nativeSubscription = null;
    }
  }

  /// 接收到远程通知数据
  void receiveRemoteMsg(arg) {
    print(">>> MEventChannel");

    print(">>> MEventChannel    --- home_view");
    if (arg != null && arg is String) {
      if (arg == '离车报警') {
        // 返回首页
        // controller.jumpToPage(0);
      } else if (arg == '消息提醒') {
        // 跳转到消息页面
        // controller.jumpToPage(2);
        // 刷新数据
        Future.delayed(Duration(milliseconds: 100), () {
          _controller.callRefresh();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于375dp * 812dp的屏幕

    /// 从全局中获取
    userInfo = context.watch<UserProvider>().user;

    return Scaffold(
      body: _buildBody(context),
    );
  }

  /// 刷新控制
  EasyRefreshController _controller = EasyRefreshController();

  Widget _buildBody(BuildContext context) {
    // int _count = context.watch<DeviceProvider>().mdevices.length;
    // DeviceModel? _model = context.watch<DeviceProvider>().displayDevice;

    // 加载显示设备
    int _count = context.watch<DeviceProvider>().deviceCount;
    String? minExpiredTime = context.watch<DeviceProvider>().minExpiredTime;
    String? _expireTime = context.watch<DeviceProvider>().expireTime;
    // 设备
    String _devices = _count > 0 ? '$_count个设备' : ''; // N个设备
    // 过期时间
    String _expTime = ''; // "2021-05-01到期"
    if (minExpiredTime != null) {
      if (minExpiredTime.length > 10) {
        _expTime = minExpiredTime.substring(0, 10) + ' 到期';
      } else {
        _expTime = minExpiredTime + ' 到期';
      }
    }
    // if (_model != null) {
    //   final _date =
    //       DateTime.fromMillisecondsSinceEpoch(_model.activeTime.toInt());
    //   _expTime = formatDate(_date, [yyyy, '-', mm, '-', dd]) + ' 到期';
    // }

    // if (userInfo != null && userInfo!.emergencyContacts != null && userInfo!.emergencyContacts!.length > 0) {
    //   _contacts = "已添加${userInfo!.emergencyContacts!.length}人";
    // }
    String? _population = context
        .watch<UserProvider>()
        .user
        ?.emergencyContacts
        ?.length
        .toString();
    if (_population != null && _population.isNotEmpty && _population != '0') {
      _contacts = "已添加${_population}人";
    } else {
      _contacts = "已添加${0}人";
    }

    List<Widget> _deviceWidget = [];
    if (_devices != '') {
      _deviceWidget.add(Text(
        _devices,
        style: TextStyle(fontSize: 12.sp, color: Color(0xFF666666)),
      ));
    }
    if (_expTime != '') {
      if (_deviceWidget.length > 0) {
        _deviceWidget.add(Container(
          margin: EdgeInsets.only(left: 5.5.w, right: 5.5.w),
          height: 12.h,
          width: 1,
          color: Color(0xFF666666),
        ));
      }
      _deviceWidget.add(Text(
        _expTime,
        style: TextStyle(fontSize: 12.sp, color: Business.mainColor),
      ));
    }

    return Stack(
      fit: StackFit.expand,
      children: [
        // 背景
        Positioned(
          left: 0,
          top: 0,
          right: 0,
          child: Image(
              image: const AssetImage('assets/images/my/5.png'), width: 1.sw),
        ),
        // 主体
        Positioned.fill(
            child: Container(
          color: Colors.transparent,
          padding: EdgeInsets.only(
              left: 15.w,
              top: MediaQuery.of(context).padding.top + 10.h,
              right: 15.w,
              bottom: MediaQuery.of(context).padding.bottom),
          child: EasyRefresh.custom(
            firstRefresh: true,
            controller: _controller,
            header: CustomRefreshHeader(),
            slivers: [
              SliverList(
                delegate: SliverChildListDelegate([
                  MyHeaderWidget(),
                  SizedBox(
                    height: 10.h,
                  ),
                  MenuBoxWidget(
                    child: MenuItemWidget(
                      text: "我的设备",
                      asset: "assets/images/my/3.png",
                      subWidget: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: _deviceWidget,
                      ),
                      onTap: () {
                        // Navigator.pushNamed(context, 'deviceList');
                        // Navigator.push(context,
                        //     MaterialPageRoute(builder: (ctx) => DevicesView()));
                        Navigator.push(context,
                            MaterialPageRoute(builder: (ctx) => DevListPage()));
                      },
                    ),
                  ),
                  MenuBoxWidget(
                    child: MenuItemWidget(
                      text: "紧急联系人",
                      asset: "assets/images/my/13.png",
                      subWidget: Text(
                        _contacts,
                        style: TextStyle(
                            fontSize: 12.sp, color: Color(0xFF666666)),
                      ),
                      onTap: () {
                        Navigator.pushNamed(context, '/contacts');
                      },
                    ),
                  ),
                  MenuBoxWidget(
                    child: MenuItemWidget(
                      text: "我的发布/互动",
                      asset: "assets/images/my/11.png",
                      isHint: userInfo!.hasNewAppComment != null &&
                              userInfo!.hasNewAppComment == true
                          ? true
                          : false,
                      onTap: () {
                        Navigator.pushNamed(context, '/interact');
                      },
                    ),
                  ),
                  MenuBoxWidget(
                    child: Column(
                      children: [
                        MenuItemWidget(
                          text: "扫一扫",
                          asset: "assets/images/my/qrscan.png",
                          onTap: () {
                            Navigator.of(context).pushNamed('/barcodeScann');
                          },
                        ),
                        _divider(),
                        MenuItemWidget(
                          text: "我的卡券",
                          asset: "assets/images/my/7.png",
                          onTap: () {
                            Navigator.pushNamed(context, '/ticket');
                          },
                        ),
                        _divider(),
                        MenuItemWidget(
                          isHint: _noticeCount > 0 ? true : false,
                          text: "系统消息",
                          asset: "assets/images/my/6.png",
                          subWidget: _noticeCount > 0
                              ? Text(
                                  '有新消息',
                                  style: TextStyle(
                                      fontSize: 12.sp,
                                      color: Business.mainColor),
                                )
                              : null,
                          onTap: () {
                            // Navigator.push(context, MaterialPageRoute(builder: (ctx) => SystemMsgPage()));
                            setState(() {
                              _noticeCount = 0;
                            });
                            Rx.clearMyItemBadgeSubject.add(0);
                            Navigator.pushNamed(context, '/sys-msg');
                          },
                        ),
                        _divider(),
                        MenuItemWidget(
                          text: "我的设置",
                          asset: "assets/images/my/8.png",
                          onTap: () {
                            Navigator.pushNamed(context, '/setting');
                          },
                        ),
                        _divider(),
                        MenuItemWidget(
                          text: "技术支持",
                          asset: "assets/images/my/9.png",
                          onTap: () {
                            Navigator.pushNamed(context, '/support');
                          },
                        ),
                      ],
                    ),
                  ),
                  MenuBoxWidget(
                    child: MenuItemWidget(
                      text: "退出登录",
                      asset: "assets/images/my/10.png",
                      onTap: () {
                        DefDialog.showDialog1(
                            context: context,
                            message: "您确定退出登录吗？",
                            confirm: () {
                              print("确定退出登录");

                              // 清除数据
                              context.read<DeviceProvider>().clear();

                              Global.profile.token = null;
                              Global.profile = Profile();
                              Global.saveProfile();
                              Navigator.pushNamedAndRemoveUntil(
                                  context, '/login-code', (route) => false);
                              // Navigator.pushNamedAndRemoveUntil(
                              //     context, '/', (route) => false);
                            },
                            cancel: () {});
                      },
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.fromLTRB(0, 38.h, 0, 0),
                    child: Image.asset('assets/images/<EMAIL>',
                        height: 12.h),
                  ),
                  SizedBox(
                    height: 50,
                  ),
                ]),
              ),
            ],
            shrinkWrap: true,
            onRefresh: () async {
              print("refresh");
              // 加载系统消息
              _loadNoticeDatas();
              // 刷新用户数据
              context.read<UserProvider>().loadUserDatas(completed: (value) {
                if (value) _controller.finishRefresh();
              });
            },
          ),
        )),
      ],
    );
  }

  /// 分割线
  Widget _divider() {
    return Divider(
      indent: 24.w,
      endIndent: 24.w,
      height: 1,
      color: Color(0xFFEEEEEE),
    );
  }

  /// 加载用户信息
  /// 加载联系人
  /// 获取用户信息
  ///
  /*
  void _loadDatas() async {
    print("加载数据");
    try {
      EasyLoading.show();
      String id = Global.profile.user!.id!;
      final data = await Http.get('/api/appuser/user/$id');
      EasyLoading.dismiss();
      final result = ResultModel<UserModel>.fromJson(data, (json) => UserModel.fromJson(json as Map<String, dynamic>));
      if (result.code == 200) {
        Global.profile.user = result.data;
        context.read<UserProvider>().user = result.data;
        // Global.saveProfile();
        // userInfo = result.data as UserModel;
        // 刷新页面
        setState(() {});
      } else {
        EasyLoading.showToast(result.message);
      }
    } on MyError catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.message ?? '网络请求失败！');
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showToast(e.toString());
    }
  }
  */

  // 加载系统消息数据
  void _loadNoticeDatas() async {
    NoticeService.loadDatas(
      pageIndex: 0,
      success: (datas) {
        if (datas != null && datas.content != null) {
          if (mounted)
            setState(() {
              _noticeCount = datas.content!.length;
            });
        }
      },
      failure: (msg) {
        print(">>> msg:$msg");
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}

/// --------------------------------------------------------------------------------
///
/// 我的页面头部部件
class MyHeaderWidget extends StatefulWidget {
  // UserModel? data;
  // MyHeaderWidget({Key? key, this.data}) : super(key: key);

  MyHeaderWidget({Key? key}) : super(key: key);

  @override
  State<MyHeaderWidget> createState() => _MyHeaderWidgetState();
}

class _MyHeaderWidgetState extends State<MyHeaderWidget> {
  // UserModel user = UserModel();

  UserModel? _user;

  /// 图片选择器
  ImagePicker _imagePicker = ImagePicker();

  /// 头像文件路径
  XFile? _portraitFile;

  /// 头像上传id
  String? _portraitUploadId;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    //设置尺寸（填写设计中设备的屏幕尺寸）如果设计基于375dp * 812dp的屏幕

    _user = context.watch<UserProvider>().user;

    /// 默认头像
    Widget? _portraitWidget =
        Image(image: AssetImage('assets/images/logo.png'), fit: BoxFit.cover);

    /// 从网络中加载头像
    if (_user != null && _user!.avatar != null && _user!.avatar!.isNotEmpty) {
      String _url = _user!.avatar!.startsWith('http://') ||
              _user!.avatar!.startsWith('https://')
          ? _user!.avatar!
          : kReleaseBaseUrl + _user!.avatar!;
      _portraitWidget = Image.network(_url, fit: BoxFit.cover);
    }

    /// 从路径中加载
    else if (_portraitFile != null) {
      _portraitWidget =
          Image.file(File(_portraitFile!.path), fit: BoxFit.cover);
    }

    // 用户昵称
    String _nickName = '两只兔子';
    if (_user != null) {
      if (_user!.nickName != null && _user!.nickName!.isNotEmpty) {
        _nickName = _user!.nickName!;
      } else {
        if (_user!.id != null &&
            _user!.id!.isNotEmpty &&
            _user!.id!.length >= 6) {
          _nickName = '两只兔子' + _user!.id!.substring((_user!.id!.length - 6));
        }
      }
    }

    return Container(
      height: 116.h,
      padding: EdgeInsets.only(left: 10.w, right: 3.w),
      child: Row(
        children: [
          // 头像
          GestureDetector(
            onTap: () {
              DefDialog.showPixBottomSheet(context, (type) {
                if (type == 1) {
                  _takePhotos();
                } else if (type == 2) {
                  _galleryPhotos();
                }
              }, false);
            },
            child: ClipOval(
              child: SizedBox(
                width: 65.w,
                height: 65.w,
                child: _portraitWidget,
              ),
            ),
          ),
          // 账户信息
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 10.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _nickName,
                    style: TextStyle(
                        fontSize: 21.sp,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2A2A2A)),
                  ),
                  SizedBox(
                    height: 8.5.h,
                  ),
                  Text(
                    _user?.telephone
                            ?.replaceFirst(RegExp(r'\d{4}'), '****', 3) ??
                        '',
                    style: TextStyle(
                        fontSize: 11.sp,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF7E7E7E)),
                  ),
                ],
              ),
            ),
          ),
          GestureDetector(
            onTap: () async {
              // await UserService.getUser(Global.profile.user?.id);
              Navigator.pushNamed(context, '/user-info');
            },
            child: Container(
              width: 70.w,
              height: 28.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(28 / 2)),
                border: Border.all(color: Color(0xFFD2D2D2), width: 1),
              ),
              alignment: Alignment.center,
              child: Text(
                "编辑资料",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Color(0xFF666666),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  /// 拍照
  void _takePhotos() async {
    Util.checkCameraPermissionStatus(context, '头像', () async {
      XFile? _imgFile = await _imagePicker.pickImage(
          source: ImageSource.camera, maxWidth: 1000);
      if (_imgFile != null) {
        // 压缩图片
        final res = await MediaAssetUtils.compressImage(File(_imgFile.path));
        if (res != null) {
          // 显示图片
          _portraitFile = XFile(res.path);
          // 上传图片
          _uploadUserPortrait();
        }
      }
    });
  }

  /// 相册获取图片
  void _galleryPhotos() async {
    Util.checkStoragePermissionStatus(context, '头像文件选取', () async {
      XFile? _imgFile = await _imagePicker.pickImage(
          source: ImageSource.gallery, maxWidth: 1000);
      if (_imgFile != null) {
        // 压缩图片
        final res = await MediaAssetUtils.compressImage(File(_imgFile.path));
        if (res != null) {
          // 显示图片
          _portraitFile = XFile(res.path);
          // 上传图片
          _uploadUserPortrait();
        }
      }
    });
  }

  /// 图片裁剪
  // Future<CroppedFile?> _cropImage(XFile xFile) async {
  //   CroppedFile? _file = await ImageCropper().cropImage(
  //     sourcePath: xFile.path,
  //     cropStyle: CropStyle.circle,
  //     aspectRatioPresets: [
  //       CropAspectRatioPreset.ratio3x2,
  //       CropAspectRatioPreset.original,
  //       CropAspectRatioPreset.ratio4x3,
  //       CropAspectRatioPreset.ratio16x9,
  //     ],
  //     uiSettings: [
  //       AndroidUiSettings(
  //         toolbarTitle: "编辑图片",
  //         toolbarColor: Colors.white,
  //         toolbarWidgetColor: Colors.grey,
  //         initAspectRatio: CropAspectRatioPreset.original,
  //         lockAspectRatio: false,
  //       ),
  //       IOSUiSettings(minimumAspectRatio: 1.0),
  //     ],
  //   );
  //   return _file;
  // }

  /// 上传用户头像
  void _uploadUserPortrait() async {
    EasyLoading.show();
    // 上传图片
    final _fleRes = await HelpService.uploadFile(_portraitFile!.path);
    if (_fleRes == null) {
      EasyLoading.dismiss();
      EasyLoading.showToast("图片上传失败!");
      return;
    }
    UserService.updateUserInfo(
        uid: Global.profile.user!.id!,
        avatar: _fleRes.fileUrl,
        success: (data) {
          EasyLoading.dismiss();
          EasyLoading.showToast("上传成功！");
          // widget.data!.avatar = _fleRes.fileUrl;
          context.read<UserProvider>().setAvatar(_fleRes.fileUrl!);
          if (mounted) setState(() {});
        },
        failure: (msg) {
          EasyLoading.dismiss();
          EasyLoading.showToast(msg);
        });
  }

  /// 设备拍摄功能权限校验
  Future<bool> _checkCameraPermissionStatus(BuildContext context) async {
    // 相机权限
    PermissionStatus _cameraStatus = await Permission.camera.request();
    if (_cameraStatus != PermissionStatus.granted) {
      DefDialog.showDialog1(
          context: context,
          title: '相机权限未开启',
          message: Platform.isIOS
              ? '请在iPhone的"设置->两字兔子"选项中，允许两只兔子访问你的相机权限。'
              : '请在手机的"设置->应用->权限管理"选项中，允许两只兔子访问你的相机权限,用于头像照片',
          cancelText: '知道了',
          confirmText: '前往设置',
          cancel: () {},
          confirm: () async {
            await AppSettings.openAppSettings();
          });
      return false;
    }

    // // 麦克风权限
    // PermissionStatus _micStatus = await Permission.microphone.request();
    // if (_micStatus != PermissionStatus.granted) {
    //   DefDialog.showDialog1(
    //       context: context,
    //       title: '麦克风权限未开启',
    //       message: Platform.isIOS
    //           ? '请在iPhone的"设置->两字兔子"选项中，允许两只兔子访问你的麦克风权限。'
    //           : '请在手机的"设置->应用->权限管理"选项中，允许两只兔子访问你的麦克风权限。',
    //       cancelText: '知道了',
    //       confirmText: '前往设置',
    //       cancel: () {},
    //       confirm: () async {
    //         await AppSettings.openAppSettings();
    //       });
    //   return false;
    // }

    // 存储权限
    if (Platform.isAndroid) {
      PermissionStatus _storageStatus = await Permission.storage.request();
      if (_storageStatus != PermissionStatus.granted) {
        DefDialog.showDialog1(
            context: context,
            title: '存储权限未开启',
            message: '请在手机的"设置->应用->权限管理"选项中，允许两只兔子访问你的存储权限，获取图片用于头像',
            cancelText: '知道了',
            confirmText: '前往设置',
            cancel: () {},
            confirm: () async {
              await AppSettings.openAppSettings();
            });
        return false;
      }
    }

    return true;
  }
}

/// --------------------------------------------------------------------------------
///
/// 自定义菜单按钮容器
class MenuBoxWidget extends StatelessWidget {
  final Widget child;

  MenuBoxWidget({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12.w)),
      ),
      child: child,
    );
  }
}

/// --------------------------------------------------------------------------------
///
/// 自定义菜单按钮元素
class MenuItemWidget extends StatelessWidget {
  // 图标
  final String asset;

  // 标题
  final String text;

  // 子项
  final Widget? subWidget;

  // 圆角
  double radius = 0;

  // 点击
  Function()? onTap;

  // 显示提醒
  bool isHint = false;

  MenuItemWidget({
    Key? key,
    required this.asset,
    required this.text,
    this.subWidget,
    this.radius = 0,
    this.onTap,
    this.isHint = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (radius == 0) radius = 12.w;

    return RippleButton(
      child: Container(
        height: 64,
        padding: EdgeInsets.only(left: 20.w, right: 25.w),
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            // 图标
            Stack(
              children: [
                Padding(
                  padding:
                      EdgeInsets.only(left: 5, top: 5, bottom: 5, right: 0),
                  child: SizedBox(
                    width: 22.w,
                    height: 22.w,
                    child: Image.asset(
                      asset,
                      fit: BoxFit.fill,
                    ),
                  ),
                ),
                isHint
                    ? Positioned(
                        left: 0,
                        top: 0,
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(8 / 2)),
                          width: 8,
                          height: 8,
                        ),
                      )
                    : SizedBox(),
              ],
            ),

            // 标题
            Padding(
              padding: EdgeInsets.only(left: 18.w, right: 15.w),
              child: Text(
                text,
                maxLines: 1,
                style: TextStyle(
                  fontSize: 15.sp,
                  color: Color(0xFF2A2A2A),
                ),
              ),
            ),
            // 子项
            Expanded(
                child: Container(
              alignment: Alignment.centerRight,
              child: subWidget,
            )),
            // 角标
            Padding(
              padding: EdgeInsets.only(left: 6.5.w),
              child: SizedBox(
                width: 8.w,
                height: 13.h,
                child: Image(
                  image: AssetImage('assets/images/my/1.png'),
                  fit: BoxFit.fill,
                ),
              ),
            ),
          ],
        ),
      ),
      color: Colors.white,
      radius: radius,
      onTap: onTap,
    );

    // return Material(
    //   color: Colors.transparent,
    //   // INK可以实现装饰容器
    //   child: Ink(
    //     decoration: BoxDecoration(
    //       color: Colors.white,
    //       borderRadius: BorderRadius.all(Radius.circular(radius)),
    //     ),
    //     child: InkWell(
    //       borderRadius: BorderRadius.all(Radius.circular(radius)),
    //       onTap: onTap,
    //       child: Container(
    //         height: 64,
    //         padding: EdgeInsets.only(left: 25.w, right: 25.w),
    //         alignment: Alignment.centerLeft,
    //         child: Row(
    //           children: [
    //             // 图标
    //             SizedBox(
    //               width: 22.w,
    //               height: 22.w,
    //               child: Image.asset(
    //                 asset,
    //                 fit: BoxFit.fill,
    //               ),
    //             ),
    //             // 标题
    //             Padding(
    //               padding: EdgeInsets.only(left: 18.w, right: 15.w),
    //               child: Text(
    //                 text,
    //                 maxLines: 1,
    //                 style: TextStyle(
    //                   fontSize: 15.sp,
    //                   color: Color(0xFF2A2A2A),
    //                 ),
    //               ),
    //             ),
    //             // 子项
    //             Expanded(
    //                 child: Container(
    //                   alignment: Alignment.centerRight,
    //                   child: subWidget,
    //                 )),
    //             // 角标
    //             Padding(
    //               padding: EdgeInsets.only(left: 6.5.w),
    //               child: SizedBox(
    //                 width: 8.w,
    //                 height: 13.h,
    //                 child: Image(
    //                   image: AssetImage('assets/images/my/1.png'),
    //                   fit: BoxFit.fill,
    //                 ),
    //               ),
    //             ),
    //           ],
    //         ),
    //       ),
    //     ),
    //   ),
    // );
  }
}
