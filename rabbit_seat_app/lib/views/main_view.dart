import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rabbit_seat_app/business.dart';
import 'package:rabbit_seat_app/utils/rx.dart';
import 'package:rabbit_seat_app/utils/utils.dart';
import 'package:rabbit_seat_app/views/circle/circle_view.dart';
import 'package:rabbit_seat_app/views/home/<USER>';
import 'package:rabbit_seat_app/views/my/my_view.dart';
import 'package:rabbit_seat_app/widgets/loading.dart';

import '../utils/version.dart';
import 'home/hom_view.dart';
import 'package:tuya/tuya.dart';

/**
 * 主页
 */
class MainView extends StatefulWidget {
  const MainView({Key? key}) : super(key: key);

  @override
  State<MainView> createState() => _MainViewState();
}

class _MainViewState extends State<MainView>
    with SingleTickerProviderStateMixin {
  // 页面索引
  int activeIndex = 0;

  // 控制器
  late PageController controller;

  // 网络监控
  late StreamSubscription subscription;

  // 需要在页面销毁时调用Stream的cancel方法取消监听
  static StreamSubscription? _nativeSubscription;

  // 有新的消息
  bool _hasNews = false;

  late StreamSubscription _showBadgeSubscription;
  late StreamSubscription _hideBadgeSubscription;

  bool isLoading = false;

  @override
  void initState() {
    isLoading = !Util.hasInitLoading;
    super.initState();

    Future.delayed(const Duration(milliseconds: 2000), () {
      if (isLoading) {
        Util.hasInitLoading = true;
        setState(() {
          isLoading = false;
        });
      }
    });

    Util.initOtherSdk();
    print(">>>> main view init ------**** ----------- ***** -------------");
    controller = PageController(initialPage: activeIndex);

    subscription = Connectivity().onConnectivityChanged.listen((event) {
      print(">>> Network state:$event");
      if (event == ConnectivityResult.none) {
        EasyLoading.showToast('当前网络不可用！');
      }
    });

    /// 消息订阅
    // 原生传回数据
    _nativeSubscription = const EventChannel('com.rabbit.seat.flutterEvent')
        .receiveBroadcastStream()
        .listen(receiveRemoteMsg, onError: (error) {
      print(">>> _main _view _Error:$error");
    });

    Future.delayed(const Duration(milliseconds: 2000), () {
      Tuya.registerPushDevice();
    });

    _hideBadgeSubscription = Rx.clearMyItemBadgeSubject.listen((value) {
      setState(() {
        _hasNews = false;
      });
    });

    _showBadgeSubscription = Rx.sendMyItemBadgeSubject.listen((value) {
      setState(() {
        _hasNews = true;
      });
    });
  }

  @override
  void dispose() {
    controller.dispose();

    subscription.cancel();

    if (_nativeSubscription != null) {
      _nativeSubscription?.cancel();
      _nativeSubscription = null;
    }

    _hideBadgeSubscription.cancel();
    _showBadgeSubscription.cancel();

    super.dispose();
  }

  /// 接收到远程通知数据
  void receiveRemoteMsg(arg) {
    print("receiveRemoteMsg");
    // EasyLoading.showToast(arg);
    if (arg != null && arg is String) {
      if (arg == '离车报警') {
        // 返回首页
        controller.jumpToPage(0);
      } else if (arg == '消息提醒') {
        setState(() {
          activeIndex = 2;
          controller.jumpToPage(2);
          _hasNews = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const LoadingPage();
    }
    // 版本检测
    AppVersion().checkAndroidVersion(context);
    AppVersion().checkIOSVersion(context);

    return Scaffold(
      body: PageView(
        physics: NeverScrollableScrollPhysics(),
        controller: controller,
        // children: [HomeView(), CircleView(), MyView()],
        children: [IndexView(), CircleView(), MyView()],
      ),
      bottomNavigationBar: _navigationBar(context),
    );
  }

  /**
   * 导航控制
   */
  Material _navigationBar(context) {
    return Material(
      color: Colors.white,
      child: BottomNavigationBar(
        /***
         * type -- 样式：点击是的样式
         *    fixed -- 固定样式，可以有多个按钮
         *    shifting -- 动态样式
         */
        type: BottomNavigationBarType.fixed,
        // 当前选中索引
        currentIndex: activeIndex,
        // 当前选中颜色
        // fixedColor: Colors.green,
        // 选中颜色，不能与"fixedColor"同时存在
        selectedItemColor: Business.mainColor,
        // 选中图标样式
        selectedIconTheme: IconThemeData(color: Business.mainColor),
        // 默认文本颜色，
        unselectedItemColor: Color(0xFF494949),
        // 默认图标样式
        unselectedIconTheme: IconThemeData(color: Color(0xFF494949)),
        // 在"type"样式为"fixed"的时候生效
        backgroundColor: Colors.white,
        // 选中字体大小
        selectedFontSize: 12,
        // 默认字体大小
        unselectedFontSize: 12,
        // 图标大小
        iconSize: 25,
        // 没项点击事件
        onTap: (int index) {
          print("index:$index");
          if (activeIndex == index) Rx.circleRefreshSubject.add(-1);
          setState(() {
            activeIndex = index;
            controller.jumpToPage(index);
            if (index != 2) {
              setState(() {
                _hasNews = false;
              });
            }
          });
        },
        landscapeLayout: BottomNavigationBarLandscapeLayout.linear,
        items: [
          BottomNavigationBarItem(
            icon: Image.asset(
              'assets/images/home/<USER>',
              width: 25,
              height: 25,
            ),
            activeIcon: Image.asset(
              'assets/images/home/<USER>',
              width: 25,
              height: 25,
            ),
            label: "主页",
          ),
          BottomNavigationBarItem(
            icon: Image.asset(
              'assets/images/home/<USER>',
              width: 25,
              height: 25,
            ),
            activeIcon: Image.asset(
              'assets/images/home/<USER>',
              width: 25,
              height: 25,
            ),
            label: "圈子",
          ),
          BottomNavigationBarItem(
            icon: _hasNews == true
                ? Stack(
                    children: [
                      Image.asset(
                        'assets/images/home/<USER>',
                        width: 25,
                        height: 25,
                      ),
                      Container(
                        decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(8 / 2)),
                        width: 8,
                        height: 8,
                      ),
                    ],
                  )
                : Image.asset(
                    'assets/images/home/<USER>',
                    width: 25,
                    height: 25,
                  ),
            activeIcon: _hasNews == true
                ? Stack(
                    children: [
                      Image.asset(
                        'assets/images/home/<USER>',
                        width: 25,
                        height: 25,
                      ),
                      Container(
                        decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(8 / 2)),
                        width: 8,
                        height: 8,
                      ),
                    ],
                  )
                : Image.asset(
                    'assets/images/home/<USER>',
                    width: 25,
                    height: 25,
                  ),
            label: "我的",
          ),
        ],
      ),
    );
  }
}
