
import 'package:flutter/cupertino.dart';

class GenerateTitles {

  GenerateTitles(this.isZh);

  //是否为中文
  bool isZh = false;

  //为了使用方便，我们定义一个静态方法
  static GenerateTitles? of(BuildContext context) {
    return Localizations.of<GenerateTitles>(context, GenerateTitles);
  }

  //Locale相关值，title为应用标题
  String get title {
    return isZh ? "两只兔子" : "Two rabbits";
  }

  String get home {
    return isZh ? "主页" : "Home";
  }

  String get login {
    return isZh ? "登录" : "Login";
  }

  String get my {
    return isZh ? "我的" : "My";
  }

  String get circle {
    return isZh ? "圈子" : "Circles";
  }
}