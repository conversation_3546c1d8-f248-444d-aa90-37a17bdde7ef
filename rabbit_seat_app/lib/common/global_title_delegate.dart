

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:rabbit_seat_app/common/generate_titles.dart';

class GlobalTitleDelegate extends LocalizationsDelegate<GenerateTitles> {
  const GlobalTitleDelegate();

  //是否支持某个Local
  @override
  bool isSupported(Locale locale) => ['en', 'zh'].contains(locale.languageCode);

  // Flutter会调用此类加载相应的Locale资源类
  @override
  Future<GenerateTitles> load(Locale locale) {
    return SynchronousFuture<GenerateTitles>(
        GenerateTitles(locale.languageCode == "zh")
    );
  }

  @override
  bool shouldReload(GlobalTitleDelegate old) => false;
}