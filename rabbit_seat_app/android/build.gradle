buildscript {
    ext {
        kotlin_version = '1.8.0'
        gradle_version = '7.4.2'
    }
    repositories {
//        google()
//        mavenCentral()
        maven{ url 'https://maven.aliyun.com/repository/google' }
        maven{ url 'https://maven.aliyun.com/repository/public' }
    }

    dependencies {
        classpath "com.android.tools.build:gradle:$gradle_version"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        jcenter()
        maven { url 'https://maven-other.tuya.com/repository/maven-releases/' }
        maven { url "https://maven-other.tuya.com/repository/maven-commercial-releases/" }
        maven { url 'https://jitpack.io' }
        google()
        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://central.maven.org/maven2/' }
        maven { url 'https://oss.sonatype.org/content/repositories/snapshots/' }
        maven { url 'https://developer.huawei.com/repo/' }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin("com.android.application") ||
                project.plugins.hasPlugin("com.android.library")) {
            project.android {
                compileSdkVersion 34
                buildToolsVersion "34.0.0"
            }
        }
    }
}

subprojects {
    project.evaluationDependsOn(':app')
}



tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
