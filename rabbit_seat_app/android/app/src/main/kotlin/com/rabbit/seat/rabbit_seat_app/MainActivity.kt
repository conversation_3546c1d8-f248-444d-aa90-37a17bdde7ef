package com.rabbit.seat.rabbit_seat_app

import android.annotation.SuppressLint
import android.app.ActivityManager
import android.app.AlertDialog
import android.app.Notification.VISIBILITY_SECRET
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.net.Uri
import android.os.*
import android.util.Log
import androidx.annotation.NonNull
import androidx.annotation.RequiresApi
import com.rocky.flutter.tuya.tuya.FlutterEventChannel
import com.thingclips.smart.home.sdk.ThingHomeSdk
import com.xiaomi.channel.commonutils.logger.LoggerInterface
import com.xiaomi.mipush.sdk.Logger
import com.xiaomi.mipush.sdk.MiPushClient
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant


class MainActivity : FlutterActivity() {

//    // channel name
//    val channelNames = arrayOf(
//        context.getString(R.string.push_channel_common),
//    )

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?, persistentState: PersistableBundle?) {
        super.onCreate(savedInstanceState, persistentState)
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun createNotificationChannel(
        channelId: String,
        channelName: String,
        importance: Int,
        soundPath: String
    ) {
        val channel = NotificationChannel(channelId, channelName, importance)
        // channel.setSound(Uri.parse(soundPath), Notification.AUDIO_ATTRIBUTES_DEFAULT)
        channel.setVibrationPattern(
            longArrayOf(
                300, 1000, 300, 1000
            )
        )
        channel.canBypassDnd()
        channel.setBypassDnd(true)

        channel.lockscreenVisibility = VISIBILITY_SECRET
        val notificationManager: NotificationManager =
            context.getSystemService(
                Context.NOTIFICATION_SERVICE
            ) as NotificationManager
        notificationManager.createNotificationChannel(channel)

//        val intent = Intent(this, MainActivity::class.java)
//        val pendingIntent: PendingIntent =
//            PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_CANCEL_CURRENT)
//
//        val notification = Notification.Buinit(tilder(this,channelId)//实例化Builder
//        notification.setContentIntent(pendingIntent)
//        notification.setSmallIcon(R.drawable.logo)
//        notificationManager.notify(0, notification.build())
    }

    fun uninstallOldApp(){
        try {
            val oldPackage = "com.rabbit.seat.rabbit_seat_app" // 旧版本应用程序的包名

            val pm = context.packageManager

            if (pm.getInstalledApplications(0).any { it.packageName == oldPackage }) {
                AlertDialog.Builder(this)
                    .setTitle("提示")
                    .setMessage("为了正常使用新版本应用，请先卸载旧版本应用，请放心卸载，该应用不是目前打开的应用")
                    .setPositiveButton("卸载") { _, _ ->
                        val intent = Intent(Intent.ACTION_DELETE)
                        intent.data = Uri.parse("package:$oldPackage")
                        startActivity(intent)
                    }
                    .setNegativeButton("取消", null)
                    .show()
            }
        }catch(e:Exception){
        }

    }
    @RequiresApi(Build.VERSION_CODES.O)
    fun initOtherSdk(): Boolean {


        println("initOtherSdk");

        ThingHomeSdk.init(application);

        //初始化push推送服务
        if (shouldInit()) {
            MiPushClient.registerPush(this, APP_ID, APP_KEY)

            val channelIds: HashMap<String, String> =
                HashMap<String, String>() //define empty hashmap
            channelIds.put("tuya_common", "通用通知")
            channelIds.put("tuya_shortbell", "短铃声通知")
            channelIds.put("tuya_longbell", "长铃声通知")
            channelIds.put("tuya_doorbell", "门铃声通知")
            channelIds.put("tuya_warnbell", "报警通知")
            channelIds.forEach { key, value ->
                run {
                    createNotificationChannel(
                        key,
                        value,
                        NotificationManager.IMPORTANCE_DEFAULT,
                        ""
                    )
                }
            }
        }
        //打开Log
        val newLogger: LoggerInterface = object : LoggerInterface {
            override fun setTag(tag: String) {
                // ignore
            }

            @SuppressLint("LongLogTag")
            override fun log(content: String, t: Throwable) {
                Log.d(TAG, content, t)
            }

            @SuppressLint("LongLogTag")
            override fun log(content: String) {
                Log.d(TAG, content)
            }
        }
        Logger.setLogger(this, newLogger)

        val eventChannel = FlutterEventChannel.getInstance()
        FlutterEventChannel.activity = this
        getFlutterEngine()?.getPlugins()?.add(eventChannel);

//        uninstallOldApp();
        return true;
    }

    ///获取消息逻辑
    private fun getPushRecevieMsg(): String? {
        val preferences: SharedPreferences =
            getSharedPreferences("XiaoMi_Push", MODE_PRIVATE)
        val data: String? = preferences.getString("push", "")
        val editor: SharedPreferences.Editor = preferences.edit()

        //清理数据
        if (data != null) {
            if (!data.isEmpty()) {
                editor.remove("push")
                editor.commit()
            }
        }
        return data
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {

        super.configureFlutterEngine(flutterEngine)

        GeneratedPluginRegistrant.registerWith(flutterEngine)

        val _android: MethodChannel =
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "toAndroid11")
        println("onCreate");
        _android.setMethodCallHandler { call, result ->
            run {
                if (call.method != null) {
                    if ("initOtherSdk".equals(call.method)) {
                        result.success(initOtherSdk())
                    }
                    if (call.method.equals("getReciveData")) {
                        val content: String? = getPushRecevieMsg()
                        result.success(content)
                    }
                } else {
                    result.notImplemented()
                }

            }

        }
        val mEventChannel = EventChannel(flutterEngine.dartExecutor.binaryMessenger, "com.rabbit.seat.flutterEvent")
        mEventChannel.setStreamHandler(object : EventChannel.StreamHandler {
            override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                println("onListen(建立连接)，arguments: $arguments") // true
                if (events != null) {
                    eventSink = events
                }
            }

            override fun onCancel(arguments: Any?) {
                println("onCancel(断开连接)，arguments: $arguments") // true
                eventSink = null
            }
        })

        //存贮 从被杀死的应用点击通知，传进来的参数
        saveIntentData(intent);

    }

    fun saveIntentData(intent: Intent) {
        if (null != intent && intent.getExtras() != null && intent.getStringExtra(extras) != null) {
            var DATA_Push = "XiaoMi_Push"
            var content = intent.getStringExtra(extras);
            // Log.i(TAG, "save receive data from push, data = " + content);
            var preferences = getSharedPreferences(DATA_Push, Context.MODE_PRIVATE);
            var editor = preferences.edit();
            editor.putString(DATA_Push, content);
            editor.commit();

        } else {

            // Log.i(TAG, "存储消息错误");
        }

    }

    private fun shouldInit(): Boolean {
        val am: ActivityManager =
            getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val processInfos: List<ActivityManager.RunningAppProcessInfo> = am.getRunningAppProcesses()
        val mainProcessName: String = getApplicationInfo().processName
        val myPid: Int = android.os.Process.myPid()
        for (info in processInfos) {
            if (info.pid === myPid && mainProcessName == info.processName) {
                return true
            }
        }
        return false
    }
    override fun onNewIntent(@NonNull intent: Intent) {
        super.onNewIntent(intent)
        // Log.i(FlutterActivity.TAG, intent.toString())
        //应用处于前台时，直接通过eventChanel 通知flutter

        //获取intentData
        getIntentData(intent)
    }

    //获取intentData
    private fun getIntentData(intent: Intent?) {
        if (null != intent && intent.extras != null && intent.getStringExtra(extras) != null) {
            val content = intent.getStringExtra(extras)
//            Log.i(
//                FlutterActivity.TAG,
//                "save receive data from push, data = $content"
//            )
            pushMsgEvent(content)
        } else {
//            Log.i(FlutterActivity.TAG, "intent is null")
        }
    }

    //发送消息至flutter
    private fun pushMsgEvent(content: String?) {
        Handler(Looper.getMainLooper()).postDelayed(
            Runnable {
                if (eventSink != null) {
                    eventSink!!.success(content)
                }
            }, 500)
    }

    companion object {
        const val APP_ID = "2882303761520164506"
        const val APP_KEY = "5932016463506"
        const val TAG = "com.rabbit.seat.rabbit_seat_app"
        const val extras="extras"
        var eventSink: EventChannel.EventSink? = null
    }
}
