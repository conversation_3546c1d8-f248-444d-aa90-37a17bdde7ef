package com.rabbit.seat.rabbit_seat_app

import android.annotation.SuppressLint
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import com.rabbit.seat.rabbit_seat_app.MainActivity.Companion.TAG
import com.rocky.flutter.tuya.tuya.TuyaAccount
import com.thingclips.smart.android.common.utils.L
import com.xiaomi.mipush.sdk.MiPushClient
import com.xiaomi.mipush.sdk.MiPushCommandMessage
import com.xiaomi.mipush.sdk.MiPushMessage
import com.xiaomi.mipush.sdk.PushMessageReceiver


class DemoMessageReceiver : PushMessageReceiver() {
    private var mRegId: String? = null
    private val mResultCode: Long = -1
    private val mReason: String? = null
    private val mCommand: String? = null
    private var mMessage: String? = null
    private var mTopic: String? = null
    private var mAlias: String? = null
    private var mUserAccount: String? = null
    private var mStartTime: String? = null
    private var mEndTime: String? = null
    override fun onReceivePassThroughMessage(context: Context?, message: MiPushMessage) {
        mMessage = message.content
        if (!TextUtils.isEmpty(message.topic)) {
            mTopic = message.topic
        } else if (!TextUtils.isEmpty(message.alias)) {
            mAlias = message.alias
        } else if (!TextUtils.isEmpty(message.userAccount)) {
            mUserAccount = message.userAccount
        }
    }

    @SuppressLint("LongLogTag")
    override fun onNotificationMessageClicked(context: Context?, message: MiPushMessage) {
//        mMessage = message.content
//        if (!TextUtils.isEmpty(message.topic)) {
//            mTopic = message.topic
//        } else if (!TextUtils.isEmpty(message.alias)) {
//            mAlias = message.alias
//        } else if (!TextUtils.isEmpty(message.userAccount)) {
//            mUserAccount = message.userAccount
//        }


        Log.i(TAG, "onNotificationMessageClicked: $message")
        if (!TextUtils.isEmpty(message.title)) {


            //点击通知 拉起应用首页，此处能解决我遇到的问题

            //点击通知 拉起应用首页，此处能解决我遇到的问题
            try {
                val componentName =
                    ComponentName(context!!.packageName, "com.rabbit.seat.rabbit_seat_app.MainActivity")
                val intent = Intent()
                //新开一个任务栈，这样当应用处于前台，再次打开MainActivity会走 NewIntent 方法
                //当应用处于杀死状态，会走onCreate方法
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                intent.component = componentName
                if (message.title != null) {
                    intent.putExtra(MainActivity.extras, message.title) //存入参数
                }
                context!!.startActivity(intent)
            } catch (e: Exception) {
                Log.i(TAG, "=============Exception:$e")
            }

            // TODO
            Log.d(TAG, "title: $message.title")
//            val i: Intent = Intent(
//                context,
//                MainActivity::class.java
//            )
//            i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//            if (context != null) {
//                context.startActivity(i)
//                if (message.title != null) {
//                    Handler(Looper.getMainLooper()).postDelayed(
//                        Runnable {
//                            if (MainActivity.eventSink != null) {
//                                MainActivity.eventSink!!.success(message.title)
//                            }
//                        }, 500
//                    )
////                    i.putExtra("extras",message.title);//存入参数
//                }
//                // FlutterEventChannel.getInstance().sendEventData(message.title)
//            }


        }
    }

    override fun onNotificationMessageArrived(context: Context?, message: MiPushMessage) {
        mMessage = message.content
        if (!TextUtils.isEmpty(message.topic)) {
            mTopic = message.topic
        } else if (!TextUtils.isEmpty(message.alias)) {
            mAlias = message.alias
        } else if (!TextUtils.isEmpty(message.userAccount)) {
            mUserAccount = message.userAccount
        }
    }

    val PUSH_PROVIDER_MI = "mi"

    @SuppressLint("LongLogTag")
    override fun onCommandResult(context: Context?, message: MiPushCommandMessage) {
        L.i(TAG, "onCommandResult: $message")
        val command = message.command
        val arguments = message.commandArguments
        val cmdArg1 = if (arguments != null && arguments.size > 0) arguments[0] else null
        if (MiPushClient.COMMAND_REGISTER == command) {
            if (message.resultCode == 0L) {
                mRegId = cmdArg1
                TuyaAccount.mRegId = mRegId
                Log.i(TAG, "mi push regid: $mRegId")
                if (!TextUtils.isEmpty(mRegId)) {
                    // tuya old register
                } else {
                    Log.e(TAG, "mRegId is empty!")
                }
            }
        } else {
            // TODO
        }
    }


//    override fun onCommandResult(context: Context?, message: MiPushCommandMessage) {
//        val command = message.command
//        val arguments = message.commandArguments
//        val cmdArg1 = if (arguments != null && arguments.size > 0) arguments[0] else null
//        val cmdArg2 = if (arguments != null && arguments.size > 1) arguments[1] else null
//        if (MiPushClient.COMMAND_REGISTER == command) {
//            if (message.resultCode == 0L) {
//                mRegId = cmdArg1
//            }
//        } else if (MiPushClient.COMMAND_SET_ALIAS == command) {
//            if (message.resultCode == 0L) {
//                mAlias = cmdArg1
//            }
//        } else if (MiPushClient.COMMAND_UNSET_ALIAS == command) {
//            if (message.resultCode == 0L) {
//                mAlias = cmdArg1
//            }
//        } else if (MiPushClient.COMMAND_SUBSCRIBE_TOPIC == command) {
//            if (message.resultCode == 0L) {
//                mTopic = cmdArg1
//            }
//        } else if (MiPushClient.COMMAND_UNSUBSCRIBE_TOPIC == command) {
//            if (message.resultCode == 0L) {
//                mTopic = cmdArg1
//            }
//        } else if (MiPushClient.COMMAND_SET_ACCEPT_TIME == command) {
//            if (message.resultCode == 0L) {
//                mStartTime = cmdArg1
//                mEndTime = cmdArg2
//            }
//        }
//    }

    override fun onReceiveRegisterResult(context: Context?, message: MiPushCommandMessage) {
        val command = message.command
        val arguments = message.commandArguments
        val cmdArg1 = if (arguments != null && arguments.size > 0) arguments[0] else null
        val cmdArg2 = if (arguments != null && arguments.size > 1) arguments[1] else null
        if (MiPushClient.COMMAND_REGISTER == command) {
            if (message.resultCode == 0L) {
                mRegId = cmdArg1
            }
        }
    }
}