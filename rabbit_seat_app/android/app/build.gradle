def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"


android {

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
        abortOnError false
    }

    signingConfigs {
        release {
            storeFile file('./keys.jks')
            storePassword '939436'
            keyAlias 'key0'
            keyPassword '939436'
        }
        debug {
            storeFile file('./keys.jks')
            storePassword '939436'
            keyAlias 'key0'
            keyPassword '939436'
        }
    }

    compileSdk 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.rabbit.seat.rabbit_seat_app"
        // minSdkVersion flutter.minSdkVersion
        // applicationId "com.rocky.rabbit.seat"
        minSdkVersion 24
        //noinspection ExpiringTargetSdkVersion
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        ndk {
            //设置支持的SO库架构（开发者可以根据需要，选择一个或多个平台的so）
            abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
        }
    }

    packagingOptions {
        pickFirst 'lib/*/libc++_shared.so'  // 多个aar存在此so，需要选择第一个
    }

    buildTypes {
        release {
            shrinkResources false
            minifyEnabled true

            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
        }
    }

    ndkVersion '25.2.9519653'
    buildToolsVersion '34.0.0'
}

flutter {
    source '../..'
}
configurations.all {
    exclude group: "com.thingclips.smart" ,module: 'thingsmart-modularCampAnno'
}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation 'com.alibaba:fastjson:1.1.67.android'
    implementation 'com.squareup.okhttp3:okhttp-urlconnection:3.14.9'

//    // App SDK 最新稳定安卓版：
//    implementation 'com.facebook.soloader:soloader:0.10.4+'
//    implementation 'com.thingclips.smart:thingsmart:6.0.0'
    implementation 'com.thingclips.smart:thingsmart:6.4.1'

    implementation 'com.amap.api:3dmap:9.2.0'
//    implementation 'com.amap.api:location:latest.integration'
    implementation 'com.amap.api:search:9.2.0'
//
//    implementation 'mi-appstore:xiaomi-update-sdk:4.0.3'
}
