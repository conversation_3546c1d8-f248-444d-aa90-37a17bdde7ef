name: rabbit_seat_app
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.2.44
# 打包指令  flutter build apk --no-shrink

# flutter 版本3.16.0
environment:
  sdk: '>=3.3.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter
  intl: any
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2

  # 数据模型
  json_annotation: ^4.4.0
  # 数据持久化
  shared_preferences: ^2.0.13

  # 屏幕适配
  flutter_screenutil: ^5.3.1
  # 网络请求
  dio: ^5.3.4
  # 弹窗
  flutter_easyloading: ^3.0.3
  # 日期格式化
  date_format: ^2.0.4
  # html
  flutter_html: ^3.0.0-beta.2
  # 拨号 文件预览 web打开等等
  url_launcher: ^6.2.1
  # 列表刷新
  flutter_easyrefresh: ^2.2.1
  # 状态管理
  provider: ^6.0.2
  # 上拉加载，下拉刷新
  #pull_to_refresh: ^2.0.0
  #
#  event_bus: ^2.0.0

  media_asset_utils: ^0.1.0
  flutter_swiper_tv: ^1.1.6-nullsafety
  flutter_cupertino_datetime_picker: ^3.0.0

  tuya:
    path: ../tuya

  # 图片选择器
  image_picker: ^1.0.4
  # 图片裁剪器
  image_cropper: ^5.0.1
  # 平台权限
  permission_handler: ^11.1.0

  # 图片预览
  photo_view: ^0.14.0

  # # 地图
  # amap_flutter_base: ^3.0.0
  # amap_flutter_location: ^3.0.0
  amap_flutter_search: 
    path: ../amap_flutter_search
  # amap_flutter_map: ^3.0.0

  amap_map:
    path: ../amap_map-main

  # 地址选择器
  city_pickers: ^1.0.1

  # 存储
  path_provider: ^2.0.11
  # 版本信息
  package_info_plus: ^8.0.1
  # 文件操作
  open_file: ^3.2.1
  # 网络状态
  connectivity_plus: ^5.0.2

  # 视频
  video_player: ^2.4.4
  # 部件可见性变化监听
  visibility_detector: ^0.4.0+2

  # 图片加载
  cached_network_image: ^3.3.0

  # 微信插件
  fluwx: ^4.4.3

  #rxdart: ^0.27.4
  rxdart: ^0.26.0
  flutter_bloc: ^8.0.1

#  flutter_gif: ^0.0.4

  gif: ^2.3.0

  # 打开app设置
  app_settings: ^6.1.1

  # 蓝牙权限检测
  flutter_blue: ^0.8.0

  loading_animation_widget: ^1.2.0+4
  flutter_native_splash: ^2.3.10
  mobile_scanner: ^5.2.3
  macos_ui: 2.0.0
  adaptive_dialog: 2.2.0
  event_bus: ^2.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/images/
    - assets/images/baby/
    - assets/images/circle/
    - assets/images/device/
    - assets/images/home/
    - assets/images/map/
    - assets/images/my/
    - assets/images/publish/
    - assets/images/share/
    - assets/images/ticket/
    - assets/images/meal/
    - assets/images/gif/


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
