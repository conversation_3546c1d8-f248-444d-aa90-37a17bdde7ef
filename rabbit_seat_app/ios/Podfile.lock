PODS:
  - AMap3DMap (10.1.200):
    - AMapFoundation (>= 1.8.0)
  - amap_flutter_search (0.0.1):
    - AMapSearch
    - Flutter
  - amap_map (1.0.8):
    - AMap3DMap
    - Flutter
  - AMapFoundation (1.8.2)
  - AMapSearch (9.7.4):
    - AMapFoundation (>= 1.8.0)
  - app_settings (5.1.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - Flutter (1.0.0)
  - flutter_blue (0.0.1):
    - Flutter
    - flutter_blue/Protos (= 0.0.1)
  - flutter_blue/Protos (0.0.1):
    - Flutter
    - Protobuf (~> 3.11.4)
  - flutter_native_splash (2.4.3):
    - Flutter
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.4)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMLKit/BarcodeScanning (6.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 5.0.0)
  - GoogleMLKit/MLKitCore (6.0.0):
    - MLKitCommon (~> 11.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (3.5.0)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.1)
  - image_picker_ios (0.0.1):
    - Flutter
  - media_asset_utils (0.0.1):
    - Flutter
  - MJExtension (3.4.2)
  - MLImage (1.0.0-beta5)
  - MLKitBarcodeScanning (5.0.0):
    - MLKitCommon (~> 11.0)
    - MLKitVision (~> 7.0)
  - MLKitCommon (11.0.0):
    - GoogleDataTransport (< 10.0, >= 9.4.1)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/UserDefaults (< 8.0, >= 7.13.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (7.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta5)
    - MLKitCommon (~> 11.0)
  - mobile_scanner (5.2.3):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 6.0.0)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - open_file_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - Protobuf (3.11.4)
  - ReachabilitySwift (5.2.4)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - ThingBluetooth (5.5.3):
    - ThingSmartUtil (~> 5.13)
  - ThingMachRegister (0.7.0)
  - ThingMbedtls (3.5.2)
  - ThingOpenSSLSDK (1.1.1-w.3)
  - ThingSmartActivatorCoreKit (5.9.0):
    - ThingSmartBaseKit (~> 5.0)
    - ThingSmartDeviceCoreKit (~> 5.0)
    - ThingSmartPairingCoreKit (~> 5.3)
    - ThingSmartSocketChannelKit (~> 5.0)
    - ThingSmartUtil (~> 5.14)
  - ThingSmartActivatorKit (5.60.0):
    - ThingSmartActivatorCoreKit (~> 5.2)
    - ThingSmartDeviceKit (~> 5.0)
    - YYModel
  - ThingSmartBaseBLEKit (1.0.0):
    - ThingSmartUtil (~> 5.12)
  - ThingSmartBaseKit (5.7.0):
    - ThingSmartMQTTChannelKit (~> 5.0)
    - ThingSmartNetworkKit (~> 5.0)
    - ThingSmartUtil (~> 5.0)
    - YYModel
  - ThingSmartBeaconKit (5.3.1):
    - ThingSmartBaseBLEKit (~> 1.0)
    - ThingSmartBaseKit (~> 5.0)
    - ThingSmartBLECoreKit (~> 5.0)
    - ThingSmartDeviceCoreKit (~> 5.14)
    - ThingSmartUtil (~> 5.14)
  - ThingSmartBLECoreKit (5.10.1):
    - ThingBluetooth (< 10.0, ~> 5.5)
    - ThingSmartBaseBLEKit (~> 1.0)
    - ThingSmartUtil (~> 5.16)
    - YYModel
  - ThingSmartBLEKit (5.10.9):
    - ThingBluetooth (< 10.0, ~> 5.5)
    - ThingSmartBaseBLEKit (~> 1.0)
    - ThingSmartBaseKit (~> 5.0)
    - ThingSmartBeaconKit (~> 5.0)
    - ThingSmartBLECoreKit (~> 5.10)
    - ThingSmartDeviceCoreKit (~> 5.15)
    - ThingSmartUtil (~> 5.16)
  - ThingSmartBLEMeshKit (5.11.1):
    - ThingBluetooth (< 10.0, ~> 5.4)
    - ThingSmartActivatorCoreKit (~> 5.0)
    - ThingSmartBaseBLEKit (~> 1.0)
    - ThingSmartBaseKit (~> 5.0)
    - ThingSmartDeviceCoreKit (~> 5.14)
    - ThingSmartUtil (~> 5.14)
  - ThingSmartCryption (5.0.0)
  - ThingSmartDeviceCoreKit (5.16.0):
    - ThingSmartMQTTChannelKit (~> 5.5)
    - ThingSmartNetworkKit (~> 5.0)
    - ThingSmartSocketChannelKit (~> 5.0)
    - ThingSmartUtil (~> 5.15)
    - YYModel
  - ThingSmartDeviceKit (5.10.3):
    - ThingMachRegister
    - ThingSmartBaseKit (~> 5.0)
    - ThingSmartDeviceCoreKit (~> 5.15)
    - ThingSmartMQTTChannelKit (~> 5.5)
    - ThingSmartShareKit (~> 5.0)
    - YYModel
  - ThingSmartFeedbackKit (5.0.1):
    - ThingSmartBaseKit (~> 5.0)
    - YYModel
  - ThingSmartHomeKit (5.8.0):
    - ThingBluetooth (~> 5.0)
    - ThingSmartActivatorCoreKit (~> 5.0)
    - ThingSmartActivatorKit (~> 5.0)
    - ThingSmartBaseKit (~> 5.0)
    - ThingSmartBeaconKit (~> 5.0)
    - ThingSmartBLECoreKit (~> 5.0)
    - ThingSmartBLEKit (~> 5.0)
    - ThingSmartBLEMeshKit (~> 5.0)
    - ThingSmartDeviceCoreKit (~> 5.0)
    - ThingSmartDeviceKit (~> 5.0)
    - ThingSmartFeedbackKit (~> 5.0)
    - ThingSmartMessageKit (~> 5.0)
    - ThingSmartMQTTChannelKit (~> 5.0)
    - ThingSmartNetworkKit (~> 5.0)
    - ThingSmartPairingCoreKit (~> 5.0)
    - ThingSmartSceneKit (~> 5.0)
    - ThingSmartShareKit (~> 5.0)
    - ThingSmartSocketChannelKit (~> 5.0)
    - ThingSmartTimerKit (~> 5.0)
  - ThingSmartMessageKit (5.6.0):
    - ThingSmartBaseKit
    - YYModel
  - ThingSmartMQTTChannelKit (5.6.0):
    - ThingSmartUtil (>= 5.0.0)
  - ThingSmartNetworkKit (5.7.8.1):
    - ThingSmartUtil (>= 5.8)
    - YYModel
  - ThingSmartPairingCoreKit (5.6.0):
    - ThingSmartMQTTChannelKit (>= 5.0.0)
    - ThingSmartSocketChannelKit (>= 5.0.0)
    - ThingSmartUtil (>= 5.16.0)
    - YYModel
  - ThingSmartSceneCoreKit (5.14.0):
    - ThingSmartBaseKit
    - ThingSmartDeviceCoreKit
    - YYModel
  - ThingSmartSceneKit (5.1.0):
    - ThingSmartBaseKit
    - ThingSmartDeviceKit
    - ThingSmartSceneCoreKit
  - ThingSmartShareKit (5.3.0):
    - ThingSmartBaseKit (~> 5.0)
    - ThingSmartDeviceCoreKit (~> 5.0)
  - ThingSmartSocketChannelKit (5.0.11):
    - ThingSmartUtil (>= 5.0.0)
  - ThingSmartTimerKit (5.0.1):
    - ThingSmartBaseKit (~> 5.0)
    - ThingSmartDeviceCoreKit (~> 5.0)
    - YYModel
  - ThingSmartUtil (5.16.0):
    - ThingMbedtls (= 3.5.2)
    - ThingOpenSSLSDK (~> 1.1.1-t.0)
    - ThingSmartCryption (~> 5.0)
  - TOCropViewController (2.6.1)
  - tuya (0.0.1):
    - Flutter
    - MJExtension
    - ThingSmartHomeKit
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - WechatOpenSDK-XCFramework (2.0.4)
  - YYModel (1.0.4)

DEPENDENCIES:
  - amap_flutter_search (from `.symlinks/plugins/amap_flutter_search/ios`)
  - amap_map (from `.symlinks/plugins/amap_map/ios`)
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_blue (from `.symlinks/plugins/flutter_blue/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - media_asset_utils (from `.symlinks/plugins/media_asset_utils/ios`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - ThingSmartCryption (from `./`)
  - ThingSmartHomeKit (~> 5.8.0)
  - tuya (from `.symlinks/plugins/tuya/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)

SPEC REPOS:
  https://github.com/tuya/tuya-pod-specs.git:
    - ThingBluetooth
    - ThingMachRegister
    - ThingMbedtls
    - ThingOpenSSLSDK
    - ThingSmartActivatorCoreKit
    - ThingSmartActivatorKit
    - ThingSmartBaseBLEKit
    - ThingSmartBaseKit
    - ThingSmartBeaconKit
    - ThingSmartBLECoreKit
    - ThingSmartBLEKit
    - ThingSmartBLEMeshKit
    - ThingSmartDeviceCoreKit
    - ThingSmartDeviceKit
    - ThingSmartFeedbackKit
    - ThingSmartHomeKit
    - ThingSmartMessageKit
    - ThingSmartMQTTChannelKit
    - ThingSmartNetworkKit
    - ThingSmartPairingCoreKit
    - ThingSmartSceneCoreKit
    - ThingSmartSceneKit
    - ThingSmartShareKit
    - ThingSmartSocketChannelKit
    - ThingSmartTimerKit
    - ThingSmartUtil
  trunk:
    - AMap3DMap
    - AMapFoundation
    - AMapSearch
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMSessionFetcher
    - MJExtension
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - PromisesObjC
    - Protobuf
    - ReachabilitySwift
    - TOCropViewController
    - WechatOpenSDK-XCFramework
    - YYModel

EXTERNAL SOURCES:
  amap_flutter_search:
    :path: ".symlinks/plugins/amap_flutter_search/ios"
  amap_map:
    :path: ".symlinks/plugins/amap_map/ios"
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  Flutter:
    :path: Flutter
  flutter_blue:
    :path: ".symlinks/plugins/flutter_blue/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  media_asset_utils:
    :path: ".symlinks/plugins/media_asset_utils/ios"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  ThingSmartCryption:
    :path: "./"
  tuya:
    :path: ".symlinks/plugins/tuya/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"

SPEC CHECKSUMS:
  AMap3DMap: 06a11a83072857d6076c14060b2e1a676182e84d
  amap_flutter_search: 3130c77f000d88acb1feef0ad8689290c9a3476f
  amap_map: a681a1441e8b707efafde356c3cd388148495524
  AMapFoundation: 9885c48fc3a78fdfb84a0299a2293e56ea3c9fec
  AMapSearch: a30e89212f55a867ac951ce71f800313cd54ad0a
  app_settings: 5127ae0678de1dcc19f2293271c51d37c89428b2
  connectivity_plus: 481668c94744c30c53b8895afb39159d1e619bdf
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_blue: 98e9bec352ab6584a882536c31c7610f7102b3f0
  flutter_native_splash: c32d145d68aeda5502d5f543ee38c192065986cf
  fluwx: 6bf9c5a3a99ad31b0de137dd92370a0d10a60f4b
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMLKit: 97ac7af399057e99182ee8edfa8249e3226a4065
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_cropper: 655b3ba703c9e15e3111e79151624d6154288774
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  media_asset_utils: 09a811a042fd87c0d464ab48fb5379a7c185f9d6
  MJExtension: e97d164cb411aa9795cf576093a1fa208b4a8dd8
  MLImage: 1824212150da33ef225fbd3dc49f184cf611046c
  MLKitBarcodeScanning: 10ca0845a6d15f2f6e911f682a1998b68b973e8b
  MLKitCommon: afec63980417d29ffbb4790529a1b0a2291699e1
  MLKitVision: e858c5f125ecc288e4a31127928301eaba9ae0c1
  mobile_scanner: 92e8812bf22a8f84131e2a7f9d0f44dad1a4742b
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  open_file_ios: 5ff7526df64e4394b4fe207636b67a95e83078bb
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  Protobuf: 176220c526ad8bd09ab1fb40a978eac3fef665f7
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  ThingBluetooth: ae45c72c9c01a7bfec11d692717413ba8137fa57
  ThingMachRegister: f21dfb29d627aa03f25d802afaa9958fc81ee0ad
  ThingMbedtls: 52721dc4ab458c5bcd4e2d679fc08d1bb716f3a0
  ThingOpenSSLSDK: 92b83659f3b2ddff973d412a43d795a5efd4170a
  ThingSmartActivatorCoreKit: 4420040a0f93df3eb3af933249ff294c5c9fd874
  ThingSmartActivatorKit: 768337b90a735364f1920408afb4b5b833e96390
  ThingSmartBaseBLEKit: ce4744d0ef3f4327b8c6c1f101a5836b804b7484
  ThingSmartBaseKit: b87a3313a4bc9b8742c7606867244511c7f9d1a9
  ThingSmartBeaconKit: e463a3719e90093ab31d93019229ffeb41631b68
  ThingSmartBLECoreKit: 2e41c0948b45d4de8df1579bd5e4e109449192f3
  ThingSmartBLEKit: 86bce0ba265d0c1000a96ac7167b22f17124930a
  ThingSmartBLEMeshKit: 574ad632de1a9e628687f8a2db76633b25aa1259
  ThingSmartCryption: ffdb949c0ffa29c07d2c0e0e79bd27c29d59412d
  ThingSmartDeviceCoreKit: 21c56bdbbebfbe1754af524dba6979a05b2a2197
  ThingSmartDeviceKit: 66b7d4d9081c2072ee8ed20802d05c72e3912eeb
  ThingSmartFeedbackKit: 7eed8f5af3f3cd13a9fd1df626b8ac3933123401
  ThingSmartHomeKit: c19a171220081cf1e248e47da1941d152a161593
  ThingSmartMessageKit: 1b4fd6b47d30550e8e236ada77089470c2688441
  ThingSmartMQTTChannelKit: 8401b88a58e913bb7b76c499318c9fb850c39b64
  ThingSmartNetworkKit: 6c8882c7c848279b56402987b3c9ee3ff4656001
  ThingSmartPairingCoreKit: dcd3d1108714531480f068b7f26c7c45a87f31b7
  ThingSmartSceneCoreKit: 6900a3c58cb5a0b6531e2e4e37ffefa46e13da4f
  ThingSmartSceneKit: 31532a825959126744bb6ae1d936dadeaeb764d2
  ThingSmartShareKit: 567c68b9c39059a8162caf2f6f8163115260b27f
  ThingSmartSocketChannelKit: 3251b177c2cb6bb4ec51754ea7e21f2c302ab0da
  ThingSmartTimerKit: b1781788fbadea84cd293b9c5388d80b45f9f5f3
  ThingSmartUtil: b0f136834d5a185cf7717a1feddfa1118193296a
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  tuya: f13a36526fa89497fb4ce94bbc492614a879646f
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  WechatOpenSDK-XCFramework: 36fb2bea0754266c17184adf4963d7e6ff98b69f
  YYModel: 2a7fdd96aaa4b86a824e26d0c517de8928c04b30

PODFILE CHECKSUM: 4557e33d79e01a5e276d10525d7dd24fd8902cae

COCOAPODS: 1.16.2
