<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/ThingSmartCryption-iOS-umbrella.h</key>
		<data>
		1FBTmChOKGVPSJr32b8OqUiG8jk=
		</data>
		<key>Headers/error_code.h</key>
		<data>
		e0l9pwTOC0iREgFq6DZdBS+Dxd0=
		</data>
		<key>Info.plist</key>
		<data>
		rA89l7O+X+eHWznB9LC9cYxfenI=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		wWV2/hBCRdg5lSZpvq4gZMz3ioY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/ThingSmartCryption-iOS-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			1FBTmChOKGVPSJr32b8OqUiG8jk=
			</data>
			<key>hash2</key>
			<data>
			d7YUE+Ldj9xgTDL2FuSemATB/gAU3p4NASp0Sy9fB8M=
			</data>
		</dict>
		<key>Headers/error_code.h</key>
		<dict>
			<key>hash</key>
			<data>
			e0l9pwTOC0iREgFq6DZdBS+Dxd0=
			</data>
			<key>hash2</key>
			<data>
			/gIxR1GRHCCn3jptLNHDi5e3mKFT95Ky6MS/S/xNDPc=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			wWV2/hBCRdg5lSZpvq4gZMz3ioY=
			</data>
			<key>hash2</key>
			<data>
			eiS5EYf6sCG3XJGkPRqmwNl5537rOCAuMca8ue1Aw5Q=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
