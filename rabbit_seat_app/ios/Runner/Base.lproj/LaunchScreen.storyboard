<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_72" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="Ydg-fD-yQy"/>
                        <viewControllerLayoutGuide type="bottom" id="xbc-2k-c8Z"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="430" height="932"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="rabbit_launch_bg" translatesAutoresizingMaskIntoConstraints="NO" id="kZk-o4-ruA">
                                <rect key="frame" x="0.0" y="0.0" width="430" height="932"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" misplaced="YES" image="rabbit_launch_logo" translatesAutoresizingMaskIntoConstraints="NO" id="kZk-o4-ruz">
                                <rect key="frame" x="145" y="306" width="125" height="125"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="kZk-o4-ruA" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="Kg7-HN-baC"/>
                            <constraint firstItem="kZk-o4-ruz" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="SvQ-7j-1s2"/>
                            <constraint firstItem="kZk-o4-ruA" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="gqO-ZA-zYx"/>
                            <constraint firstAttribute="trailing" secondItem="kZk-o4-ruA" secondAttribute="trailing" id="lCM-OD-Z9O"/>
                            <constraint firstItem="kZk-o4-ruz" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="xg3-1A-3Zz"/>
                            <constraint firstAttribute="bottom" secondItem="kZk-o4-ruA" secondAttribute="bottom" id="yrk-5U-hhT"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="76.811594202898561" y="251.11607142857142"/>
        </scene>
    </scenes>
    <resources>
        <image name="rabbit_launch_bg" width="1125" height="2463"/>
        <image name="rabbit_launch_logo" width="152" height="152"/>
    </resources>
</document>
