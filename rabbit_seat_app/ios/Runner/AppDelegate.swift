import UIKit
import Flutter

@main
@objc class AppDelegate: FlutterAppDelegate, FlutterStreamHandler {
    
    var fluMethodChannel: FlutterMethodChannel?
    var eventChannel: FlutterEventChannel?
    var fluEventSink: FlutterEventSink?
    var _deviceToken: Data?
    
    //这个onListen是Flutter端开始监听这个channel时的回调，第二个参数 EventSink是用来传数据的载体
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        self.fluEventSink = events
        return nil
    }
    
    /// flutter不再接收
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        return nil
    }
    
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
      GeneratedPluginRegistrant.register(with: self)
      
      ThingSmartSDK.sharedInstance().start(withAppKey: "scre7m57hxkv34qx9w5v", secretKey: "9r4wqhe8jpe88j7w8w88j8xawf3mymns")
      
      
      let flutterViewController = window.rootViewController as! FlutterViewController
      
      eventChannel = FlutterEventChannel.init(name: "EventChannel", binaryMessenger: flutterViewController as! FlutterBinaryMessenger)
      eventChannel?.setStreamHandler(self)
      
      
//            // 延迟执行
//            DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 5) {
//                print("延迟执行3")
//                self.fluEventSink?("离车报警")
//            }
//
//      // 延迟执行
//      DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 9) {
//          print("延迟执行3")
//          self.fluEventSink?("您有一条新消息")
//      }
      
      // 推送配置
      application.registerForRemoteNotifications()
      application.registerUserNotificationSettings(UIUserNotificationSettings(
           types: [.badge, .sound, .alert], categories: nil))

      // iOS 10.0之后的版本
      if #available(iOS 10.0, *) {
          let center = UNUserNotificationCenter.current()
          center.delegate = self
          center.requestAuthorization(options: [.badge, .alert, .sound]) { granted, error in
              if (granted) {
                  //点击允许
                  print("点击允许");
              } else {
                  //点击不允许
                  print("点击不允许")
              }
          }
      }
      

      fluMethodChannel = FlutterMethodChannel.init(name: "Home_View", binaryMessenger: flutterViewController as! FlutterBinaryMessenger)
      fluMethodChannel?.setMethodCallHandler { call, result in
          if call.method == "initTuyaDeviceToken" {
              //
              print(">>>>> iOS Init Tuya DeviceToken")
              
              if self._deviceToken != nil {
                  ThingSmartSDK.sharedInstance().deviceToken = self._deviceToken!
                  
              }
            
              
          }
      }
      
      // 开启消息推送
      ThingSmartSDK.sharedInstance().setPushStatusWithStatus(true) {
          print(">>>> 开启消息推送成功")
      } failure: { error in
          print(">>>> 开启消息推送失败")
      }
      
      // 设置告警
      ThingSmartSDK.sharedInstance().setDevicePushStatusWithStauts(true) {
          print(">>>> 开启警告消息推送成功")
      } failure: { error in
          print(">>>> 开启警告消息推送失败")
      }
      
      // 设置通知
      ThingSmartSDK.sharedInstance().setNoticePushStatusWithStauts(true) {
          print(">>>> 开启通知消息推送成功")
      } failure: { error in
          print(">>>> 开启通知消息推送失败")
      }
      

//      ThingSmartMessage().getList { list in
//          print(">>> ThingSmartMessage().getList  list:", list)
//          // ThingSmartMessageListModel
//          list.forEach { model in
//              print(">>> model:", model.tysdk_toString())
//          }
//      } failure: { error in
//          print(">>> ThingSmartMessage().getList  error:", error)
//      }


        
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
    
    
    /// 应用程序是否进入后台
    override func applicationDidEnterBackground(_ application: UIApplication) {

        // 清除消息推送
//        UIApplication.shared.cancelAllLocalNotifications()
//        let badge = (UIApplication.shared.scheduledLocalNotifications?.count ?? 0) + 1
//        let notification = UILocalNotification()
//        notification.applicationIconBadgeNumber = badge
//        application.presentLocalNotificationNow(notification)
    }
    
    
    /// 应用程序激活了(进入前台)
    override func applicationDidBecomeActive(_ application: UIApplication) {
        // 清除全部推送消息
        application.cancelAllLocalNotifications()
        // 请求角标
        application.applicationIconBadgeNumber = 0
    }

    
    
    // 注册远程通知成功
    // 在 didRegisterForRemoteNotificationsWithDeviceToken 中，注册 pushId 到智能生活 App SDK。
    override func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        
        
        let tokenData = NSData.init(data: deviceToken)
        let tokenString = tokenData.description.replacingOccurrences(of: "<", with: "").replacingOccurrences(of: ">", with: "").replacingOccurrences(of: " ", with: "")
        
        
//        ThingSmartSDK.sharedInstance().deviceToken = deviceToken
        _deviceToken = deviceToken
        
        print("============================================================")
        print("application did register for remote notifications with deviceToken:", deviceToken)
        print("token:", tokenString);
        print("============================================================")
        
//        UMessage.registerDeviceToken(deviceToken)
    }
    
    // 注册远程通知失败
    override func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("============================================================")
        print("did Fail To Register For Remote Notifications With Error:", error)
        print("============================================================")
    }
    
    //iOS10以下使用这两个方法接收通知
    override func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        print("============================================================")
        print("application did receive remote notification:", userInfo)
        print("============================================================")
        

//        self.fluEventSink?(dictionaryToJSONString(userInfo))
        
        let  data2 :Data! = try? JSONSerialization.data(withJSONObject: userInfo, options: [])
        let  JSONString = String(data: data2, encoding: String.Encoding(rawValue: String.Encoding.utf8.rawValue))
        print("jsonString:", JSONString ?? "")
        
        /*
         {
             "msgClassification":"device_notification",
             "link":"rockyrabbitseat:\/\/messageCenter?category=0&msgId=cd6e7ada-80a9-4405-a850-e95d9d36f8ae",
             "clientId":"unh4avcat9j4ccuw94wg",
             "pkid":484404978,
             "pushSign":"e962c476b898891deb238edc06f10e75",
             "uid":"ay1653981918539tW2At",
             "appId":1626129041,
             "bizType":565276,
             "pushSerialNum":"S1571066531251503104",
             "aps":{
                 "alert":{
                     "title":"消息提醒",
                     "body":"您有新消息，请查看"
                 },
                 "sound":"default",
                 "mutable-content":1
             },
             "ts":1663406424712,
             "pushRecordId":"cd6e7ada-80a9-4405-a850-e95d9d36f8ae",
             "GOTONE_TRACE_ID":"N\/A"
         }
         */
        

        let _aps: [String : Any]? = userInfo["aps"] as? [String : Any]
        if (_aps != nil) {
            let _alert: [String : Any]? = _aps?["alert"] as? [String : Any]
            if (_alert != nil) {
                let _title: String? = _alert?["title"] as? String
                self.fluEventSink?(_title)
            }
        }
  
        
        
        completionHandler(UIBackgroundFetchResult.newData)
    }
    
    //iOS10新增：处理前台点击通知的代理方法
    @available(iOS 10.0, *)
    override func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        
        print("============================================================")
        print("user notification center will present with completion handler", notification.request.content.userInfo)
        print("============================================================")
        
        
   
        
        // 讲通知数据 转成 data 数据
        let  data2 :Data! = try? JSONSerialization.data(withJSONObject: notification.request.content.userInfo, options: [])
        let  JSONString = String(data: data2, encoding: String.Encoding(rawValue: String.Encoding.utf8.rawValue))
        print("jsonString:", JSONString ?? "")
        
        let userInfo = notification.request.content.userInfo
        if notification.request.trigger is UNPushNotificationTrigger {
            

//            self.fluEventSink?(dictionaryToJSONString(userInfo))
            let _aps: [String : Any]? = userInfo["aps"] as? [String : Any]
            if (_aps != nil) {
                let _alert: [String : Any]? = _aps?["alert"] as? [String : Any]
                if (_alert != nil) {
                    let _title: String? = _alert?["title"] as? String
                    self.fluEventSink?(_title)
                }
            }
//            UMessage.setAutoAlert(false)
            //必须加这句代码
//            UMessage.didReceiveRemoteNotification(userInfo)
        } else {
            //应用处于前台时的本地推送接受
        }
        completionHandler([.sound, .badge, .alert])
    }
    
    //iOS10新增：处理后台点击通知的代理方法
    @available(iOS 10.0, *)
    override func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        
        print("============================================================")
        print("user notification center did receive with completion handler", response.notification.request.content.userInfo)
        print("============================================================")
        
        let userInfo = response.notification.request.content.userInfo
        if response.notification.request.trigger is UNPushNotificationTrigger {
            //必须加这句代码
//            UMessage.didReceiveRemoteNotification(userInfo)

//            self.fluEventSink?(dictionaryToJSONString(userInfo))
            let _aps: [String : Any]? = userInfo["aps"] as? [String : Any]
            if (_aps != nil) {
                let _alert: [String : Any]? = _aps?["alert"] as? [String : Any]
                if (_alert != nil) {
                    let _title: String? = _alert?["title"] as? String
                    self.fluEventSink?(_title)
                }
            }
        } else {
            //应用处于前台时的本地推送接受
        }
    }
    
    //iOS10新增：
    @available(iOS 10.0, *)
    override func userNotificationCenter(_ center: UNUserNotificationCenter, openSettingsFor notification: UNNotification?) {
        print("============================================================")
        print("user notification open settings for")
        print("============================================================")
    
    }
    
    
    // 将推送消息转成JSONString
    func dictionaryToJSONString(_ userInfo: [AnyHashable : Any]) -> String? {
        
        let  _data :Data! = try? JSONSerialization.data(withJSONObject: userInfo, options: [])
        
        let  JSONString = String(data: _data, encoding: String.Encoding(rawValue: String.Encoding.utf8.rawValue))
        
        print("jsonString:", JSONString ?? "")
        
        return JSONString
    }
}
