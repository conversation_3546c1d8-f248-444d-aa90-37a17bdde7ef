<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>两只兔子</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>rabbit_seat_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx21996e7b57190e41</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>wechat</string>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>weixinURLParamsAPI</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>使用媒体资料库来发布文章，若不允许，您将无分享更多内体信息</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>使用蓝牙连接设备，若不允许，您将无法来链接智能设备。</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>使用蓝牙连接设备，若不允许，您将无法来链接智能设备。</string>
	<key>NSCameraUsageDescription</key>
	<string>使用相机拍照/摄像来发布文章，若不允许，您将无分享去向/视频</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>使用定位来标记您发布文章地点，若不允许，您将无法记录发布地点。</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>使用定位来标记您发布文章地点，若不允许，您将无法记录发布地点。</string>
	<key>NSLocationUsageDescription</key>
	<string>使用定位来标记您发布文章地点，若不允许，您将无法记录发布地点。</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>App需要您的同意,访问位置进行定位</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>使用麦克风进行视频录制发布文章，若不允许，您将无分享录制视频</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>使用相册选择图片来发布文章，若不允许，您将无法分享图片。</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>使用相册选择图片来发布文章，若不允许，您将无法分享图片。</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
