<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/tuya/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/tuya/build" />
      <excludeFolder url="file://$MODULE_DIR$/tuya/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/tuya/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/tuya/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/tuya/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/rabbit_seat_app/build" />
      <excludeFolder url="file://$MODULE_DIR$/rabbit_seat_app/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/rabbit_seat_app/.dart_tool" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Dart SDK" level="project" />
    <orderEntry type="library" name="Dart Packages" level="project" />
  </component>
</module>